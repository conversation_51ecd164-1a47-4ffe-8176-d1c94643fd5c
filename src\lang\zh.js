export default {
  route: {
    dashboard: "首页",
    introduction: "简述",
    documentation: "文档",
    guide: "引导页",
    permission: "权限测试页",
    pagePermission: "页面权限",
    directivePermission: "指令权限",
    icons: "图标",
    components: "组件",
    componentIndex: "介绍",
    tinymce: "富文本编辑器",
    markdown: "Markdown",
    jsonEditor: "JSON编辑器",
    dndList: "列表拖拽",
    splitPane: "Splitpane",
    avatarUpload: "头像上传",
    dropzone: "Dropzone",
    sticky: "Sticky",
    countTo: "CountTo",
    componentMixin: "小组件",
    backToTop: "返回顶部",
    dragDialog: "拖拽 Dialog",
    dragSelect: "拖拽 Select",
    dragKanban: "可拖拽看板",
    charts: "图表",
    keyboardChart: "键盘图表",
    lineChart: "折线图",
    mixChart: "混合图表",
    example: "综合实例",
    nested: "路由嵌套",
    menu1: "菜单1",
    "menu1-1": "菜单1-1",
    "menu1-2": "菜单1-2",
    "menu1-2-1": "菜单1-2-1",
    "menu1-2-2": "菜单1-2-2",
    "menu1-3": "菜单1-3",
    menu2: "菜单2",
    Table: "Table",
    dynamicTable: "动态Table",
    dragTable: "拖拽Table",
    inlineEditTable: "Table内编辑",
    complexTable: "综合Table",
    treeTable: "树形表格",
    customTreeTable: "自定义树表",
    tab: "Tab",
    form: "表单",
    createArticle: "创建文章",
    editArticle: "编辑文章",
    articleList: "文章列表",
    errorPages: "错误页面",
    page401: "401",
    page404: "404",
    errorLog: "错误日志",
    excel: "Excel",
    exportExcel: "Export Excel",
    selectExcel: "Export Selected",
    uploadExcel: "Upload Excel",
    zip: "Zip",
    exportZip: "Export Zip",
    theme: "换肤",
    clipboardDemo: "Clipboard",
    i18n: "国际化",
    externalLink: "外链",
    menu: {
      appPanel: '应用面板',
      workbench: '工作台',
      system: '系统管理',
      company: '公司',
      role: '角色',
      roleDataPermission: '角色数据权限',
      user: '用户',
      permission: '权限',
      log: '日志',
      processMethod: '处理方法',
      app: '应用',
      project: '工程',
      backup: '备份',
      serviceState: '服务状态',
      announcement: '公告',
      aliasManagement: '别名管理',
      logPlotChart: '常规测井图',
      oil: '业务对象',
      oilMap: '井地图',
      oilField: '油田',
      oilfieldBlock: '区块',
      oilfieldBlockTabs: '区块详情信息',
      oilFieldTabs: '油田详情信息',
      oilWellTabs: '井详情信息',
      blockGeologicModel: '地质模型',
      oilWell: '井',
      oilWellbore: '井眼',
      oilWellboreTabs: '井眼详情信息',
      wellTrace: '井轨迹',
      toolStringDetail: '仪器串详情',
      logDataFileInfo: '曲线对象',
      logChannelData: '曲线数据',
      logDataChart: '数据分析',
      logChannelEdit: '曲线编辑',
      multiWell: '多井对比',
      otherWeb: 'iFrame',
      processingModule: '模块处理',
      dataPreprocess: '数据预处理',
      resource: '资源管理',
      locale: '资源域',
      wellStandard: '测井标准',
      colorPalettes: '调色板',
      dhTool: '井下仪器',
      dhToolLayout: '井下模块',
      witsServer: 'Witsml服务器',
      logPlotTemplate: '应用模板',
      workflow: '工作流',
      workFlowProcessRun: '工作流执行',
      downtask: 'Witsml任务',
      pyCodeSamples: 'Python代码范例'
    }
  },
  navbar: {
    logOut: "退出登录",
    dashboard: "首页",
    github: "项目地址",
    screenfull: "全屏",
    theme: "换肤",
    size: "布局大小",
    clearCache: "清除缓存",
    changePassword: "修改密码",
    hello: "你好",
  },
  login: {
    title: "系统登录",
    logIn: "登录",
    username: "账号",
    password: "密码",
    any: "随便填",
    thirdparty: "第三方登录",
    thirdpartyTips: "本地不能模拟，请结合自己业务进行模拟！！！",
  },
  documentation: {
    documentation: "文档",
    github: "Github 地址",
  },
  permission: {
    roles: "你的权限",
    switchRoles: "切换权限",
  },
  guide: {
    description:
      "引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于",
    button: "打开引导",
  },
  components: {
    documentation: "文档",
    tinymceTips:
      "富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见",
    dropzoneTips:
      "由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone",
    stickyTips: "当页面滚动到预设的位置会吸附在顶部",
    backToTopTips1: "页面滚动到指定位置会在右下角出现返回顶部按钮",
    backToTopTips2:
      "可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素",
    imageUploadTips:
      "由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。",
  },
  table: {
    dynamicTips1: "固定表头, 按照表头顺序排序",
    dynamicTips2: "不固定表头, 按照点击顺序排序",
    dragTips1: "默认顺序",
    dragTips2: "拖拽后顺序",
    title: "标题",
    importance: "重要性",
    type: "类型",
    remark: "点评",
    search: "搜索",
    add: "添加",
    export: "导出",
    reviewer: "审核人",
    id: "序号",
    date: "时间",
    author: "作者",
    readings: "阅读数",
    status: "状态",
    actions: "操作",
    edit: "编辑",
    publish: "发布",
    draft: "草稿",
    delete: "删除",
    cancel: "取 消",
    confirm: "确 定",
  },
  errorLog: {
    tips: "请点击右上角bug小图标",
    description:
      "现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。",
    documentation: "文档介绍",
  },
  excel: {
    export: "导出",
    selectedExport: "导出已选择项",
    placeholder: "请输入文件名(默认excel-list)",
  },
  zip: {
    export: "导出",
    placeholder: "请输入文件名(默认file)",
  },
  theme: {
    change: "换肤",
    documentation: "换肤文档",
    tips:
      "Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。",
  },
  tagsView: {
    refresh: "刷新",
    close: "关闭",
    closeOthers: "关闭其它",
    closeAll: "关闭所有",
  },
  common: {
    confirmDeletion: "确定要删除吗",
    confirmTitle: "提示",
    failed: "失败",
    success: "成功",
  },
  appPanel: {
    title: "i-Smart GS 应用桌面",
    subtitle: "一站式管理中枢，系统桌面赋能业务决策，解锁智能管理新体验",
    myApps: "我的应用",
    searchPlaceholder: "搜索应用...",
    allCategories: "全部分类",
    recentProjects: "最近工程",
    allProjects: "全部工程",
    noProjects: "无",
    openProject: "打开工程",
    createProject: "创建新工程",
    projectName: "工程名称",
    wellbore: "井眼",
    lastEditor: "最近编辑",
    editDate: "日期",
    remark: "备注",
    operation: "操作",
    deleteProject: "删除工程",
    saveChanges: "保存修改",
    deleteMultiple: "删除多个",
    confirmDelete: "是否确认删除选中工程？",
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    deleteCancel: "已取消删除",
    selectProjectsToDelete: "请选择需要删除的工程",
    noChanges: "暂无修改",
    searchProjects: "请输入查询关键字...",
    loading: "加载中...",
    noAppsInCategory: "暂无应用",
    categoryName: "分类名称",
    appName: "应用名称",
    appDescription: "应用描述",
    enterApp: "进入应用",
    viewDetails: "查看详情",
    noSearchResults: "没有找到匹配的应用",
    searchResults: "搜索结果",
    clearSearch: "清空搜索",
    totalApps: "共 {count} 个应用",
    appInfo: "应用信息",
    categoryInfo: "分类信息",
    refreshData: "刷新数据",
    appNames: {
      preprocessing: '预处理',
      visualization3d: '三维可视化',
      processingModule: '处理模块',
      geosteering: '地质导向',
      fastLogPlot: '快速测井图',
      multiWellCorrelation: '多井对比',
      pythonProcessing: 'python处理模块',
      dataPreprocessing: '数据预处理',
      pythonEditor: 'PythonApp'
    },
  },
  button: {
    add: "新增",
          confirm: "确定",
      cancel: "取消",
      clickToSave: "点击按钮保存修改",
      confirmDelete: "确定删除选中数据吗？",
      confirmSave: "确定保存修改吗？",
      deleteBatch: "删除多个",
    edit: "编辑",
    delete: "删除",
    enable: "启用",
    disable: "停用",
    create: "创建",
    failed: "失败",
    success: "成功",
  },
  processMethod: {
    method: "方法",
    methodName: "方法名",
    serial: "序号",
    category: "所属分类",
    icon: "图标",
    serviceUrl: "服务地址",
    version: "版本号",
    lastModified: "最后修改时间",
    createTime: "创建时间",
    status: "状态",
    remark: "备注",
    inputMethodName: "请输入方法名称",
    selectCategory: "请选择分类",
    parameterConfig: "参数配置",
    parameterName: "参数名",
    parameterTitle: "参数标题",
    dataGroup: "分组",
    dataType: "数据类型",
    defValue: "默认值",
    zoned: "是否分层",
    unit: "单位",
    unitClass: "单位类别",
    desc: "描述",
    min: "最小值",
    max: "最大值",
    optional: "是否可选",
    inputCurveConfig: "输入曲线配置",
    curveName: "曲线名称",
    curveTitle: "曲线标题",
    outputCurveConfig: "输出曲线配置",
    curveType: "曲线类型",
    inputCurveTab: "输入曲线",
    parameterTab: "参数设置",
    outputCurveTab: "输出曲线",
    settingsTab: "设置",
    dataset: "数据集：",
    selectDataset: "请选择数据集",
    nameColumn: "名称",
    valueColumn: "值",
    unitColumn: "单位",
    searchParameters: "搜索参数",
    search: "搜索",
    depthRange: "深度范围：",
    samplingInterval: "采样间隔：",
    reset: "重置",
    titleColumn:"标题"
  },
  other: {
    selectImage: "选择图片",
    moreIcon: "更多图标",
  },
  announcement: {
    title: "公告管理",
    list: "公告列表",
    create: "新增公告",
    edit: "编辑公告",
    delete: "删除公告",
    publish: "发布公告",
    withdraw: "撤回公告",
    detail: "公告详情",
    // 表格字段
    messageId: "消息ID",
    messageType: "消息类型",
    systemMessage: "系统消息",
    publicMessage: "公告消息",
    announcementTitle: "消息标题",
    content: "消息内容",
    senderName: "发送者",
    receiverType: "接收者类型",
    targetUser: "指定用户",
    targetAll: "全体用户",
    priority: "优先级",
    status: "状态",
    targetType: "目标用户",
    targetUsers: "指定用户",
    targetRoles: "指定角色",
    effectiveTime: "生效时间",
    expireTime: "失效时间",
    expiryTime: "失效时间",
    isTop: "是否置顶",
    createTime: "创建时间",
    createdOn: "创建时间",
    sentTime: "发送时间",
    createBy: "创建人",
    updateTime: "更新时间",
    // 优先级选项
    priorityLow: "低",
    priorityNormal: "普通",
    priorityImportant: "重要",
    priorityUrgent: "紧急",
    priorityHigh: "高",
    // 状态选项
    statusDraft: "草稿",
    statusPublished: "已发布",
    // 目标用户类型
    targetAll: "全部用户",
    targetSpecificUsers: "指定用户",
    targetSpecificRoles: "指定角色",
    // 表单标签
    titlePlaceholder: "请输入公告标题（最多15字）",
    contentPlaceholder: "请输入公告内容（最多100字）",
    selectPriority: "请选择优先级",
    selectTargetType: "请选择目标用户",
    selectUsers: "请选择用户",
    selectRoles: "请选择角色",
    selectEffectiveTime: "选择生效时间",
    selectExpireTime: "选择失效时间",
    saveDraft: "保存为草稿",
    publishNow: "立即发布",
    // 操作按钮
    markRead: "标记已读",
    markAllRead: "全部标记为已读",
    viewAll: "查看全部",
    sendMessage: "发送公告",
    send: "发送",
    // 验证信息
    titleRequired: "公告标题必填",
    titleMaxLength: "公告标题最多200字符",
    contentRequired: "公告内容必填",
    priorityRequired: "请选择优先级",
    targetTypeRequired: "请选择目标用户",
    effectiveTimeRequired: "请选择生效时间",
    expireTimeRequired: "请选择失效时间",
    expireTimeCannotBeEarlier: "失效时间不能早于当前时间",
    // 提示信息
    confirmPublish: "确定要发布这条公告吗？",
    confirmWithdraw: "确定要撤回这条公告吗？",
    confirmDelete: "确定要删除这条公告吗？",
    confirmBatchDelete: "确定要删除选中的公告吗？",
    confirmSendMessage: "确定要发送这条公告吗？",
    confirmUpdateMessage: "确定要更新这条公告吗？",
    publishSuccess: "发布成功",
    withdrawSuccess: "撤回成功",
    sendSuccess: "发送成功",
    sendFailed: "发送失败",
    updateSuccess: "更新成功",
    updateFailed: "更新失败",
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    batchDeleteSuccess: "批量删除成功",
    batchDeleteFailed: "批量删除失败",
    selectDeleteRows: "请选择要删除的记录",
    markReadSuccess: "标记成功",
    markAllReadSuccess: "全部标记为已读",
    operationFailed: "操作失败",
    loadFailed: "加载公告失败",
    // 通知相关
    notificationTitle: "公告通知",
    noAnnouncement: "暂无公告",
    unreadCount: "条未读",
    timeAgo: {
      minutesAgo: "分钟前",
      hoursAgo: "小时前",
      daysAgo: "天前"
    },
    // 其他
    yes: "是",
    no: "否",
    all: "全部",
    requireConfirmation: "需要确认",
          downloadReport: "下载报表",
      downloadCount: "下载条数",
      config: "配置",
    downloadCountPlaceholder: "下载条数",
    // 演示相关
         demo: {
       features: "功能特性",
       realTimeNotification: "实时通知",
       realTimeNotificationDesc: "支持实时公告推送，未读消息提醒，确保重要信息及时传达",
       darkMode: "深色主题",
       darkModeDesc: "完美适配深色和浅色主题，提供舒适的视觉体验",
       i18n: "国际化支持",
       i18nDesc: "支持中英文切换，界面语言跟随系统设置自动调整",
       management: "全功能管理",
       managementDesc: "提供完整的公告创建、编辑、发布、撤回等管理功能",
       goToManagement: "进入公告管理",
       simulateNotification: "模拟新公告",
       simulationMessage: "这是一个模拟的公告通知演示"
      },
    titleLength: '标题不能超过15个字',
    contentLength: '内容不能超过100个字',
   },
   common: {
     confirmDeletion: "确定要删除吗",
     confirmTitle: "提示",
     failed: "失败",
     success: "成功",
     demo: "演示",
     lightMode: "浅色",
     darkMode: "深色",
     languageChanged: "语言切换成功",
     themeChanged: "主题切换成功"
   },
  workbench: {
    welcome: '欢迎回来',
    passwordExpire: '密码将于{days}天后过期',
    more: '更多',
    totalWell: '总井次',
    yearWell: '年新增井次',
    monthWell: '月新增井次',
    wellboreList: '井眼列表',
    projectList: '最近打开工程',
    appList: '应用列表',
    messageList: '消息列表',
    system: '系统',
    announcement: '公告',
    systemMsg: '系统',
    wellboreName: '井眼名',
    workingWellbore: '作业井眼',
    fieldName: '所属油田',
    wellName: '所属井',
    startDate: '开始日期',
    area: '区域',
    block: '区块',
    remark: '备注',
    upload: '上传',
    operation: '操作',
    updateTime: '更新日期',
    app: '应用',
    projectName: '工程名',
    noProjects: '暂无工程',
    createProject: '创建新工程',
    refreshProject: '刷新工程列表',
    editor: '编辑人',
    createTime: '创建时间',
    unknown: '未知',
    loading: '加载中...',
    open: '打开',
    detail: '详情',
    noData: '暂无数据',
    uploadSuccess: '上传成功',
    uploadFail: '上传失败',
    openProjectFail: '工程信息不完整，无法打开',
    appDetail: '应用详情',
    myProjects: '我的工程',
    projectCount: '{count}个工程',
    unitCount: '个',
  },
  company: {
    index: '序号',
    action: '操作',
    edit: '修改',
    setArea: '设置区域',
    delete: '删除',
    company: '公司',
    companyName: '名称',
    address: '地址',
    linkMan: '联系人',
    email: '电子邮箱',
    officePhone: '电话',
    cancel: '取消',
    confirm: '确定',
    addArea: '点击添加区域',
    downloadReport: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    companyNameRequired: '公司名称必填',
    emailValid: '请输入正确的邮箱',
  },
  role: {
    index: '序号',
    action: '操作',
    edit: '修改',
    delete: '删除',
    setPagePermission: '设置角色权限',
    setDataPermission: '设置数据权限',
    role: '角色',
    roleName: '角色名称',
    remark: '备注',
    add: '新增',
    download: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    roleNameRequired: '角色名称必填',
    pageName: '页面名称',
    dataPermission: '数据权限',
    all: '全选',
    addData: '新增数据',
    viewSelf: '查看自己的数据',
    editSelf: '编辑自己的数据',
    deleteSelf: '删除自己的数据',
    viewDept: '查看本部门数据',
    editDept: '编辑本部门数据',
    deleteDept: '删除本部门数据',
    viewCompany: '查看本公司数据',
    editCompany: '编辑本公司数据',
    deleteCompany: '删除本公司数据',
    viewPublic: '查看公开数据',
    editPublic: '编辑公开数据',
    deletePublic: '删除公开数据',
    viewAll: '查看所有数据',
    editAll: '编辑所有数据',
    deleteAll: '删除所有数据',
    cancel: '取消',
    confirm: '确定',
    refresh: '刷新',
  },
  user: {
    index: '序号',
    action: '操作',
    edit: '编辑',
    delete: '删除',
    setRole: '设置用户角色',
    lock: '锁定',
    unlock: '解锁',
    company: '公司',
    name: '姓名',
    email: '电子邮箱',
    phone: '电话',
    isLocked: '是否锁定',
    yes: '是',
    no: '否',
    add: '新增',
    download: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    userNameRequired: '姓名必填',
    emailValid: '请输入正确的邮箱',
    passwordRequired: '密码长度最少6位',
    phoneRequired: '电话必填',
    cancel: '取消',
    confirm: '确定',
    refresh: '刷新',
    all: '全选',
    role: '角色',
  },
  log: {
    login: '登录日志',
    loginTable: '登录日志表',
    loginRanking: '登录排行',
    module: '模块浏览日志',
    moduleRanking: '浏览排行',
    data: '数据操作日志',
    dataRanking: '操作排行',
    error: '系统异常日志',
    type: '类型',
    loginUserName: '登录名',
    userName: '姓名',
    cIP: '来源IP',
    dLoginDate: '操作时间',
    cClientInfo: '客户端信息',
    cComputeName: '计算机名',
    operationTime: '操作时间',
    operationDocName: '操作集合',
    result: '操作结果',
    errorTime: '异常产生时间',
    url: '访问路径',
    message: '异常信息',
    browsingTime: '浏览时间',
    moduleName: '模块名称',
  },
  appManage: {
    index: '序号',
    action: '操作',
    edit: '修改',
    delete: '删除',
    appName: '应用名称',
    category: '所属分类',
    icon: '图标',
    order: '排序',
    url: '应用地址',
    deleteUrl: '移除地址',
    isInSite: '内部链接',
    yes: '是',
    no: '否',
    remark: '备注',
    download: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    add: '新增',
    cancel: '取消',
    confirm: '确定',
    appNameRequired: '应用名称必填',
    categoryRequired: '分类必填',
    urlRequired: '应用地址必填',
    isInSiteRequired: '是否为内部链接必填',
    orderRequired: '排序号必填',
  },
  project: {
    index: '序号',
    action: '操作',
    delete: '删除',
    refresh: '刷新',
    download: '下载',
    downloadReport: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    cancel: '取消',
    confirm: '确定',
    projectName: '工程名称',
    appId: '应用名称',
    projectType: '所属分类',
    wellboreId: '所属井眼',
    createTime: '创建时间',
    lastModifyTime: '修改时间',
    remark: '备注',
    inputKeyword: '请输入关键字',
  },
  backupManage: {
    index: '序号',
    action: '操作',
    edit: '编辑',
    delete: '删除',
    refresh: '刷新',
    download: '导出',
    downloadReport: '下载报表',
    downloadCount: '下载条数',
    downloadCountPlaceholder: '下载条数',
    cancel: '取消',
    confirm: '确定',
    close: '关闭',
    createFull: '新增系统全量备份',
    createIncremental: '新增系统增量备份',
    uploadRestore: '上传文件恢复备份',
    deleteBatch: '删除多个',
    searchPlaceholder: '请输入关键字',
    backupName: '备份名称',
    backupType: '类型',
    backupMethod: '方式',
    state: '状态',
    progress: '完成进度(%)',
    description: '备注',
    createTime: '备份时间',
    modify: '修改',
    downloadLog: '下载Log文件',
    downloadDb: '下载数据库',
    restore: '恢复',
    startBackup: '开始备份',
    startRestore: '开始恢复',
    startUpload: '开始恢复',
    setup: '定时设置',
    intervalDays: '备份间隔天数',
    startTime: '定时备份时间',
    startDate: '开始日期',
    enable: '是否启用',
    fileName: '文件名',
    selectFile: '选择文件',
    fileType: '类型',
    system: '系统',
    well: '井',
    notSelected: '未选择文件',
    uploadTip: '请上传.zip文件',
    backupNameRequired: '备份名称必填',
    intervalDaysRequired: '间隔天数必填',
    startTimeRequired: '定时备份时间必填',
    startDateRequired: '开始日期必填',
    fileTypeRequired: '类型必选',
    fileRequired: '文件必上传',
    createFullTitle: '创建系统全量备份',
    createIncrementalTitle: '创建系统增量备份',
    editTitle: '编辑信息',
    restoreFullTitle: '恢复系统全量备份',
    restoreIncrementalTitle: '恢复系统增量备份',
    restoreWellFullTitle: '恢复井全量备份',
    restoreWellIncrementalTitle: '恢复井增量备份',
    unknownType: '未知备份类型',
    restoreTip: '恢复提示',
    restoreStartTip: '开始恢复,请等待恢复完成......',
    restoreSuccess: '恢复成功',
    restoreFailed: '恢复失败',
    backupSuccess: '备份成功',
    backupFailed: '备份失败',
    backupNameExist: '备份名称已存在',
    uploadFailed: '上传失败,请完善上传信息',
    confirmRestore: '恢复将清除掉所有备份后修改的数据,确定要恢复吗？',
    confirmRestoreTitle: '确定恢复',
    select: '选择',
    systemFull: '系统全量',
    systemIncremental: '系统增量',
    tipNoFullBackup: '提示:基于的全量备份不存在',
    tipBasedOnFull: '提示:基于全量备份',
    tipRestoreClear: '提示：恢复将清除掉所有备份后修改的数据，确定要恢复吗？',
    backupListTab: '备份列表',
    restoreLogTab: '恢复日志',
    restoreBackupName: '恢复备份名称',
    operationName: '操作人',
    restoreTime: '恢复时间',
    inputKeyword: "请输入关键字",
    backup: "备份",
    restore: "恢复",
    purpose: "目的",
    wellFluid: "井流体",
    fullBackup: "全量备份",
    incrementalBackup: "增量备份",
    backupName: "备份名称",
    restoreWarning: "警告：恢复操作将覆盖现有数据，请谨慎操作！",
    startRestore: "开始恢复",
    startBackup: "开始备份",
    progress: "进度",
    save: "保存",
    deleteConfirm: "确定要删除吗？",
    fullWidth: "占满窗口宽度",
    proportionalDisplay: "按比例显示",
  },
  resource: {
    // 通用
    common: {
      operation: "操作",
      edit: "修改",
      delete: "删除",
      view: "详情",
      add: "添加",
      refresh: "刷新",
      download: "下载",
      export: "导出",
      import: "导入",
      upload: "上传",
      search: "搜索",
      cancel: "取消",
      confirm: "确定",
      save: "保存",
      reset: "重置",
      back: "返回",
      close: "关闭",
      submit: "提交",
      clear: "清空",
      select: "选择",
      selectAll: "全选",
      batchDelete: "批量删除",
      batchImport: "批量导入",
      batchExport: "批量导出",
      sequence: "序号",
      name: "名称",
      type: "类型",
      category: "类别",
      description: "描述",
      remark: "备注",
      note: "备注",
      createTime: "创建时间",
      updateTime: "更新时间",
      status: "状态",
      enabled: "启用",
      disabled: "禁用",
      pleaseInput: "请输入",
      pleaseSelect: "请选择",
      downloadCount: "下载条数",
      downloadReport: "下载报表"
    },
    // Witsml服务器
    witsServer: {
      title: "Witsml服务器",
      serverName: "服务器名称",
      serverType: "服务器类型",
      address: "地址",
      username: "用户名",
      password: "密码",
      token: "Token",
      serverNameRequired: "服务器名称必填",
      serverTypeRequired: "服务器类型必填",
      addressRequired: "地址必填",
      usernameRequired: "用户名必填",
      passwordRequired: "密码必填",
      tokenRequired: "token必填"
    },
    // Python代码范例
    pyCodeSamples: {
      title: "代码范例",
      sampleType: "代码范例类型",
      sampleName: "代码范例名称", 
      sampleCode: "代码内容",
      sampleTypeRequired: "代码范例类型必填",
      sampleNameRequired: "代码范例名称必填",
      inputName: "请输入名称"
    },
    // 下载任务
    downtask: {
      title: "下载任务",
      server: "服务器",
      serverName: "服务器名称",
      serverWell: "服务器油井",
      serverWellbore: "服务器井眼",
      log: "Log",
      localWell: "本地油井",
      localWellbore: "本地井眼",
      interval: "间隔(秒)",
      caller: "调度者",
      originalWellName: "原井名",
      originalWellboreName: "原井眼名称",
      localWellboreName: "本地井眼",
      dataName: "数据名称",
      downloadType: "下载类型",
      realtimeDownload: "实时下载",
      historicalDownload: "历史下载",
      startTime: "开始时间",
      endTime: "结束时间",
      downloadHistoricalData: "下载历史数据",
      monitorRealtimeData: "监控实时数据",
      wellTrajectoryDownloadSettings: "井轨迹数据下载设置",
      originalStartDepth: "原起始深度",
      originalEndDepth: "原终止深度",
      newStartDepth: "新起始深度",
      newEndDepth: "新终止深度",
      replace: "替换",
      merge: "合并",
      depth: "深度",
      serverRequired: "服务器名称必填",
      serverWellRequired: "服务器油井必填",
      serverWellboreRequired: "服务器井眼必填",
      logRequired: "Log必填",
      localWellRequired: "本地油井必填",
      localWellboreRequired: "本地井眼必填",
      curveName: "曲线名称",
      curveUnit: "曲线单位",
      startIndex: "起始索引",
      endIndex: "终止索引",
      indexSpacing: "索引间距",
      duration: "持续时间",
      dataSize: "数据大小"
    },
    // 资源域
    locale: {
      title: "资源域",
      lithSymbol: "岩性符号",
      logResultSymbol: "解释结论符号",
      icon: "图标",
      nameRequired: "名称必填"
    },
    // 井下仪器
    dhTool: {
      title: "井下仪器",
      manufacturer: "制造商",
      seriesNo: "仪器序列号",
      logo: "Logo",
      length: "长度",
      diameter: "直径",
      weight: "重量",
      maxPressure: "耐压",
      maxTemp: "耐温",
      plotImage: "图像文件",
      plotScaleX: "绘图横向比例",
      plotScaleY: "绘图纵向比例",
      mirror: "镜像",
      turn: "翻转",
      plotHeightMin: "最小绘图高度",
      plotHeightMax: "最大绘图高度",
      plotCenterY: "中心绘图位置",
      plotXoffset: "X绘图偏移量",
      measureOrigin: "测量点偏移量",
      nameRequired: "名称必填",
      inputNameTypeCategory: "请输入名称、类型、类别"
    },
    // 调色板
    colorPalettes: {
      title: "调色板",
      paletteName: "调色板名称",
      color: "颜色",
      setColor: "设置调色板颜色",
      importPalette: "导入调色板",
      uploadXmlFile: "上传xml文件",
      pleaseUploadXml: "请上传.xml文件",
      paletteNameRequired: "调色板名称必填",
      inputPaletteNameOrCategory: "请输入调色板名称或类别",
      pleaseSelectFile: "请选择要上传的文件",
      fileNameTooLong: "文件名长度超出限制，无法上传"
    },
    // 工作流
    workflow: {
      title: "工作流",
      workflowModule: "工作流模块",
      flowChart: "流程图",
      input: "输入",
      output: "输出",
      parameter: "参数",
      addParameter: "添加参数",
      title_: "标题",
      defaultValue: "默认值",
      value: "值",
      visible: "是否可见",
      up: "上移",
      down: "下移",
      execute: "执行",
      generalWorkflow: "一般工作流",
      instrumentWorkflow: "仪器工作流",
      instrument: "仪器",
      nameRequired: "名称必填",
      titleRequired: "标题不能为空",
      defaultValueRequired: "默认值为空",
      typeRequired: "类型不能为空",
      // 模块相关
      module: {
        abbreviation: "缩写",
        appCategory: "应用类别"
      },
      // 流程相关
      process: {
        toolName: "仪器名称",
        dhToolId: "类型"
      }
    },
    // 井标准
    wellStandard: {
      title: "井标准",
      bitStandard: "钻头标准",
      casingStandard: "套管标准",
      bitName: "钻头名称",
      bitSize: "钻头尺寸(英寸)",
      bitNameRequired: "钻头名称必填",
      bitSizeRequired: "钻头尺寸(英寸)不能小于1",
      casingName: "套管名称",
      casingSize: "套管尺寸(英寸)",
      outDiameter: "套管外径(英寸)",
      weight: "套管重量(kg)",
      casingNameRequired: "套管名称必填"
    },
    // 岩性符号
    lithSymbol: {
      title: "岩性符号",
      code: "code",
      chineseName: "中文名称",
      integerValue: "整数值",
      width: "宽度",
      height: "高度",
      icon: "icon",
      svg: "svg",
      selectFile: "选择文件",
      uploadZipFile: "请上传.zip文件 svg和png文件名请用id名称命名",
      uploadXmlFile: "请上传.xml",
      codeRequired: "code必填",
      nameRequired: "名称必填",
      inputKeyword: "请输入关键字"
    },
    // 解释结论符号
    logResultSymbol: {
      title: "解释结论符号",
      code: "code",
      chineseName: "中文名称",
      integerValue: "整数值",
      width: "宽度",
      height: "高度",
      icon: "icon",
      svg: "svg",
      codeRequired: "code必填",
      nameRequired: "名称必填"
    }
  },
  // 石油模块
  oil: {
    // 通用字段
    common: {
      actions: "操作",
      operation: "操作",
      edit: "修改",
      delete: "删除",
      view: "详情",
      add: "添加",
      refresh: "刷新",
      download: "下载",
      export: "导出",
      import: "导入",
      upload: "上传",
      search: "搜索",
      cancel: "取消",
      confirm: "确定",
      save: "保存",
      reset: "重置",
      back: "返回",
      close: "关闭",
      submit: "提交",
      clear: "清空",
      select: "选择",
      selectAll: "全选",
      batchDelete: "批量删除",
      batchImport: "批量导入",
      batchExport: "批量导出",
      sequence: "序号",
      name: "名称",
      type: "类型",
      category: "类别",
      description: "描述",
      remark: "备注",
      note: "备注",
      createTime: "创建时间",
      updateTime: "更新时间",
      inputPerson: "录入人",
      status: "状态",
      enabled: "启用",
      disabled: "禁用",
      success: "成功",
      failed: "失败",
      tip: "提示",
      pleaseInput: "请输入",
      pleaseSelect: "请选择",
      downloadCount: "下载条数",
      downloadReport: "下载报表",
      inputKeyword: "请输入关键字",
      backup: "备份",
      restore: "恢复",
      purpose: "目的",
      wellFluid: "井流体",
      fullBackup: "全量备份",
      incrementalBackup: "增量备份",
      backupName: "备份名称",
      restoreWarning: "警告：恢复操作将覆盖现有数据，请谨慎操作！",
      startRestore: "开始恢复",
      startBackup: "开始备份",
      progress: "进度",
      save: "保存",
      paletteColors: "调色板颜色",
      // 确认对话框
      confirmDelete: "确定要删除吗？",
      confirmDeleteTitle: "确定删除",
      deleteSuccess: "删除成功",
      deleteCancelled: "已取消删除",
      noSelection: "未勾选",
      batchDeleteConfirm: "此操作将删除勾选中的 {count} 条数据, 是否继续?",
      enterCorrectDownloadCount: "输入正确的下载条数",
      deleteConfirm: "确定要删除吗？",
      // textMap 相关
      create: "新增",
      update: "编辑",
      config: "配置",
      fullWidth: "占满窗口宽度",
      proportionalDisplay: "按比例显示",
      // 文件相关
      selectFile: "选择文件",
      fileRequired: "文件上传必选",
      fileNotSelected: "没有选择文件",
      pleaseSelectUploadFile: "请选择要上传的文件",
      pleaseSelectFile: "请选择文件",
      pleaseSelectUnit: "请选择单位",
      pleaseSelectDepthColumn: "请选择斜深列",
      pleaseSelectIncColumn: "请选择井斜列",
      pleaseSelectAziColumn: "请选择方位列",
      pleaseSelectUpDownCurves: "请选择向上和向下曲线",
      selectUnitRequired: "请选择单位",
      selectDepthDataColumn: "请选择深度数据列",
      selectIncDataColumn: "请选择井斜数据列",
      selectAziDataColumn: "请选择方位数据列",
      modelNameRequired: "模型名必填",
      categoryRequired: "类别必填",
      pleaseSelectOrInputCategory: "请选择或输入类别",
      blockNameRequired: "区块名必填",
      oilFieldRequired: "所属油田必填",
      colorPaletteRequired: "调色板必填",
      structuralModelColorPalette: "构造模型调色板",
      blockName: "区块名",
      colorPalette: "调色板",
      structuralColorPalette: "构造图调色板",
      fileUploadRequired: "文件必选",
      downloadFailed: "下载失败",
      block: "区块",
      geologicModel: "地层模型",
      geologicModelTitle: "地质模型",
      modelName: "模型名",
      model: "模型",
      inputBy: "录入人"
    },
    // 油田
    oilField: {
      title: "油田",
      oilFieldName: "油田名",
      oilFieldCode: "油田编号",
      company: "公司",
      area: "区域",
      country: "国家",
      province: "省份",
      city: "城市",
      county: "区县",
      oilFieldNameRequired: "油田名必填",
      areaRequired: "区域必选",
      companyRequired: "公司必选",
      selectCompany: "选择公司",
      selectArea: "选择区域"
    },
    // 油井
    oilWell: {
      title: "井",
      wellName: "井名",
      wellCode: "井编号",
      oilField: "油田",
      block: "区块",
      xCoordinates: "X坐标(m)",
      yCoordinates: "Y坐标(m)",
      longitude: "经度(°)",
      latitude: "纬度(°)",
      kb: "补心海拔(m)",
      wellCategory: "类别",
      wellNameRequired: "井名必填",
      oilFieldRequired: "油田必选",
      blockRequired: "区块必选",
      selectOilField: "选择油田",
      selectBlock: "选择区块",
      town: "镇",
      location: "位置",
      altitude: "海拔高度(m)",
      drillFloorHeight: "钻台高度(m)",
      complCoreHeight: "补心高度(m)",
      magneticDeclination: "磁偏角",
      mudNature: "泥浆性质",
      mudDensity: "泥浆密度",
      mudViscosity: "泥浆粘度",
      mudResistivity: "泥浆电阻率",
      wellDepth: "完井深度",
      inputBy: "录入人",
      startDate: "开钻日期",
      endDate: "完钻日期",
      drillingMF: "钻井测量基准位置",
      rigType: "钻机类型",
      rigName: "钻机名称"
    },
    // 井眼
    oilWellbore: {
      title: "井眼",
      wellboreName: "井眼名称",
      wellboreNumber: "井眼名称",
      belongWell: "所属井",
      platform: "平台",
      wellboreType: "井别",
      shape: "井型",
      purpose: "用途",
      wellboreNameRequired: "井眼名称必填",
      belongWellRequired: "所属井必填",
      inputWellboreName: "请输入井眼名模糊搜索",
      // 井型选项
      straight: "直井",
      horizontal: "水平井",
      directional: "定向井",
      deviated: "斜井",
      // 用途选项
      evaluation: "评估",
      exploration: "勘探",
      development: "开发",
      // 详情页字段
      company: "作业公司",
      toolNo: "仪器编号",
      area: "区域",
      footage: "进尺",
      oilField: "油田",
      drillingRate: "钻遇率(%)",
      well: "井",
      bha: "钻具组合",
      dtf: "钻具功能",
      mdPlan: "设计测深(m)",
      caliper: "井径",
      tvdPlan: "设计垂深(m)",
      actualMD: "实际测深(m)",
      maxTVD: "最大垂深(m)",
      startDate: "开始日期",
      dataSet: "数据集",
      block: "区块",
      // 井轨迹相关
      wellTrajectoryData: "井轨迹数据",
      designTrajectory: "设计轨迹",
      actualTrajectory: "实钻轨迹",
      uploaded: "已上传",
      notUploaded: "未上传",
      upload: "上传",
      view: "查看",
      clear: "清除",
      uploadWellboreModel: "上传井眼模型",
      uploadObjFile: "上传obj文件",
      uploadMtlFile: "上传mtl文件",
      image: "图片",
      pleaseUploadObjFile: "请上传.obj文件",
      pleaseUploadMtlFile: "请上传.mtl文件",
      pleaseUploadImageFile: "请上传.jpg,.png,.jpeg",
      selectTrajectoryFile: "选择轨迹文件",
      pleaseUploadTrajectoryFile: "请上传.las|.csv|.txt文件",
      uploadWellboreTrajectory: "上传井眼轨迹",
      trajectoryData: "轨迹数据",
      replace: "替换",
      merge: "合并",
      unit: "单位",
      md: "斜深",
      incl: "井斜",
      azimuth: "方位",
      kbElevation: "补心海拔",
      modelFile: "模型文件",
      fileType: "文件类型",
      fileName: "文件名",
      noModelFileUploaded: "未上传模型文件。",
      fileNameTooLong: "文件名长度超出限制，无法上传",
      clearWellboreTrajectoryData: "此操作将清空该井眼的{type}轨迹数据, 是否继续?",
      updateTrajectoryUploadStatus: "更新轨迹上传状态",
      design: "设计",
      actual: "实钻",
      confirmDeleteTrajectory: "确认删除",
      deleteCancelled: "已取消删除"
    },
    // 作业
    oilJob: {
      title: "作业",
      jobName: "作业名称",
      jobType: "类别",
      purpose: "作业目的",
      status: "作业状态",
      startTime: "开始时间",
      endTime: "结束时间",
      planEndTime: "计划结束时间",
      startDepth: "起始深度",
      endDepth: "终止深度",
      mdZero: "深度零点位置",
      bitSize: "钻头尺寸",
      maxHoleDev: "最大倾斜",
      maxDevAzim: "最大倾斜方位",
      company: "作业公司",
      equipment: "设备",
      teamLeader: "队长",
      recordedBy: "记录人",
      witnessedBy: "监督",
      operator: "操作",
      toolString: "工具串",
      toolStringConfig: "工具串配置",
      toolStringDetail: "工具串详情",
      editFluid: "编辑流体",
      configToolString: "配置仪器串",
      editFluidData: "编辑流体数据",
      // 流体相关
      fluidType: "流体类型",
      maxTemp: "最高记录温度",
      sourceOfSample: "取样来源",
      salinity: "矿化度",
      density: "密度",
      viscosity: "粘度",
      fluidLoss: "流失量",
      ph: "PH值",
      circStopTime: "循环停止时间",
      logBottomTime: "测井底部时间",
      rm: "泥浆电阻率",
      rmf: "泥浆滤液电阻率",
      rmc: "泥饼电阻率",
      rmAtBht: "井底温度下的泥浆电阻率",
      rmfAtBht: "井底温度下的泥浆滤液电阻率",
      rmcAtBht: "井底温度下的泥饼电阻率",
      bht: "井底温度",
      sourceOfRmf: "Rmf来源",
      sourceOfRmc: "Rmc来源",
      comments: "备注",
      // 验证消息
      jobNameRequired: "作业名称必填",
      startTimeRequired: "开始时间必填",
      startDepthRequired: "起始深度必填",
      endDepthRequired: "终止深度必填",
      // 作业类型选项
      drilling: "钻井",
      logging: "测井", 
      completion: "完井",
      other: "其它",
      // 目的选项
      evaluation: "评估",
      exploration: "勘探",
      development: "开发",
      // 状态选项
      active: "活跃",
      activeInjection: "活跃-注水",
      activeProduction: "活跃-生产",
      completed: "完成",
      partiallyPlugged: "部分堵塞",
      pluggedAndAbandoned: "堵塞并废弃",
      abandoned: "废弃"
    },
    // 油田区块
    oilfieldBlock: {
      title: "油田区块",
      blockName: "区块名称",
      oilField: "油田",
      colorPalette: "调色板",
      structuralPalette: "构造图调色板",
      geologicModel: "地质模型",
      blockWellList: "区块井列表",
      inputBlockNameOrAddress: "请输入区块名或地址",
      selectOilField: "选择油田",
      selectStructuralPalette: "选择构造图调色板",
      blockNameRequired: "区块名必填",
      oilFieldRequired: "油田必选",
      notes: "备注",
      address: "地址"
    },
    // 井层顶面集
    wellLayerTopSets: {
      title: "井层顶面集",
      layerSetName: "标志层集名称",
      purpose: "目的",
      notes: "备注",
      markerLayerSet: "标志层集",
      layerSetNameRequired: "标志层集名称必填",
      // 标志层相关
      markerLayer: "标志层",
      layerName: "标志层名",
      md: "斜深",
      thick: "厚度",
      memo: "备注",
      layerNameRequired: "标志层名必填",
      selectMarkerLayerFile: "选择标志层文件",
      selectTxtFile: "选择txt文件",
      uploadMarkerLayer: "上传标志层",
      uploadMode: "上传模式",
      replace: "替换",
      merge: "合并",
      topName: "层名称",
      depth: "顶深",
      thickness: "层厚",
      saveModification: "保存修改",
      exportData: "导出",
      uploadData: "上传",
      duplicateLayerName: "列表存在相同名称的标志层，无法提交修改，请修改后再试"
    },
    // 文件附件
    fileAttach: {
      title: "文件附件",
      fileName: "文件名",
      fileSize: "文件大小",
      fileType: "文件类型",
      uploadTime: "上传时间",
      selectFile: "选择文件",
      attachType: "类别",
      material: "资料",
      fileRequired: "文件必选",
      // 附件类型选项
      autoDetect: "服务器自动判断",
      image: "图片",
      textDocument: "文本文档",
      wordDocument: "Word文档",
      excelDocument: "Excel文档",
      compressedFile: "压缩文件",
      otherType: "其他类型",
      // 验证消息
      fileNameRequired: "文件名称必填",
      categoryRequired: "类别必填",
      sizeRequired: "大小必填",
      // 对话框
      downloadReport: "下载报表",
      downloadCount: "下载条数",
      enterDownloadCount: "请输入下载条数"
    },
    // 测井数据文件
    logDataFile: {
      title: "测井数据文件",
      dataFile: "数据文件",
      dataType: "数据类型",
      dataFormat: "数据格式",
      curveCount: "曲线数量",
      dataset: "数据集",
      datasetName: "数据集名称",
      relatedJob: "关联作业",
      selectRelatedJob: "选择关联作业",
      runNumber: "RUN编号",
      isGrowing: "数据在增长",
      dtbCurtainRange: "DTBCurtain范围(m) +/-",
      interpolationImaging: "插值成像",
      interpolationSettings: "插值成像设置",
      curveName: "曲线名",
      outputCurveName: "生成曲线名称",
      upCurve: "向上",
      downCurve: "向下",
      averageCurve: "平均",
      addToUp: "添加到向上",
      addToDown: "添加到向下",
      addToAverage: "添加到平均",
      clearSelection: "清空选择",
      inputCurveName: "请输入生成曲线名称",
      // 验证消息
      datasetNameRequired: "数据集名称必填",
      // 文件格式支持
      supportedFormats: "支持.las|.edx|.wis|.lis|.cff|.wtf|.xtf|.dlis格式",
      // 插值成像相关
      curveList: "曲线列表",
      noCurveData: "暂无曲线数据",
      selectUpCurve: "请从左侧选择向上曲线",
      selectDownCurve: "请从左侧选择向下曲线",
      selectAverageCurve: "请从左侧选择平均曲线",
      averageCurveEmpty: "平均曲线为空，是否确定继续？",
      submitCancelled: "已取消提交",
      pleaseSelectFile: "请选择文件",
      // 对话框
      downloadReport: "下载报表",
      downloadCount: "下载条数",
      enterDownloadCount: "请输入下载条数"
    },
    // 井轨迹
    wellTrace: {
      title: "井轨迹",
      md: "测深",
      inc: "井斜",
      azi: "方位",
      tvd: "垂深",
      ns: "南北",
      ew: "东西",
      dls: "狗腿度",
      kb: "补心海拔",
      kbElevation: "补心海拔:"
    },
    // 井眼钻头程序
    wellboreBitProgram: {
      title: "井眼钻头程序",
      bitType: "钻头类型",
      bitSize: "钻头尺寸(英寸)",
      startDepth: "起始深度",
      endDepth: "终止深度",
      bottomDepth: "底部深度(m)",
      depth: "深度",
      // 验证消息
      bitSizeRequired: "钻头尺寸必填",
      bottomDepthRequired: "底部深度必填"
    },
    // 井眼套管程序
    wellboreCasingProgram: {
      title: "井眼套管程序",
      casingType: "套管类型",
      casingSize: "套管尺寸(英寸)",
      casingDepth: "套管深度",
      serial: "套管序列号",
      topDepth: "顶部深度(m)",
      bottomDepth: "底部深度(m)",
      // 验证消息
      casingSizeRequired: "套管尺寸必填",
      serialRequired: "套管序列号必填",
      topDepthRequired: "顶部深度必填",
      bottomDepthRequired: "底部深度必填"
    },
    // 评论
    comment: {
      title: "评论",
      content: "内容",
      author: "作者",
      commentTime: "评论时间",
      contentRequired: "内容必填",
      commentPlaceholder: "说点什么。。。"
    },
    // 测井图列表
    logPlotList: {
      title: "测井图",
      plotName: "测井图名称",
      plotMemo: "备注",
      startIndex: "起始深度",
      endIndex: "结束深度",
      minIndex: "当前数据最小深度",
      maxIndex: "当前数据最大深度",
      depthRatio: "深度比例",
      plotNameRequired: "测井图名称必填",
      trackList: "道列表",
      noDataFound: "没有找到数据"
    },
    // 井眼详情标签页
    wellboreDetail: {
      wellboreInfo: "井眼信息详情",
      bitProgram: "钻头程序",
      casingProgram: "套管程序",
      jobList: "作业列表",
      fileAttachList: "资料列表",
      logDataFileList: "测井数据集",
      logPlotList: "测井图",
      markerLayerList: "标志层",
      markPoints: "标记点"
    },
    // 地质模型
    geologicModel: {
      title: "地质模型",
      modelName: "模型名称",
      inputKeyword: "请输入关键字"
    },
    // 油井详情标签页
    oilWellDetail: {
      wellInfo: "井详情信息",
      wellboreList: "井眼列表"
    },
    // 油田详情标签页
    oilFieldDetail: {
      fieldInfo: "油田信息详情",
      blockList: "区块列表",
      geologicModelList: "地层模型"
    },
    // 油田区块详情标签页
    oilfieldBlockDetail: {
      blockInfo: "区块详情信息",
      geologicModelList: "地质模型",
      wellList: "井列表"
    },
    // 区块地质模型
    blockGeologicModel: {
      title: "地质模型",
      fileName: "文件名",
      depth: "深度",
      leftColor: "颜色左值",
      rightColor: "颜色右值",
      xInitialCoord: "X方向初始坐标",
      yInitialCoord: "Y方向初始坐标",
      zInitialCoord: "Z方向初始坐标",
      xGridPoints: "X方向网格点个数",
      yGridPoints: "Y方向网格点个数",
      xMinValue: "X方向网格点最小值",
      xMaxValue: "X方向网格点最大值",
      yMinValue: "Y方向网格点最小值",
      yMaxValue: "Y方向网格点最大值",
      zMinValue: "Z方向网格点最小值",
      zMaxValue: "Z方向网格点最大值",
      zData: "Z方向数据",
      threeDModel: "三维模型",
      twoDModel: "二维模型",
      detail: "详情",
      uploadFileFormat: "请上传.grd文件或.ascii文件",
      fileNameTooLong: "文件名长度超出限制，无法上传",
      pleaseSelectUploadFile: "请选择要上传的文件"
    }
  },
      // 别名管理
    aliasManagement: {
      title: "别名管理",
      index: "序号",
      curveName: "曲线名",
      curveAlias: "曲线别名",
      description: "描述",
      createTime: "创建时间",
      action: "操作",
      delete: "删除",
      saveAll: "保存全部",
      searchPlaceholder: "请输入曲线名或别名搜索",
      confirmTitle: "确认",
      confirm: "确定",
      cancel: "取消",
      noChanges: "暂无修改",
      saveSuccess: "保存成功",
      saveFailed: "保存失败",
      deleteSuccess: "删除成功",
      deleteCancelled: "已取消删除",
      confirmDelete: "确定要删除这条记录吗？",
      selectDeleteRows: "请选择要删除的记录",
      batchDeleteConfirm: "确定要删除选中的 {count} 条记录吗？",
      batchDeleteSuccess: "批量删除成功",
              importTitle: "导入别名",
        uploadText: "将文件拖到此处，或点击上传",
        uploadTip: "支持 .csv 格式文件",
        importOption: "导入选项",
        mergeOption: "合并",
        overwriteOption: "覆盖",
        mergeTip: "合并：相同曲线名时，合并别名",
        overwriteTip: "覆盖：相同曲线名时，覆盖别名",
        importSuccess: "导入成功",
        importFailed: "导入失败",
      invalidFileType: "文件格式不支持",
      fileTooLarge: "文件大小不能超过 2MB",
      exportFailed: "导出失败",
      noAlias: "无别名",
      chineseCommaError: "不能使用中文逗号（，），请使用英文逗号（,）",
      invalidCharError: "包含不合法字符：{char}",
      aliasTooLongError: "别名过长：{alias}，单个别名不能超过50个字符",
      emptyAliasError: "不能包含空的别名",
      tooManyAliasesError: "别名数量过多，不能超过20个",
      duplicateAliasError: "存在重复的别名",
      inputPlaceholder: "请输入别名，多个别名用英文逗号分隔，如：A1,A2,A3",
      loadFailed: "加载失败",
      exportSuccess: "导出成功",
      pleaseSelectFile: "请选择文件",
      emptyCurveNameWarning: "存在曲线名为空的记录，请填写曲线名后再保存"
    }
};
