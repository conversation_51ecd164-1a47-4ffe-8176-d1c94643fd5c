/**
 * 图片标注编辑器 - 导出功能扩展
 * 提供图片保存和剪贴板复制功能
 */

(function() {
    'use strict';
    
    if (typeof ImageEditor === 'undefined') {
        console.error('ImageEditor class not found. Please ensure imageEditor.js is loaded first.');
        return;
    }
    
    // 扩展导出功能
    Object.assign(ImageEditor.prototype, {
        
        /**
         * 加载图片文件
         */
        async loadImage(file) {
            const text = this.texts ?  ['完钻深度:', `MD ${this.texts.Md.toFixed(2)}m`,`Inc ${this.texts.Incl.toFixed(2)}deg`] : [];
            const url = await plot.exportCanvasToBlob(text);
            const img = new Image();
            img.onload = () => {
                // 调整画布大小以适应图片
                this.resizeCanvasToImage(img);
                
                // 绘制图片作为背景
                this.backgroundImage = img;
                
                // 重新绘制画布
                this.redrawCanvas();
                
                // 自动适应窗口显示
                // setTimeout(() => {
                    // this.fitCanvasToWindow();
                // }, 100);
            };
            img.src = url;
            
        },
        
        /**
         * 调整画布大小以适应图片
         */
        resizeCanvasToImage(img) {
            const maxWidth = 1200;
            const maxHeight = 800;
            
            let canvasWidth = img.width;
            let canvasHeight = img.height;
            
            // // 如果图片太大，按比例缩放
            if (canvasWidth > maxWidth || canvasHeight > maxHeight) {
                const scale = Math.min(maxWidth / canvasWidth, maxHeight / canvasHeight);
                canvasWidth = Math.floor(canvasWidth * scale);
                canvasHeight = Math.floor(canvasHeight * scale);
            }

            // 更新画布包装器样式
            $('#canvasWrapper').css({
                width: canvasWidth + 'px',
                // height: canvasHeight + 'px'
            });
            
            // 设置画布大小
            this.canvas.width = canvasWidth;
            this.canvas.height = canvasHeight;
            
            // 重新设置画布样式
            this.context.lineCap = 'round';
            this.context.lineJoin = 'round';
            
            // 重置画布变换
            if (this.canvasTransform) {
                this.resetCanvasView();
            }
        },
        
        /**
         * 重绘画布（包含背景图片）
         */
        redrawCanvas() {
            // 清除画布
            this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            // 绘制背景图片
            if (this.backgroundImage) {
                this.context.drawImage(this.backgroundImage, 0, 0, this.canvas.width, this.canvas.height);
            } else {
                // 绘制网格
                this.drawGrid();
            }
            
            // 重绘所有图形
            this.shapes.forEach(shape => {
                this.drawShape(shape);
            });
            
            // 绘制选中框
            if (this.selectedShape) {
                this.drawSelectionBox(this.selectedShape);
            }
        },
        
        /**
         * 保存图片为PDF文件（包含手动绘制的文字）
         */
        saveImage() {
            try {
                // 计算包含文字区域的画布尺寸
                const textAreaHeight = this.calculateTextAreasHeight();

                // 创建一个更大的临时画布，包含文字区域
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');

                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height + textAreaHeight + 40; // 额外空间

                // 绘制背景
                tempCtx.fillStyle = 'white';
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // 绘制原始背景图片
                if (this.backgroundImage) {
                    tempCtx.drawImage(this.backgroundImage, 0, 0, this.canvas.width, this.canvas.height);
                }

                // 绘制所有图形（不包含选中状态）
                this.shapes.forEach(shape => {
                    this.drawShapeOnContext(tempCtx, shape);
                });

                // 手动绘制文字区域
                this.drawTextAreasOnCanvas(tempCtx, this.canvas.height + 20);

                // 转换Canvas为图片数据并生成PDF
                const imgData = tempCanvas.toDataURL('image/png');

                // 创建PDF文档
                const { jsPDF } = window.jspdf;

                // 计算画布宽高比
                const canvasRatio = tempCanvas.height / tempCanvas.width;
                

                // 根据画布宽高比确定PDF方向
                const isPortrait = canvasRatio > 1; // 高度大于宽度时使用纵向

                // 创建PDF实例
                const pdf = new jsPDF({
                    orientation: isPortrait ? 'portrait' : 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });

                // 获取实际PDF页面尺寸
                const actualPageSize = pdf.internal.pageSize;
                const pdfWidth = actualPageSize.getWidth();
                const pdfHeight = actualPageSize.getHeight();

                // 计算图片尺寸 - 完全填满页面
                let imgWidth, imgHeight;
                const pdfRatio = pdfHeight / pdfWidth;

                if (canvasRatio > pdfRatio) {
                    // 画布比PDF更"高瘦"，以高度为准
                    imgHeight = pdfHeight;
                    imgWidth = imgHeight / canvasRatio;
                } else {
                    // 画布比PDF更"矮胖"，以宽度为准
                    imgWidth = pdfWidth;
                    imgHeight = imgWidth * canvasRatio;
                }

                console.log('计算的图片尺寸:', imgWidth, 'x', imgHeight);

                // 计算居中位置
                const x = (pdfWidth - imgWidth) / 2;
                const y = (pdfHeight - imgHeight) / 2;
                console.log('图片位置:', x, y);


                // 添加图片到PDF
                pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

                // 生成文件名
                const now = new Date();
                const timeString =
                    `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_` +
                    `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

                // 保存PDF文件
                pdf.save(`导向报告_${timeString}.pdf`);

                $.messager.show({
                    title: '成功',
                    msg: '导向报告PDF已保存到下载文件夹',
                    timeout: 3000,
                    showType: 'slide'
                });

            } catch (error) {
                console.error('保存导向报告PDF失败:', error);
                // $.messager.alert('错误', '保存PDF失败，请重试！', 'error');
            }
        },

        /**
         * 保存图片为PNG文件（包含手动绘制的文字）
         */
        saveImageAsPNG() {
            try {
                // 计算包含文字区域的画布尺寸
                const textAreaHeight = this.calculateTextAreasHeight();

                // 创建一个更大的临时画布，包含文字区域
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');

                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height + textAreaHeight + 40; // 额外空间

                // 绘制背景
                tempCtx.fillStyle = 'white';
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // 绘制原始背景图片
                if (this.backgroundImage) {
                    tempCtx.drawImage(this.backgroundImage, 0, 0, this.canvas.width, this.canvas.height);
                }

                // 绘制所有图形（不包含选中状态）
                this.shapes.forEach(shape => {
                    this.drawShapeOnContext(tempCtx, shape);
                });

                // 手动绘制文字区域
                this.drawTextAreasOnCanvas(tempCtx, this.canvas.height + 20);

                // 转换为blob并下载
                tempCanvas.toBlob((blob) => {
                    const now = new Date();
                    // 格式化时间字符串：YYYYMMDD_HHmmss
                    const timeString =
                        `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_` +
                        `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `导向报告_${timeString}.png`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    $.messager.show({
                        title: '成功',
                        msg: '导向报告PNG已保存到下载文件夹',
                        timeout: 3000,
                        showType: 'slide'
                    });
                }, 'image/png');

            } catch (error) {
                console.error('保存导向报告PNG失败:', error);
            }
        },

        /**
         * 复制图片到剪贴板（包含手动绘制的文字）
         */
        async copyToClipboard() {
            if (!navigator.clipboard || !navigator.clipboard.write) {
                $.messager.alert('不支持', '您的浏览器不支持剪贴板功能！请选择保存功能！', 'warning');
                return;
            }

            try {
                // 计算包含文字区域的画布尺寸
                const textAreaHeight = this.calculateTextAreasHeight();
                console.log(textAreaHeight, 'text====')

                // 创建临时画布
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');

                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height + textAreaHeight + 40; //给临时画布添加高度

                // 绘制背景
                tempCtx.fillStyle = 'white';
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // 绘制原始背景图片
                if (this.backgroundImage) {
                    tempCtx.drawImage(this.backgroundImage, 0, 0, this.canvas.width, this.canvas.height);
                }

                // 绘制所有图形
                this.shapes.forEach(shape => {
                    this.drawShapeOnContext(tempCtx, shape);
                });

                // 手动绘制文字区域
                this.drawTextAreasOnCanvas(tempCtx, this.canvas.height + 20);

                // 转换为blob
                tempCanvas.toBlob(async (blob) => {
                    try {
                        await navigator.clipboard.write([
                            new ClipboardItem({
                                'image/png': blob
                            })
                        ]);

                        $.messager.show({
                            title: '成功',
                            msg: '导向报告已复制到剪贴板',
                            timeout: 3000,
                            showType: 'slide'
                        });
                    } catch (error) {
                        console.error('复制到剪贴板失败:', error);
                        $.messager.alert('错误', '复制到剪贴板失败，请重试！', 'error');
                    }
                }, 'image/png');

            } catch (error) {
                console.error('生成导向报告失败:', error);
                $.messager.alert('错误', '生成失败，请重试！', 'error');
            }
        },

        /**
         * 计算文字区域需要的总高度
         */
        calculateTextAreasHeight() {
            let totalHeight = 0;
            // 总结文本区域高度
            const summaryText = $('#summaryText');
            const detailTextAreas = $('#detailTextAreas');
            // 计算总结文本区域高度
            totalHeight = summaryText[0].offsetHeight + detailTextAreas[0].offsetHeight;
            console.log(`计算文字区域总高度: ${totalHeight}px`);
            return totalHeight + 20;
            if (summaryText && summaryText.value.trim()) {
                const summaryLines = this.calculateTextLines(summaryText.value, this.canvas.width, 'blod 14px Microsoft YaHei');
                // totalHeight += Math.max(60, summaryText.clientHeight); // 行高20px + 内边距
                totalHeight += Math.max(60, summaryLines * 20 + 20); // 行高20px + 内边距
            }

            // 详细描述区域高度
            // const detailTextAreas = document.querySelectorAll('.detail-text');
            if (detailTextAreas.length > 0) {
                let maxRowHeight = 0;
                let currentRowHeight = 0;
                let currentRowWidth = 0;
                const containerWidth = this.canvas.width;
                // const itemWidth = 150 + 10; // 宽度 + gap
                console.log(`计算文字区域总宽度: ${containerWidth}px`);

                detailTextAreas.forEach(textarea => {
                    const textWidth = textarea.clientWidth + 10;
                    if (textarea.value.trim()) {
                        const lines = this.calculateTextLines(textarea.value, textarea.clientWidth, '12px Microsoft YaHei');
                        const itemHeight = Math.max(textarea.clientHeight, lines * 18 + 16); // 行高18px + 内边距

                        // 检查是否需要换行
                        if (currentRowWidth + textWidth > containerWidth) {
                            maxRowHeight += currentRowHeight;
                            currentRowHeight = itemHeight;
                            currentRowWidth = textWidth;
                        } else {
                            currentRowHeight = Math.max(currentRowHeight, itemHeight);
                            currentRowWidth += textWidth;
                        }
                    }
                });

                maxRowHeight += currentRowHeight; // 添加最后一行
                totalHeight += maxRowHeight + 20; // 额外间距
            }

            console.log(`计算文字区域总高度: ${totalHeight}px`);
            return totalHeight;
        },

        /**
         * 计算文本需要的行数
         */
        calculateTextLines(text, maxWidth, font) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.font = font;

            const words = text.split('');
            let lines = 1;
            let currentLine = '';

            for (let i = 0; i < words.length; i++) {
                const testLine = currentLine + words[i];
                const metrics = ctx.measureText(testLine);

                if (metrics.width > maxWidth - 16) { // 减去内边距
                    lines++;
                    currentLine = words[i];
                } else {
                    currentLine = testLine;
                }
            }

            return lines;
        },

        /**
         * 在Canvas上手动绘制文字区域
         */
        drawTextAreasOnCanvas(ctx, startY) {
            let currentY = startY;

            // 绘制总结文本
            const summaryText = document.getElementById('summaryText');
            if (summaryText && summaryText.value.trim()) {
                currentY = this.drawTextAreaOnCanvas(ctx, summaryText.value, 0, currentY, this.canvas.width, 'bold 14px Microsoft YaHei', '#333333');
                currentY += 2; // 间距
            }

            // 绘制详细描述文本
            const detailTextAreas = document.querySelectorAll('.detail-text');
            if (detailTextAreas.length > 0) {
                let currentX = 0;
                let rowStartY = currentY;
                let maxRowHeight = 0;
                const containerWidth = this.canvas.width;
                const itemWidth = 150;
                const gap = 10;

                detailTextAreas.forEach((textarea, index) => {
                    const textWidth = textarea.clientWidth
                    if (textarea.value.trim()) {
                        // 检查是否需要换行
                        if (currentX + textWidth > containerWidth) {
                            currentX = 0;
                            rowStartY += maxRowHeight + gap;
                            maxRowHeight = 0;
                        }

                        const textHeight = this.drawTextAreaOnCanvas(
                            ctx,
                            textarea.value,
                            currentX,
                            rowStartY,
                            textWidth,
                            '12px Microsoft YaHei',
                            '#333333',
                            textarea.clientHeight
                        );

                        maxRowHeight = Math.max(maxRowHeight, textHeight - rowStartY);
                        currentX += textWidth + gap;
                    }
                });
            }
        },

        /**
         * 在Canvas上绘制单个文本区域
         */
        drawTextAreaOnCanvas(ctx, text, x, y, maxWidth, font, color, txtHeight = 0) {
            ctx.save();

            // 绘制边框
            ctx.strokeStyle = '#dddddd';
            ctx.lineWidth = 1;

            // 计算文本高度
            const lines = this.calculateTextLines(text, maxWidth, font);
            const lineHeight = font.includes('14px') ? 20 : 18;
            const padding = 8;
            const textHeight = txtHeight === 0 ? lines * lineHeight + padding * 2 : txtHeight;
            const minHeight = font.includes('14px') ? 60 : 80;
            const finalHeight = Math.max(minHeight, textHeight);

            // 绘制背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(x, y, maxWidth, finalHeight);

            // 绘制边框
            ctx.strokeRect(x, y, maxWidth, finalHeight);

            // 绘制文字
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textBaseline = 'top';

            const words = text.split('');
            let currentLine = '';
            let lineY = y + padding;

            for (let i = 0; i < words.length; i++) {
                const testLine = currentLine + words[i];
                const metrics = ctx.measureText(testLine);

                if (metrics.width > maxWidth - padding * 2) {
                    // 绘制当前行
                    ctx.fillText(currentLine, x + padding, lineY);
                    lineY += lineHeight;
                    currentLine = words[i];
                } else {
                    currentLine = testLine;
                }
            }

            // 绘制最后一行
            if (currentLine) {
                ctx.fillText(currentLine, x + padding, lineY);
            }

            ctx.restore();
            return y + finalHeight;
        },

        /**
         * 在指定上下文中绘制图形（用于导出）
         */
        drawShapeOnContext(ctx, shape) {
            if (!shape || !shape.visible) return;
            
            ctx.save();
            
            // 设置样式
            ctx.strokeStyle = shape.styles.strokeColor || '#ff0000';
            ctx.fillStyle = shape.styles.fillColor || 'rgba(255, 0, 0, 0.1)';
            ctx.lineWidth = shape.styles.lineWidth || 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            switch (shape.type) {
                case 'rectangle':
                    this.drawRectangleOnContext(ctx, shape);
                    break;
                case 'line':
                    this.drawLineOnContext(ctx, shape);
                    break;
                case 'arrow':
                    this.drawArrowOnContext(ctx, shape);
                    break;
                case 'text':
                    this.drawTextOnContext(ctx, shape);
                    break;
                case 'number':
                    this.drawNumberOnContext(ctx, shape);
                    break;
                case 'freehand':
                    this.drawFreehandOnContext(ctx, shape);
                    break;
                case 'curve':
                    this.drawCurveOnContext(ctx, shape);
                    break;
                case 'ellipse':
                    this.drawEllipseOnContext(ctx, shape);
                    break;
            }
            
            ctx.restore();
        },
        
        /**
         * 在指定上下文中绘制矩形
         */
        drawRectangleOnContext(ctx, shape) {
            const { x, y, width, height } = shape.data;
            
            if (shape.styles.fillColor && shape.styles.fillColor !== 'transparent') {
                ctx.fillRect(x, y, width, height);
            }
            ctx.strokeRect(x, y, width, height);
        },
        
        /**
         * 在指定上下文中绘制直线
         */
        drawLineOnContext(ctx, shape) {
            const { startX, startY, endX, endY } = shape.data;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
        },
        
        /**
         * 在指定上下文中绘制箭头
         */
        drawArrowOnContext(ctx, shape) {
            const { startX, startY, endX, endY } = shape.data;
            const arrowLength = 15;
            const arrowAngle = Math.PI / 6;
            
            // 绘制直线
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            
            // 计算箭头角度
            const angle = Math.atan2(endY - startY, endX - startX);
            
            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle - arrowAngle),
                endY - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle + arrowAngle),
                endY - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
        },
        
        /**
         * 在指定上下文中绘制文字
         */
        drawTextOnContext(ctx, shape) {
            const { x, y, text } = shape.data;
            const fontSize = shape.styles.fontSize || 16;
            const fontFamily = shape.styles.fontFamily || 'Arial';
            const fontWeight = shape.styles.fontWeight || 'normal';
            const fontStyle = shape.styles.fontStyle || 'normal';
            
            ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
            ctx.fillStyle = shape.styles.strokeColor || '#ff0000';
            
            // 支持多行文字显示
            const lines = text.split('\n');
            const lineHeight = fontSize * 1.4; // 行高与输入框保持一致
            
             //使用forEach有问题
             for (let i = 0; i < lines.length; i++) {
                ctx.fillText(lines[i], x, y + (i * lineHeight));
            }
        },
        
        /**
         * 在指定上下文中绘制序号
         */
        drawNumberOnContext(ctx, shape) {
            const { x, y, number } = shape.data;
            const radius = 15;
            
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            
            // 绘制数字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(number.toString(), x, y);
            
            // 重置文本对齐
            ctx.textAlign = 'start';
            ctx.textBaseline = 'alphabetic';
        },
        
        /**
         * 在指定上下文中绘制自由曲线
         */
        drawFreehandOnContext(ctx, shape) {
            const points = shape.data.points;
            if (!points || points.length < 2) return;
            
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);
            
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }
            
            ctx.stroke();
        },
        
        /**
         * 在指定上下文中绘制曲线
         */
        drawCurveOnContext(ctx, shape) {
            const { startX, startY, endX, endY, control1X, control1Y, control2X, control2Y } = shape.data;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            
            // 使用三次贝塞尔曲线
            if (control1X !== undefined && control1Y !== undefined && 
                control2X !== undefined && control2Y !== undefined) {
                ctx.bezierCurveTo(control1X, control1Y, control2X, control2Y, endX, endY);
            } else {
                ctx.lineTo(endX, endY);
            }
            
            ctx.stroke();
        },
        
        /**
         * 在指定上下文中绘制椭圆
         */
        drawEllipseOnContext(ctx, shape) {
            const { x, y, width, height } = shape.data;
            
            // 计算椭圆参数
            const centerX = x + width / 2;
            const centerY = y + height / 2;
            const radiusX = Math.abs(width) / 2;
            const radiusY = Math.abs(height) / 2;
            
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
            
            // 绘制填充
            if (shape.styles.fillColor && shape.styles.fillColor !== 'transparent') {
                ctx.fill();
            }
            
            // 绘制边框
            ctx.stroke();
        },
        
        /**
         * 导出为JSON格式（用于保存标注数据）
         */
        exportToJSON() {
            const data = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                canvas: {
                    width: this.canvas.width,
                    height: this.canvas.height
                },
                shapes: this.shapes.map(shape => ({
                    id: shape.id,
                    type: shape.type,
                    data: shape.data,
                    styles: shape.styles,
                    visible: shape.visible
                })),
                nextNumberIndex: this.nextNumberIndex
            };
            
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `annotations_${new Date().getTime()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            $.messager.show({
                title: '成功',
                msg: '标注数据已导出为JSON文件',
                timeout: 3000,
                showType: 'slide'
            });
        },
        
        /**
         * 从JSON格式导入标注数据
         */
        importFromJSON(file) {
            if (!file || file.type !== 'application/json') {
                $.messager.alert('错误', '请选择有效的JSON文件！', 'error');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    if (data.version && data.shapes) {
                        // 清除现有数据
                        this.shapes = [];
                        this.selectedShape = null;
                        
                        // 导入数据
                        this.shapes = data.shapes.map(shapeData => ({
                            ...shapeData,
                            selected: false
                        }));
                        
                        this.nextNumberIndex = data.nextNumberIndex || 1;
                        
                        // 调整画布大小
                        if (data.canvas) {
                            this.canvas.width = data.canvas.width;
                            this.canvas.height = data.canvas.height;
                            $('#canvasWrapper').css({
                                width: data.canvas.width + 'px',
                                height: data.canvas.height + 'px'
                            });
                        }
                        
                        this.redrawCanvas();
                        this.updateLayerList();
                        this.updateSelectedObjectInfo(null);
                        
                        $.messager.show({
                            title: '成功',
                            msg: '标注数据已成功导入',
                            timeout: 3000,
                            showType: 'slide'
                        });
                    } else {
                        throw new Error('无效的JSON格式');
                    }
                } catch (error) {
                    console.error('导入JSON失败:', error);
                    $.messager.alert('错误', '导入JSON文件失败，文件格式可能不正确！', 'error');
                }
            };
            reader.readAsText(file);
        }
    });
    
})();