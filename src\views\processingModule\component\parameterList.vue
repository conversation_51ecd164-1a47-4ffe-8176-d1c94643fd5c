<template>
  <el-table
    :data="filteredParameterList"
    border
    fit
    highlight-current-row
    max-height="450"
    row-key="name"
    :tree-props="{
      children: 'children',
      hasChildren: 'hasChildren',
    }"
    default-expand-all
    :span-method="groupSpanMethod"
  >
   
  <el-table-column
  :label="$t('processMethod.titleColumn')"
  prop="title"
  width="160"
  :show-overflow-tooltip="true"
>
  <template slot-scope="{ row }">
    <template v-if="row.groupFlag">
      {{ row.name }}
    </template>
    <template v-else>
      <span style="float: right;">
        <span style="color: red" v-show="row.optional === 'No'">*</span>
        <span>{{ row.title || row.name }}</span>
      </span>
    </template>
  </template>
</el-table-column>

    <el-table-column
      :label="$t('processMethod.valueColumn')"
      prop="value"
      :show-overflow-tooltip="true"
      canEdit="true"
    >
      <template slot-scope="{ row }">
        <div>
          <template v-if="['Int', 'Real'].includes(row.dataType)">
            <el-input-number
              v-model="row.value"
              :disabled="row.editable === false"
              :min="
                'min' in row && row.min !== null ? Number(row.min) : -Infinity
              "
              :max="
                'max' in row && row.max !== null ? Number(row.max) : Infinity
              "
              style="width: 95%;"
              :controls="false"
            />
          </template>
          <template v-else-if="row.dataType === 'Bool'">
            <el-select
              v-model="row.value"
              :disabled="row.editable === false"
              placeholder="Please select"
              style="width: 95%;"
            >
              <el-option label="Yes" value="Yes"></el-option>
              <el-option label="No" value="No"></el-option>
            </el-select>
          </template>
          <template v-else-if="row.dataType === 'FlagInt'">
            <el-select
              v-model="row.value"
              :disabled="row.editable === false"
              placeholder="Please select"
              style="width: 95%;"
            >
              <el-option
                v-for="(v, i) in stringToArray(row.valueList)"
                :key="i"
                :label="v"
                :value="i"
              ></el-option>
            </el-select>
          </template>
          <template v-else-if="row.dataType === 'MultiSelectableString'">
            <el-select
              v-model="row.value"
              :disabled="row.editable === false"
              multiple
              collapse-tags
              placeholder="Please select"
              style="width: 95%;"
            >
              <el-option
                v-for="(v, i) in stringToArray(row.valueList)"
                :key="i"
                :label="v"
                :value="v"
              ></el-option>
            </el-select>
          </template>
          <template v-else>
            <el-input
              :disabled="row.editable === false"
              v-model="row.value"
              style="width: 95%;"
            />
          </template>
        </div>
      </template>
    </el-table-column>

    <el-table-column
      :label="$t('processMethod.unitColumn')"
      prop="unit"
      width="80"
      :show-overflow-tooltip="true"
    >
    </el-table-column>
  </el-table>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      required: true,
    },
    searchAll: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    filteredParameterList() {
      // console.log(JSON.stringify(this.list));

      const checkDependency = (param, visited = new Set()) => {
        // 循环依赖检测
        if (visited.has(param.name)) return false;
        const allParams = this.list.flatMap((g) => g.children);
        const newVisited = new Set(visited).add(param.name);
        // 检查所有依赖当前参数的配置
        const dependencies = allParams.filter((p) => {
          const { dependencies = [] } = p;
          return dependencies.some(({ target = "" }) => target === param.name);
        });

        // 无依赖项时始终显示
        if (dependencies.length === 0) return true;
        // 初始化当前参数的状态
        const relyParam = allParams.find((p) => p.name === param.name);
        if (relyParam) {
          relyParam.visible = true; // 默认可见
          relyParam.editable = true; // 默认可编辑
        }
        
        // 检查所有依赖，必须全部满足才显示
        let allDependenciesMet = true;
        let finalVisible = true;
        let finalEditable = true;
        
        dependencies.forEach((dep) => {
          const triggerParam = allParams.find((p) => p.name === dep.name);
          if (!triggerParam) {
            allDependenciesMet = false;
            return;
          }
          
          const dependencyConfig = dep.dependencies.find(
            (d) => d.target === param.name // 匹配当前参数的条件
          );
          if (!dependencyConfig) {
            allDependenciesMet = false;
            return;
          }
          
          const currentValue =
            triggerParam.value != null
              ? triggerParam.value
              : triggerParam.defValue;
          const condition = dependencyConfig.condition;
          const triggerValue = condition.triggerValue;
          let value;
          let conditionMet;
          if (triggerParam.dataType === "FlagInt") {
            value = this.stringToArray(triggerParam.valueList)[currentValue];
            conditionMet = value == triggerValue;
          } else if (triggerParam.dataType === "MultiSelectableString") {
            conditionMet =
              JSON.stringify(currentValue) == JSON.stringify(triggerValue);
          } else if (triggerParam.dataType === "Bool") {
            value = currentValue == "Yes" ? true : false;
            conditionMet = value == triggerValue;
          } else if (triggerParam.dataType === "String") {
            conditionMet = currentValue == triggerValue;
          } else {
            // 执行条件判断
            switch (condition.operator) {
              case "==":
                conditionMet = currentValue == triggerValue;
                break;
              case ">":
                conditionMet = currentValue > triggerValue;
                break;
              case "<":
                conditionMet = currentValue < triggerValue;
                break;
              case ">=":
                conditionMet = currentValue >= triggerValue;
                break;
              case "<=":
                conditionMet = currentValue <= triggerValue;
                break;
              default:
                conditionMet = false;
            }
          }

          // 递归检查依赖项自身的显示状态
          const depIsVisible = checkDependency(dep, newVisited);

          // 如果当前依赖不满足，整体就不满足
          if (!conditionMet || !depIsVisible) {
            allDependenciesMet = false;
          }
          
          // 累积影响：任何依赖设置为false，最终就是false
          if (dependencyConfig.effects.visible === false) {
            finalVisible = false;
          }
          if (dependencyConfig.effects.editable === false) {
            finalEditable = false;
          }
        });
        
        // 只有所有依赖都满足时，才设置最终状态
        if (allDependenciesMet && relyParam) {
          relyParam.visible = finalVisible;
          relyParam.editable = finalEditable;
        }
        
        return allDependenciesMet;
      };

      if (this.searchAll.trim() === "") {
        return this.list
          .map((group) => ({
            ...group,
            children: group.children.filter(
              (item) => checkDependency(item) && item.visible !== false
            ),
          }))
          .filter((g) => g.children.length > 0);
      } else {
        let filteredGroupParas = [];
        for (var g of this.list) {
          const group = {
            ...g,
            children: g.children.filter(
              (item) =>
                item.name
                  .toLowerCase()
                  .includes(this.searchAll.toLowerCase()) &&
                checkDependency(item) &&
                item.visible !== false
            ),
          };
          filteredGroupParas.push(group);
        }
        return filteredGroupParas.filter((g) => g.children.length > 0);
      }
    },
  },
  methods: {
    updateEditableStates(groupedParameters) {
      const allParams = groupedParameters.flatMap((group) => group.children);
      //console.log(allParams);
      groupedParameters.forEach((group) => {
        group.children.forEach((sourceParam) => {
          if (!sourceParam.dependencies) return;

          sourceParam.dependencies.forEach((dep) => {
            // 在所有参数中查找目标参数
            const targetParam = allParams.find((p) => p.name === dep.target);
            if (targetParam) {
              targetParam.editable = dep.effects.editable;
              targetParam.visible = dep.effects.visible;
            }
          });
        });
      });
      return groupedParameters;
    },
    groupSpanMethod({ row, columnIndex }) {
      if (row.groupFlag) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },
    // 将逗号分割的字符串转为字符串数组
    stringToArray(content) {
      if (content && content.length > 0) {
        return content.split(",");
      } else {
        return [];
      }
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-input__inner {
  text-align: left;
}
</style>
