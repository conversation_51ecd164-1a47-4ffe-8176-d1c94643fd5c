/* empty css              */import{G as R,r as v,o as V,e as u,d as n,h as l,N as $,w as r,j as i,b as h,f as _,J as S,F as T,s as B,K as D,I as G,t as w,L as U,i as z,M as q,P as E,H as p}from"./index-B5fOQYc3.js";import{a as F}from"./index-DW_MHI2K.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const M={class:"list-container"},A={class:"list-wrapper"},J={class:"cell-title"},K={class:"cell-info"},W={key:0,class:"cell-info"},O={key:1,class:"loading-container"},Q={__name:"list",setup(X){const g=R(),k=window.location.protocol+"//"+window.location.host,d=v([]),c=v(!1),m=v(!1),j=async()=>{try{let e=null;if(e=g.currentRoute.value.query.appId,!e){const o=window.location.hash;if(o.includes("?")){const f=o.split("?")[1];e=new URLSearchParams(f).get("appId")}}if(!e)return p("缺少应用ID参数"),console.log("当前URL:",window.location.href),console.log("Hash部分:",window.location.hash),[];console.log("获取到的appId:",e);const t=(await F.get(`${k}/api/project/project/GetProjctList?appId=${e}`)).data;return t.success&&t.data&&t.data.rows?t.data.rows.map(o=>({id:o.projectId,projectName:o.project[0]?.projectName||"未命名项目",createTime:o.project[0]?.createTime?new Date(o.project[0].createTime).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-"):"未知时间",wellboreNumber:o.wellboreNumber||"",appId:o.appId})):(p(t.message||"获取工程列表失败"),[])}catch(e){return console.error("获取工程列表错误:",e),p("网络请求失败"),[]}},N=()=>{g.back()},y=async()=>{c.value=!0;try{const e=await j();d.value=e}catch{p("加载失败，请重试")}finally{c.value=!1,m.value=!1}},C=()=>{y()},L=e=>{console.log("点击了项目:",e),p(`打开项目: ${e.projectName}`);const s=`${k}/static/GeoSteering/GeoSteeringIndex.html?projectId=${e.id}&appId=${e.appId}`;window.open(s,"_blank")};return V(()=>{y()}),(e,s)=>{const b=$,t=G,o=D,f=S,I=U,x=q,P=E;return n(),u("div",M,[l(b,{title:"工程列表",fixed:"",placeholder:"","left-arrow":"",onClickLeft:N}),l(P,{modelValue:m.value,"onUpdate:modelValue":s[0]||(s[0]=a=>m.value=a),onRefresh:C},{default:r(()=>[i("div",A,[!c.value&&d.value.length>0?(n(),h(f,{key:0,inset:""},{default:r(()=>[(n(!0),u(T,null,B(d.value,a=>(n(),h(o,{key:a.id,clickable:"",onClick:Y=>L(a),class:"project-cell"},{title:r(()=>[i("div",J,w(a.projectName),1)]),label:r(()=>[i("div",K,[l(t,{name:"clock-o",size:"14"}),i("span",null,w(a.createTime),1)]),a.wellboreNumber?(n(),u("div",W,[l(t,{name:"location-o",size:"14"}),i("span",null,w(a.wellboreNumber),1)])):_("",!0)]),"right-icon":r(()=>[l(t,{name:"arrow",color:"#969799"})]),_:2},1032,["onClick"]))),128))]),_:1})):_("",!0),c.value?(n(),u("div",O,[l(I,{size:"24px",vertical:""},{default:r(()=>s[1]||(s[1]=[z("加载中...")])),_:1})])):_("",!0),!c.value&&d.value.length===0?(n(),h(x,{key:2,description:"暂无工程数据"})):_("",!0)])]),_:1},8,["modelValue"])])}}},ae=H(Q,[["__scopeId","data-v-d4ec58b0"]]);export{ae as default};
