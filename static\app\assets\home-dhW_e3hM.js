/* empty css              */import{z as V,A as z,B as E,C as F,h as r,D as W,E as q,k as J,G as K,r as _,o as Q,e as c,d as i,j as o,N as X,i as d,I as Y,f as x,t as p,w as T,L as Z,F as j,s as I,b as ee,T as te,y as oe,H as v}from"./index-B5fOQYc3.js";import{a as y}from"./index-DW_MHI2K.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";let b;const ae={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let ne=z({},ae);function re(){({instance:b}=E({setup(){const{state:m,toggle:l}=F();return()=>r(q,W(m,{"onUpdate:show":l}),null)}}))}function ie(h){return V?new Promise((m,l)=>{b||re(),b.open(z({},ne,h,{callback:g=>{(g==="confirm"?m:l)(g)}}))}):Promise.resolve(void 0)}const ce={class:"workplace-container"},le={class:"content-section"},de={class:"card-container"},pe={class:"card-header"},ue={class:"card-title"},ge={class:"welcome-section"},me={class:"welcome-text"},_e={key:0,class:"welcome-text"},ve={class:"card-container"},fe={class:"card-header"},ye={class:"card-title"},he={class:"project-list"},we={key:0,class:"loading-container"},xe={key:1,class:"list-content"},be=["onClick"],ke={class:"project-content"},Ne={class:"project-title"},Ce={class:"project-info"},Te={class:"info-item"},je={key:0,class:"info-item"},Ie={key:2,class:"empty-state"},ze={class:"card-container"},Le={class:"card-header"},De={class:"card-title"},Me={class:"message-list"},Be=["onClick"],Pe={class:"message-icon"},Se={class:"message-content"},$e={class:"message-title"},He={class:"message-desc"},Oe={class:"message-time"},Ae={style:{"margin-left":"4px"}},Ge=J({__name:"home",setup(h){const m=K(),l=window.location.protocol+"//"+window.location.host,g=_([]),w=_(!1);_(!1);const f=_({userId:"",cName:"",lastLoginDate:""}),L=async()=>{try{const e=(await y.get(l+"/api/user/user/GetCurrentUserBaseInfo")).data;console.log("用户信息:",e),e.success&&e.data?f.value={userId:e.data.userId,cName:e.data.cName,lastLoginDate:e.data.lastLoginDate}:v(e.message||"获取用户信息失败")}catch(t){console.error("获取用户信息错误:",t),v("获取用户信息失败")}},D=t=>t?new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-"):"",M=async()=>{try{const e=(await y.get(`${l}/api/stateSync/Message/GetUserMessages?page=1&size=100`)).data;console.log("消息列表:",e),e.success&&e.data&&e.data.list?k.value=e.data.list.map(a=>({id:a.id,title:a.title,description:a.content||"无内容",time:B(a.createdOn),isRead:!1,priority:a.priority,senderName:a.senderName,messageType:a.messageType,expiryTime:a.expiryTime})):console.error("获取消息列表失败:",e.message)}catch(t){console.error("获取消息列表错误:",t),v("获取消息列表失败")}},B=t=>t?new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-"):"",P=async()=>{w.value=!0;try{const e=(await y.get(l+"/api/project/project/GetHubApp")).data;if(!e.success||!e.data){console.error("获取应用列表失败");return}const a=e.data.find(s=>s.name==="geosteering");if(!a){console.error("未找到地质导向应用");return}const n=a.id;console.log("找到地质导向应用ID:",n);const u=(await y.get(`${l}/api/project/project/GetProjctList?appId=${n}`)).data;u.success&&u.data&&u.data.rows?g.value=u.data.rows.map(s=>({id:s.projectId,projectName:s.project[0]?.projectName||"未命名项目",createTime:s.project[0]?.createTime?new Date(s.project[0].createTime).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-"):"未知时间",wellboreNumber:s.wellboreNumber||"",appId:s.appId})):console.error("获取工程列表失败:",u.message)}catch(t){console.error("获取工程列表错误:",t),v("获取工程列表失败")}finally{w.value=!1}},k=_([]),S=()=>{m.back()},$=t=>{console.log("点击了项目:",t),v(`打开项目: ${t.projectName}`);const e=`${l}/static/GeoSteering/GeoSteeringIndex.html?projectId=${t.id}&appId=${t.appId}`;window.open(e,"_blank")},H=t=>{O(t),t.isRead=!0},O=t=>{const e=`
      <div style="text-align: left;font-size: 13px;line-height: 1.3;margin-top: -20%;">
        <div style="margin-bottom: 2px; color: #666;"><strong>发送人：</strong>${t.senderName||"系统"}</div>
        <div style="margin-bottom: 2px; color: #666;"><strong>时间：</strong>${t.time}</div>
        <div style="margin-bottom: 2px; color: #666;"><strong>类型：</strong>${A(t.messageType)}</div>
        <div style="margin-bottom: 6px; color: #666;"><strong>优先级：</strong>${G(t.priority)}</div>
        <div style="margin-top: -15%;">
          <div style="font-weight: 500; margin-bottom: 4px; color: #333; font-size: 12px;">内容：</div>
          <div style="background: #f8f9fa; padding: 6px 8px; border-radius: 4px; border-left: 3px solid #1989fa; font-size: 12px; line-height: 1.4;">
            ${t.description}
          </div>
        </div>
      </div>
    `;ie({title:t.title,message:e,allowHtml:!0,confirmButtonText:"知道了"})},A=t=>({Public:"公告",Private:"私信",System:"系统消息"})[t]||t,G=t=>({High:"高",Normal:"普通",Low:"低"})[t]||t,R=(t,e)=>e==="High"?"warning-o":{Public:"bullhorn-o",Private:"envelop-o",System:"setting-o"}[t]||"info-o",U=(t,e)=>e==="High"?"#ff4757":{Public:"#1989fa",Private:"#52c41a",System:"#faad14"}[t]||"#969799";return Q(()=>{L(),P(),M()}),(t,e)=>{const a=X,n=Y,N=Z,u=te;return i(),c("div",ce,[r(a,{title:"工作台","left-arrow":"",onClickLeft:S,class:"nav-bar"}),o("div",le,[o("div",de,[o("div",pe,[o("h3",ue,[r(n,{name:"user-o",size:"18",color:"#1989fa",style:{"margin-right":"8px"}}),e[0]||(e[0]=d(" 个人信息 "))])]),o("div",ge,[o("div",me,[r(n,{name:"smile-o",size:"18",color:"#1989fa",style:{"margin-right":"8px"}}),d(" 欢迎回来，"+p(f.value.cName||"用户"),1)]),f.value.lastLoginDate?(i(),c("div",_e,[r(n,{name:"clock-o",size:"14",color:"#969799",style:{"margin-right":"6px"}}),d(" 上次登录："+p(D(f.value.lastLoginDate)),1)])):x("",!0)])]),o("div",ve,[o("div",fe,[o("h3",ye,[r(n,{name:"folder-o",size:"18",color:"#1989fa",style:{"margin-right":"8px"}}),e[1]||(e[1]=d(" 工程列表 "))])]),o("div",he,[w.value?(i(),c("div",we,[r(N,{size:"24px",vertical:""},{default:T(()=>e[2]||(e[2]=[d("加载中...")])),_:1})])):g.value.length>0?(i(),c("div",xe,[(i(!0),c(j,null,I(g.value,s=>(i(),c("div",{key:s.id,class:"project-item",onClick:C=>$(s)},[o("div",ke,[o("div",Ne,p(s.projectName),1),o("div",Ce,[o("div",Te,[r(n,{name:"clock-o",size:"12",color:"#969799"}),o("span",null,p(s.createTime),1)]),s.wellboreNumber?(i(),c("div",je,[r(n,{name:"location-o",size:"12",color:"#969799"}),o("span",null,p(s.wellboreNumber),1)])):x("",!0)])]),r(n,{name:"arrow",color:"#969799",size:"16"})],8,be))),128))])):(i(),c("div",Ie,[r(n,{name:"folder-o",size:"48",color:"#ddd"}),e[3]||(e[3]=o("div",{class:"empty-text"},"暂无工程数据",-1))]))])]),o("div",ze,[o("div",Le,[o("h3",De,[r(n,{name:"chat-o",size:"18",color:"#1989fa",style:{"margin-right":"8px"}}),e[4]||(e[4]=d(" 消息列表 "))])]),o("div",Me,[(i(!0),c(j,null,I(k.value,(s,C)=>(i(),c("div",{key:C,class:"message-item",onClick:Re=>H(s)},[o("div",Pe,[r(n,{name:R(s.messageType,s.priority),color:U(s.messageType,s.priority),size:"20"},null,8,["name","color"])]),o("div",Se,[o("div",$e,[d(p(s.title)+" ",1),s.priority==="High"?(i(),ee(u,{key:0,type:"danger",size:"mini",style:{"margin-left":"8px"}},{default:T(()=>e[5]||(e[5]=[d(" 重要 ")])),_:1})):x("",!0)]),o("div",He,p(s.description),1),o("div",Oe,[r(n,{name:"clock-o",size:"12",color:"#c8c9cc"}),o("span",Ae,p(s.time),1)])]),o("div",{class:oe(["message-status",{unread:!s.isRead}])},null,2)],8,Be))),128))])])])])}}}),We=se(Ge,[["__scopeId","data-v-1d329810"]]);export{We as default};
