import{e as u,d as c,j as a,f as N,p as C,F as v,s as f,u as V,x as p,y as I,t as g,v as x,a as b,i as w,h as y}from"./index-B5fOQYc3.js";import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={name:"CustomPalette",props:{initialPalette:{type:Array,default:()=>[{position:0,color:"#FF0000"},{position:1,color:"#0000FF"}]},minValue:{type:Number,default:0},maxValue:{type:Number,default:100}},data(){return{nodes:this.initialPalette.map((t,e)=>({...t,id:t.id||`initial-${e}-${Date.now()}`})),canvasWidth:300,canvasHeight:50,draggingIndex:null,draggingNodeId:null,showMenu:!1,menuX:0,menuY:0,menuNodeIndex:null,showColorPicker:!1,selectedColor:"#000000",selectedNodeIndex:null}},computed:{sortedNodes(){return[...this.nodes].sort((t,e)=>t.position-e.position)},majorTicks(){const t=[],e=this.maxValue-this.minValue,i=Math.min(10,Math.max(3,Math.floor(e/10)+1));for(let r=0;r<=i;r++){const o=this.minValue+e*r/i,s=r/i;t.push({value:Math.round(o*10)/10,position:s})}return t},minorTicks(){const t=[],e=this.maxValue-this.minValue,i=this.majorTicks.length-1,r=4;for(let o=0;o<i;o++)for(let s=1;s<r;s++){const n=o/i,l=(o+1)/i,d=n+(l-n)*s/r,m=this.minValue+e*d;t.push({value:Math.round(m*10)/10,position:d})}return t}},mounted(){this.$nextTick(()=>{this.resizeCanvas(),this.drawPalette()}),window.addEventListener("resize",this.resizeCanvas),window.addEventListener("mousemove",this.handleDrag),window.addEventListener("mouseup",this.stopDragging)},beforeDestroy(){window.removeEventListener("resize",this.resizeCanvas),window.removeEventListener("mousemove",this.handleDrag),window.removeEventListener("mouseup",this.stopDragging)},methods:{drawPalette(){const t=this.$refs.paletteCanvas;if(!t)return;const e=t.getContext("2d"),i=t.getBoundingClientRect();t.width=this.canvasWidth,t.height=this.canvasHeight,console.log("画布尺寸信息:",{canvasWidth:this.canvasWidth,canvasHeight:this.canvasHeight,actualWidth:t.width,actualHeight:t.height,cssWidth:i.width,cssHeight:i.height});const r=e.createLinearGradient(0,0,this.canvasWidth,0);this.nodes.sort((o,s)=>o.position-s.position).forEach(o=>{r.addColorStop(o.position,o.color)}),e.fillStyle=r,e.fillRect(0,0,this.canvasWidth,this.canvasHeight),e.strokeStyle="#000",e.strokeRect(0,0,this.canvasWidth,this.canvasHeight),this.$emit("palette-updated",this.nodes,this.mapValueToColor)},resizeCanvas(){const t=this.$refs.paletteCanvas.parentElement.clientWidth||300;t!==this.canvasWidth&&(this.canvasWidth=t,this.drawPalette())},addNode(t){console.log("addNode 方法被调用");const e=this.$refs.paletteCanvas;if(!e){console.log("画布不存在");return}const i=e.getBoundingClientRect(),r=t.clientX-i.left,o=r/this.canvasWidth;if(console.log("点击信息:",{clientX:t.clientX,rectLeft:i.left,x:r,canvasWidth:this.canvasWidth,position:o}),o<=.05||o>=.95){console.log("位置太靠近端点，跳过添加");return}const s=this.interpolateColor(o),n={position:o,color:s,id:Date.now()+Math.random()};console.log("新节点:",n),this.nodes.push(n),this.nodes.sort((l,d)=>l.position-d.position),this.drawPalette(),console.log("节点添加完成，当前节点数:",this.nodes.length)},startDragging(t,e){this.draggingNodeId=this.nodes[e].id||e,this.draggingIndex=e,this.showMenu=!1},handleDrag(t){if(this.draggingIndex===null)return;if(this.draggingNodeId!==void 0){const m=this.nodes.findIndex(h=>h.id===this.draggingNodeId);m!==-1&&(this.draggingIndex=m)}const i=this.$refs.paletteCanvas.getBoundingClientRect();let r=t.clientX-i.left;r=Math.max(0,Math.min(r,this.canvasWidth));let o=r/this.canvasWidth;const s=[...this.nodes].sort((m,h)=>m.position-h.position),n=s.findIndex(m=>m.id===this.draggingNodeId||s.indexOf(this.nodes[this.draggingIndex])===s.indexOf(m)),l=n===0,d=n===s.length-1;if(l)o=Math.min(o,s[1].position);else if(d)o=Math.max(o,s[s.length-2].position);else{const m=s[n-1],h=s[n+1];o=Math.max(m.position,Math.min(o,h.position))}this.nodes[this.draggingIndex].position=o,this.drawPalette()},stopDragging(){this.draggingIndex=null,this.draggingNodeId=null},showContextMenu(t,e){e===0||e===this.nodes.length-1||(this.showMenu=!0,this.menuX=t.clientX,this.menuY=t.clientY,this.menuNodeIndex=e)},deleteNode(){this.menuNodeIndex!==null&&this.menuNodeIndex>0&&this.menuNodeIndex<this.nodes.length-1&&(this.nodes.splice(this.menuNodeIndex,1),this.drawPalette()),this.showMenu=!1,this.menuNodeIndex=null},editNodeColor(t){this.selectedNodeIndex=t,this.selectedColor=this.nodes[t].color,this.showColorPicker=!0,this.$nextTick(()=>{this.$refs.colorPicker.focus()})},updateNodeColor(){this.selectedNodeIndex!==null&&(this.nodes[this.selectedNodeIndex].color=this.selectedColor,this.drawPalette())},interpolateColor(t){const e=[...this.nodes].sort((h,P)=>h.position-P.position);let i=e[0],r=e[e.length-1];for(let h=0;h<e.length-1;h++)if(t>=e[h].position&&t<=e[h+1].position){i=e[h],r=e[h+1];break}const o=(t-i.position)/(r.position-i.position),s=this.hexToRGB(i.color),n=this.hexToRGB(r.color),l=Math.round(s.r+o*(n.r-s.r)),d=Math.round(s.g+o*(n.g-s.g)),m=Math.round(s.b+o*(n.b-s.b));return`#${((1<<24)+(l<<16)+(d<<8)+m).toString(16).slice(1)}`},hexToRGB(t){const e=parseInt(t.slice(1,3),16),i=parseInt(t.slice(3,5),16),r=parseInt(t.slice(5,7),16);return{r:e,g:i,b:r}},mapValueToColor(t){const e=(t-this.minValue)/(this.maxValue-this.minValue);return this.interpolateColor(e)},savePalette(){this.$emit("save-palette",this.nodes)},getOriginalIndex(t){return this.nodes.findIndex(e=>e.id===t.id||e===t)},handleClick(t){console.log("画布被单击了:",t)},addNodeAtCenter(){const i={position:.5,color:this.interpolateColor(.5),id:Date.now()+Math.random()};console.log("手动添加节点:",i),this.nodes.push(i),this.nodes.sort((r,o)=>r.position-o.position),this.drawPalette(),console.log("节点添加完成，当前节点数:",this.nodes.length)},getNodeValue(t){return this.minValue+(this.maxValue-this.minValue)*t},exportPaletteAsJSON(){const t={version:"1.0",timestamp:new Date().toISOString(),palette:{nodeCount:this.nodes.length,minValue:this.minValue,maxValue:this.maxValue,nodes:this.nodes.map((e,i)=>({index:i,position:e.position,color:e.color,id:e.id}))}};return JSON.stringify(t,null,2)},loadPaletteFromJSON(t){try{const e=typeof t=="string"?JSON.parse(t):t;if(e.palette&&e.palette.nodes)return this.nodes=e.palette.nodes.map(i=>({position:i.position,color:i.color,id:i.id||`loaded-${i.index}-${Date.now()}`})),this.drawPalette(),this.$emit("palette-loaded",this.nodes),!0}catch(e){return console.error("加载调色板失败:",e),!1}return!1}}},M={class:"custom-palette-container"},R={class:"nodes-container"},T=["onMousedown","onDblclick","onContextmenu"],F={class:"node-labels"},O={class:"scale-container"},W={class:"tick-label"},D={class:"add-node-controls"};function L(t,e,i,r,o,s){return c(),u("div",M,[a("canvas",{ref:"paletteCanvas",class:"palette-canvas",onDblclick:e[0]||(e[0]=(...n)=>s.addNode&&s.addNode(...n)),onClick:e[1]||(e[1]=(...n)=>s.handleClick&&s.handleClick(...n)),title:"双击此处添加新节点"},null,544),a("div",R,[(c(!0),u(v,null,f(s.sortedNodes,(n,l)=>(c(),u("div",{key:n.id||l,class:I(["node",{"end-node":l===0||l===s.sortedNodes.length-1}]),style:p({left:`${n.position*o.canvasWidth}px`}),onMousedown:d=>s.startDragging(d,s.getOriginalIndex(n)),onDblclick:V(d=>s.editNodeColor(s.getOriginalIndex(n)),["stop"]),onContextmenu:V(d=>s.showContextMenu(d,s.getOriginalIndex(n)),["prevent"])},null,46,T))),128))]),a("div",F,[(c(!0),u(v,null,f(s.sortedNodes,(n,l)=>(c(),u("div",{key:"label-"+(n.id||l),class:"node-label",style:p({left:`${n.position*o.canvasWidth}px`})},g(s.getNodeValue(n.position).toFixed(1)),5))),128))]),a("div",O,[e[8]||(e[8]=a("div",{class:"scale-line"},null,-1)),(c(!0),u(v,null,f(s.majorTicks,n=>(c(),u("div",{key:"major-"+n.value,class:"scale-tick major-tick",style:p({left:`${n.position*o.canvasWidth}px`})},[e[6]||(e[6]=a("div",{class:"tick-mark"},null,-1)),a("div",W,g(n.value),1)],4))),128)),(c(!0),u(v,null,f(s.minorTicks,n=>(c(),u("div",{key:"minor-"+n.value,class:"scale-tick minor-tick",style:p({left:`${n.position*o.canvasWidth}px`})},e[7]||(e[7]=[a("div",{class:"tick-mark"},null,-1)]),4))),128))]),a("div",D,[a("button",{onClick:e[2]||(e[2]=(...n)=>s.addNodeAtCenter&&s.addNodeAtCenter(...n)),class:"add-node-btn"}," ➕ 在中间添加节点 "),e[9]||(e[9]=a("span",{class:"hint"},"或双击色标条添加节点",-1))]),o.showMenu?(c(),u("div",{key:0,class:"context-menu",style:p({top:`${o.menuY}px`,left:`${o.menuX}px`})},[a("button",{onClick:e[3]||(e[3]=(...n)=>s.deleteNode&&s.deleteNode(...n))},"Delete")],4)):N("",!0),o.showColorPicker?C((c(),u("input",{key:1,type:"color","onUpdate:modelValue":e[4]||(e[4]=n=>o.selectedColor=n),class:"color-picker",onInput:e[5]||(e[5]=(...n)=>s.updateNodeColor&&s.updateNodeColor(...n)),ref:"colorPicker"},null,544)),[[x,o.selectedColor]]):N("",!0)])}const j=k(S,[["render",L],["__scopeId","data-v-26580b8a"]]),E={name:"ColorCodeIndex",components:{ColorCode:j},data(){return{defaultPalette:[{position:0,color:"#FF0000"},{position:.5,color:"#FFFF00"},{position:1,color:"#0000FF"}],minValue:0,maxValue:100,currentPalette:[],mapValueToColorFunction:null,testValue:50,testColor:""}},mounted(){this.currentPalette=[...this.defaultPalette]},methods:{onPaletteUpdated(t,e){this.currentPalette=[...t],this.mapValueToColorFunction=e,this.updateTestColor(),console.log("调色板已更新:",t)},onSavePalette(t){console.log("保存调色板:",t),this.$message?.success?.("调色板已保存")||alert("调色板已保存")},onPaletteLoaded(t){this.currentPalette=[...t],console.log("调色板已加载:",t),this.$message?.success?.("调色板加载成功")||alert("调色板加载成功")},exportPalette(){try{const t=this.$refs.colorCodeRef.exportPaletteAsJSON(),e=new Blob([t],{type:"application/json"}),i=URL.createObjectURL(e),r=document.createElement("a");r.href=i,r.download=`color-palette-${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.json`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(i),this.$message?.success?.("调色板已导出")||alert("调色板已导出"),console.log("导出的JSON数据:",t)}catch(t){console.error("导出失败:",t),this.$message?.error?.("导出失败")||alert("导出失败")}},triggerImport(){this.$refs.fileInput.click()},importPalette(t){const e=t.target.files[0];if(!e)return;if(!e.name.endsWith(".json")){this.$message?.warning?.("请选择JSON文件")||alert("请选择JSON文件");return}const i=new FileReader;i.onload=r=>{try{this.$refs.colorCodeRef.loadPaletteFromJSON(r.target.result)||this.$message?.error?.("JSON文件格式不正确")||alert("JSON文件格式不正确")}catch(o){console.error("导入失败:",o),this.$message?.error?.("导入失败")||alert("导入失败")}},i.readAsText(e),t.target.value=""},updateRange(){if(this.minValue>=this.maxValue){this.$message?.warning?.("最小值必须小于最大值")||alert("最小值必须小于最大值");return}this.testValue<this.minValue?this.testValue=this.minValue:this.testValue>this.maxValue&&(this.testValue=this.maxValue),this.updateTestColor()},resetRange(){this.minValue=0,this.maxValue=100,this.testValue=50,this.updateTestColor()},updateTestColor(){this.mapValueToColorFunction&&typeof this.testValue=="number"&&(this.testColor=this.mapValueToColorFunction(this.testValue))}}},J={class:"color-code-page"},B={class:"range-settings"},U={class:"range-inputs"},z={class:"palette-wrapper"},H={class:"palette-controls"},X={key:0,class:"palette-info"},A={class:"node-list"},G={class:"test-section"},Y={class:"test-input"},_=["min","max"];function q(t,e,i,r,o,s){const n=b("ColorCode");return c(),u("div",J,[e[15]||(e[15]=a("h1",null,"颜色编码调色板",-1)),a("div",B,[e[12]||(e[12]=a("h3",null,"数值范围设置：",-1)),a("div",U,[a("label",null,[e[10]||(e[10]=w(" 最小值: ")),C(a("input",{type:"number","onUpdate:modelValue":e[0]||(e[0]=l=>o.minValue=l),onChange:e[1]||(e[1]=(...l)=>s.updateRange&&s.updateRange(...l)),step:"0.1"},null,544),[[x,o.minValue,void 0,{number:!0}]])]),a("label",null,[e[11]||(e[11]=w(" 最大值: ")),C(a("input",{type:"number","onUpdate:modelValue":e[2]||(e[2]=l=>o.maxValue=l),onChange:e[3]||(e[3]=(...l)=>s.updateRange&&s.updateRange(...l)),step:"0.1"},null,544),[[x,o.maxValue,void 0,{number:!0}]])]),a("button",{onClick:e[4]||(e[4]=(...l)=>s.resetRange&&s.resetRange(...l)),class:"reset-btn"},"重置为0-100")])]),a("div",z,[y(n,{ref:"colorCodeRef","initial-palette":o.defaultPalette,"min-value":o.minValue,"max-value":o.maxValue,onPaletteUpdated:s.onPaletteUpdated,onSavePalette:s.onSavePalette,onPaletteLoaded:s.onPaletteLoaded},null,8,["initial-palette","min-value","max-value","onPaletteUpdated","onSavePalette","onPaletteLoaded"]),a("div",H,[a("button",{onClick:e[5]||(e[5]=(...l)=>s.exportPalette&&s.exportPalette(...l)),class:"control-btn export-btn"}," 📥 导出JSON "),a("button",{onClick:e[6]||(e[6]=(...l)=>s.triggerImport&&s.triggerImport(...l)),class:"control-btn import-btn"}," 📤 导入JSON "),a("input",{ref:"fileInput",type:"file",accept:".json",onChange:e[7]||(e[7]=(...l)=>s.importPalette&&s.importPalette(...l)),style:{display:"none"}},null,544)])]),o.currentPalette.length>0?(c(),u("div",X,[e[13]||(e[13]=a("h3",null,"当前调色板信息：",-1)),a("div",A,[(c(!0),u(v,null,f(o.currentPalette,(l,d)=>(c(),u("div",{key:d,class:"node-info"},[a("span",null,"节点 "+g(d+1)+":",1),a("span",{class:"color-preview",style:p({backgroundColor:l.color})},null,4),a("span",null,"位置: "+g((l.position*100).toFixed(1))+"%",1),a("span",null,"颜色: "+g(l.color),1)]))),128))])])):N("",!0),a("div",G,[e[14]||(e[14]=a("h3",null,"测试颜色映射：",-1)),a("div",Y,[a("label",null,"输入数值 ("+g(o.minValue)+" - "+g(o.maxValue)+"): ",1),C(a("input",{type:"number","onUpdate:modelValue":e[8]||(e[8]=l=>o.testValue=l),min:o.minValue,max:o.maxValue,onInput:e[9]||(e[9]=(...l)=>s.updateTestColor&&s.updateTestColor(...l))},null,40,_),[[x,o.testValue,void 0,{number:!0}]]),o.testColor?(c(),u("div",{key:0,class:"test-color-preview",style:p({backgroundColor:o.testColor})},g(o.testColor),5)):N("",!0)])])])}const Z=k(E,[["render",q],["__scopeId","data-v-715823f7"]]);export{Z as default};
