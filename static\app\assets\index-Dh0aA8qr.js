import{k as defineComponent,c as computed,e as createElementBlock,d as openBlock,a6 as renderSlot,x as normalizeStyle,y as normalizeClass,r as ref,l as watch,n as nextTick,R as ElMessage,a as resolveComponent,O as resolveDirective,b as createBlock,w as withCtx,p as withDirectives,f as createCommentVNode,h as createVNode,i as createTextVNode,t as toDisplayString,j as createBaseVNode,a7 as ElementPlusIconsVue,F as Fragment,s as renderList,q as vModelSelect,v as vModelText,a8 as vModelCheckbox,o as onMounted,a9 as onBeforeUnmount,a3 as applyVueInReact,a4 as reactExports,a5 as applyReactInVue,D as mergeProps,W as unref}from"./index-B5fOQYc3.js";import{D as DockLayout}from"./rc-dock-C2QYZG-B.js";import{_ as _sfc_main$5,a as __unplugin_components_1,u as useResourceTreeStore,b as usePreviewStore,c as _sfc_main$6,d as _sfc_main$7}from"./Collapse.vue_vue_type_script_setup_true_lang-Bjaf696f.js";import{_ as _export_sfc}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as axios}from"./index-DW_MHI2K.js";import{C as Color,L as LinearSRGBColorSpace,d as MathUtils,B as BufferGeometry,V as Vector3,e as Vector2,F as Float32BufferAttribute,f as Matrix4,g as CanvasTexture,h as MeshBasicMaterial,i as BackSide,M as Mesh,U as UnsignedByteType,j as Sprite,k as SpriteMaterial,N as NormalBlending,G as Group,l as Line,W as WebGLRenderer,S as Scene,P as PerspectiveCamera,O as OrbitControls,A as AmbientLight,D as DirectionalLight,T as TubeGeometry,m as MeshPhongMaterial,c as DoubleSide,n as CatmullRomCurve3,o as LineDashedMaterial,p as LineBasicMaterial,q as Plane,r as MeshLambertMaterial,s as PointLight,t as Curve,a as PlaneGeometry,b as ShaderMaterial}from"./OrbitControls-CLEN8DbS.js";import{P as Property}from"./property-C5fC_ukq.js";const __vite_glob_0_0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAABH0lEQVR4nJ3SvyuFcRTH8VdkIVK4G5mQEmWmhKIoJbIZyB9gNuEfUH4kvxYhBuXXhklZiFBWi8lmsOqpc+vp0r2XU9+ep9N5f87nfL+H/JHBSIGavPAjPjH+H/gJaziI7+Bf4HPcoRYLaMYOuouBzzCH6cj1YQpl2EdnPvgUPXhAG0pQga2oKcdxOPoB34f1ZdyGi704L+hFJapxgoYsXBMXto3GyK2EqHCxhHlsxBhHeA1WVuAG6xiNM5NymLzAZPyXYjEtILolttoxETeeiM7GvFXRPbmXa7yhXk5kcmbbjM6J9UO8h/AVmnLh30SGU7ZbsRpL1aFA1MUutGA3cslLXKCrEJx2kuzEJfrxhYFi4bTIMz4w9lc4LTKUr+IbQJY5NwAOhEgAAAAASUVORK5CYII=",__vite_glob_0_1="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AzMzMzMzMzMzP/////////M////+qr6r8z////6qvqvzPd3f/mZ+Z/M////+Zn5n8z/////////zMzMzMzMzMzM/////////8z/////////zPd3d//3d3/M/////////8z/////////zPd3d/93d3/M/////////8zMzMzMzMzMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_2="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAfklEQVR42sXQ0Q2AIAwEUG4mnUlnwplgpoqFIBBqMSTKDyH0XgswkwvfAZaI9x0YAFJxLMEVdpvn03osFQIxTOEKybHGDAJFmG/j0ZHPFQwUT4EYzi2oAtopaoA6L1KmwGN3DYmAElYQiKMLSPsfMCE+lu5P8Q4okBuYXP8DJ+29SdJt7IliAAAAAElFTkSuQmCC",__vite_glob_0_3="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1753752548064'%20class='icon'%20viewBox='0%200%201084%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='33923'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='271'%20height='256'%3e%3cpath%20d='M1083.668152%20889.437085V210.601651c0-82.521108-75.051279-74.750286-75.051279-74.750286s-448.32072%200.480989-424.649263%200c-25.659412%200.480989-38.36912-13.492691-38.36912-13.492691s-17.88959-30.719296-49.993854-79.268182C461.994407-7.806341%20423.0223%200.505468%20423.0223%200.505468H92.639876C1.083975%200.505468%200%2088.627448%200%2088.627448v796.291741c0%2098.181749%2074.208298%2086.015028%2074.208298%2086.015028h941.215419c79.38818%200%2068.244435-81.497131%2068.244435-81.497132z'%20fill='%23FFC500'%20p-id='33924'%3e%3c/path%3e%3c/svg%3e",__vite_glob_0_4="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAP////f///8A9/9/9/9/fwD3/3/3/39/AP////f///8AAAAAAAAAAA//////////8AAAAAAAAAAA////9/9//wD/f3/3/3//AP////f/f/8A////9/9//wD/f3/3/3d3AP9/f/f/f/8A////9/9//wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_5="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEzZf8ENmr/AAAAAAAAAAABM2X/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABM2X/ATNl/wEzZf8BM2X/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABM2X/ATNl/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATNl/wEzZf8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEzZf8BM2X/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPlYU/wAAAAAAAAAAAAAAAAEzZf8MPnL/ATNl/wAAAAAAAAAAAAAAAD5WFP8AAAAAAAAAAAAAAAAAAAAAPlYU/2i5Wf8+VhT/PlYU/z5WFP8nZWv/GUx//zN2bP8BM2X/PlYU/z5WFP9XnTf/PlYU/z5WFP8AAAAAPlYU/12qS/8zbRX/L2YP/zd0Gv8vgCP/L4Aj/yyoNv9Ahk7/J2Vr/ydla/8nZWv/OJo9/0agMv9KiiT/PlYU/wAAAAA+VhT/N3Qa/zNtFf8vahD/O4kk/0WnNP8lqR3/QK9S/zN2bP8xbRP/Moch/y6WKf9GoDL/XKM9/z5WFP8AAAAAAAAAAD5WFP9Mkzb/Ups+/2q9Xf9Jk3T/TJRc/0mTdP9muHH/Km8q/zVxEf8vZg//NXsc/2azTP8+VhT/AAAAAD5WFP9Gii7/RIYq/1qmSP89gmz/IF1e/3bMa/8nZWv/SZN0/0mTdP9WrUT/WqZI/zNtFf9ms0z/PlYU/wAAAAAAAAAAPlYU/zV7HP83bhj/Km8q/zWNGf9Ahk7/SZN0/0yUXP9CuDn/M7Ur/4jmgf+I5oH/PlYU/wAAAAAAAAAAAAAAAD5WFP88eyD/PHsg/zx7IP83dBr/JW46/0SULv87lCf/LpYp/zqqLf9Rz03/PlYU/wAAAAAAAAAAAAAAAAAAAAAAAAAAPlYU/zx7IP88gSL/JW46/yVuOv9GoDL/YK5P/0yUXP940Gz/heJ9/z5WFP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+VhT/PlYU/zOuKf8zuS3/T6Y7/1qmSP8+VhT/PlYU/z5WFP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+VhT/PlYU/z5WFP8+VhT/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA+b8AAPh/AAD+fwAA/n8AAP5/AADcdwAAgAEAAAAAAACAAAAAwAAAAIAAAADAAQAAwAMAAOADAADwBwAA/D8AAA==",__vite_glob_0_6="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACPUlEQVR42o2TvWsUURTFz515O/PmK7tZJNgbBAVBWLDNssHSSv8BS0VSiNUWliJpU9hpISG7IkKwEMXGyl5BC4uY+Bmi2c0O2c3Ozsz1vtmsGowhA2/ug5nzu+fcmUc45Pp0/SyOe9HGYsMipQCLQLZCKqvKMVaX3+T18zNIomlk210QUmA0gqVLyJIYvXdfUXs9JFq/OwcWsQHYykXCwAx66H7oQp05xUd1P3njCdH/7Bpx+VrzSPs79+6APi7W4XrRP52MmDffMxwHcEtEtiuBtUGD3DCjuGd3Hi6Bvi1dOrQTr71NyVGcOTbIAZFEZNhmbALwc+z2Va+1YhWAaLaWsWMxuZrY00wlIDy3oAqba82cPSbLdphtLXK3ANBgaMetl1QAwtlaDq0ZKqcs3RJZlyv1B/bETff7TSYVMFEoBiaARMWt5+MI4ekLOQ82KdvbYLgpUeCjcvEx/R2p9+N2TlZQzKEA9IcSYR/gRR7z4AsoCsCRDwpcVObbBwE/BXDAgQBWJoDRVs6hCEMN9mXqoYvp+bb1J8KtnGwziPC3A6u/p3rtF+MZBFlnZEQIRBwIRByUG62SEcfrCwkHWj6jz+MIngACiTBwdpafjb+CzjqAdGZP7JkqIDJ7A5TKvnRVIhQxW2Yvqz/E7tNX4wjHPjmHHSZ+1JDMsjsht2hKahmoyh9n8kIsIzL/pSyJCG+/mnQZtq9cFcD9OR9aBl6Rh6EIKiKckpeqvmiNQGrRQeJATwDiOqXPl5vJL2jl27QjoOsDAAAAAElFTkSuQmCC",__vite_glob_0_7="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA/wAAAAAAAAAAAAAAAAAAAAAAAAD///8A////AP///wD/AAAA/wAAAAAAAAAAAAAAAAAAAAAAAAAA/wAA//8AAP8AAAAAAAAAAAAAAAAAAAAAAAAA////AP8AAAD///8A/wAAAP8AAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP//AAD/AAAAAAAAAAAAAAAAAAAAAAAAAP///wD///8A////AP8AAAD/AAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA/wAAAAAAAAAAAAAAAAAAAAAAAAD///8A/wAAAP///wD/AAAA/wAAAAAAAAAAAAAAAAAAAAAAAAAA/wAA//8AAP8AAAAAAAAAAAAAAAAAAAAAAAAA////AP///wD///8A/wAAAP8AAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP//AAD/AAAAAAAAAAAAAAAAAAAAAAAAAP///wD/AAAA////AP8AAAD/AAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA/wAAAAAAAAAAAAAAAAAAAAAAAAD///8A////AP///wD/AAAA/wAAAAAAAAAAAAAAAAAAAAAAAAAA/wAA//8AAP8AAAAAAAAAAAAAAAAAAAAAAAAA/wAAAP8AAAD/AAAA/wAAAP8AAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP//AAD/AAAAAAAAAAAAAAAAAAAAAAAAAP8AAAD/AAAA/wAAAP8AAAD/AAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA/wAAAAAAAAAAAAAAAAAAAAAAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAAAAAAAAAAAAAAAAAAAAAAAA/wAA//8AAP8AAAAAAAAAAAAAAAAAAAAA/wAA/wAAAAAAAAAAAAAAAP8AAP8AAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP//AAD/AAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA//8AAP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD//wAA/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/wAA//8AAP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/AAD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD/AAAAAHg+AAB4PgAAeD4AAHg+AAB4PgAAeD4AAHg+AAB4PgAAeD4AAHg+AAB7vgAAfH4AAH7+AAB+/gAAAAAAAA==",__vite_glob_0_8="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAXElEQVR42u3T4QrAIAgEYO/Jdz25a2OEDE0j2K8JwQX5IVGQV5HUviDFQgSoiD4H8AObQIaEwJUrU5SAGeICdn+QIzeTXSB6hbNJkDV7iMVQaY6QG1j9POklfg6cdQxPkFXgw5cAAAAASUVORK5CYII=",__vite_glob_0_9="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAU0lEQVR42mP8DwQMOAAjI4QGq4Bx0NXgMgBFMwOJBmBoxiOJYQBezYQMIKiZ5gYQbQixgUiMQYxEpwNyDIAZMjAGEBsOOAOR7FigOBpJsZ0qBgAAKpJn1WUHkioAAAAASUVORK5CYII=",__vite_glob_0_10="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAjUlEQVR42r2S2w2AIAxF2ykcRGfSmXAmGcQprlgaBYJaYkJ/SB/30BaYjAaivRbnboASFIRDR4AD5FyYDQAtjhPyKd5mL960jgJ52YGKEcKsHEdkBCRiyUR3g78qBKCjFIBCfGWRAWpd3ABU1mHogh9v/4JkAHy85ksnHH4GyGKVfTQDyi7aAAkkB/y0A3u1UqJiMoY0AAAAAElFTkSuQmCC",__vite_glob_0_11="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA30lEQVR42mNcsuvIfwYsgIODA0z/+PGDAR9gBBnwXlgFLvDt6y+Gnz9/M/xnYvkKNuDrb+5zN18y7Ci1xm3ABzGNr8gGfP/xG67g+/d/DKevvfy/p8yCB6cB0a4I0xkZIfR/NI+BxMFiSAqW7j6KagA+zXBxQgb8xxKkKOLYDICFeIidMZhec+gsigG4xEExRHoYIAEML+BTjE0crwHEuAIjDPD5GSRGMAxAhjNhsR3ZRf+QxJfvwZEO/gIVMaE5HSb2D0l8OSnpAFuY4EyJ6C4A2coMNRyrCxiwAGKzMwB7WrvbVXBA/AAAAABJRU5ErkJggg==",__vite_glob_0_12="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAXklEQVR42tWSQQrAIAwE3f8/ei0SlISuMfRQFG864xBE+7hwepFj/ymIIhgrBAyvAQWBwbQj0EkSQYBngJeIGQjYSaQggTcVOII3FXhGwpyMFaukJngpKX8kMcSbBR00HyYNPyfRpQAAAABJRU5ErkJggg==",__vite_glob_0_13="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABCElEQVRIx+WVsXHDMAxFn+IMwHQplS6dtIHoDbKBUqa0JhA20ApZIx0zQdCq4wjUBml0PlkxdPRdUtjBHRscgU/i4wNw7Vacc4rIANQX5lIR6dbO+40AZ4E453DOEWM8Jrcee2f8oAMOFnJVVbRtu3R1InLIBvhN+3OAnVGiFnixOCiKgmmalhzgvX8IIWguya+At14VYzxJPt9/At5zSd5vATRNQ9/3S9deRPxtkmyVqN5S8ppgoJ5jyCV52OJAVVHV9f3PczFWibr5ZCvZUr5VIgW+zCHlHGVZrged/qMuuolhNwBvwKOlg3EcSSkddeC9fw4hfFyy0aa5t39YSmmZfHP9Xr99A/zFWXF94MojAAAAAElFTkSuQmCC",__vite_glob_0_14="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AzMzMzMzMzMzP///3/////M///39////8z///f39///zP//9/f3///M///39/f//8z///9/f///zP//9/f////M///39////8z///f3////zP//9/f////M///39////8z///f3////zP//9/f////M///3d////8zMzMzMzMzMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_15="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAR0lEQVR42mNkwA/+/0cTYAQjFP6wN4CQGkYiNOM1hGYGoGvCaQg+A4hSy4hDMyMBNu0MwOZ0vHqwGcBIAp9gSBMUo6oBZAEACDggERUn66AAAAAASUVORK5CYII=",__vite_glob_0_16="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AzMzMzMzMzMzP+Z/8+v///M/5//z6///8z/n//Pqv//zP+Z/8/6r//M//mfz//6/8z//5/P//qvzP//n8///6/M/5mfz//6r8z5n//P+qr/zPmf/8+q///M/5//yq///8z/mf/Kr///zP/5/8+q///M//+fz/qv/8zMzMzMzMzMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_17="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AiIiIiIiIjMiP//j//4/M+I//+P//jM/4j//4///M//iP//j//M//+IiIiIiMiIiIj//4/8yP//iP//j/z4//+I//+PzPj//4iIiIjIiIiIiP//jM/4//+I//+M//j//4j//4z/+P//iP//jP/4//+IiIiIiIiIiI//////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_18="data:image/x-icon;base64,AAABAAEAEBAQAAAABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8A7u7u537u7u7u7u7nfu7u7u7u7ud+7u7u7u7ufufu7u7u7u5+5+7u7u7ufn7n5+7u7u5+fufn7u7u7n5+5+fu7u7ufn7n5+7u7u5+fufn7u53d3d+53d3d/////////////////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",__vite_glob_0_19="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAYElEQVR42uWTUQ4AEAxD7WY72o62m/G1pKhJ7FO/ZPSpQVpRwopm1g91SQFhVFW6m7tvIEHzychAAZkAMb4lwBQ0AS5E4XwKeD5CCVC+BdZMpvUtUECW4AdA1sjrZ3rRAJmbXBH/l8xMAAAAAElFTkSuQmCC",__vite_glob_0_20="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZBJREFUSEvtlb9LVWEYxz/fYzo2mBfL1mYLCeIoQlEEDSENgtD/ENwrDtmgDtUgeqH+g6AlhwZpiaIa9DiIg4MQNKYlec7g6I/3G/dGIN1j557BIfJd3/d9Ps/z/T7v84oTXjrh+JwCChX+q0Q99WTAh76NdBfoRfQ2I5ptYBt7UR16u1ON144j5QLO1ZNbmEeY682L4qPtb0hbvwDuk3Th6D7icVqN3/0JygfMJavARcyT/cgvd2uDWV6GZ+eXuzuD7iMmgc10PL7aLsDAlqyZM11dr78/GPiRBzj/bK1ysLd3z/IU0JeOxy0JH1dBA/AFuNSWRPAeuFkKILQAegFhxHAZqIAqyMKkwA6wQeC5CXcURVOlAA5hJpsYmi7sQ6B7dmm6NADIZD10FN6ktcHNAg+eNjilKjjqgcQ61mfsryEitLTpf+jBfPIB01/KA7Ge1uIbbT20xqGeuZVXxqPNSVHgQaOlQzisZxNDSduAJmR2acyKhhFXcocd/uSoYzGrXmsJ/Bt0+uEUPvR/X6KfjaDiGeHqZWsAAAAASUVORK5CYII=",__vite_glob_0_21="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAVklEQVR42r2SSQoAMAgD4/8fbQ/dqFVILZiTohlwERxSBSWRFVnAhZi9o9BTFmDMbwDHzAMCMwdAbC4CfI9gl5i+Agwo9QcOpBjg7KHoE2dP5oygtAENDF9WEXnYM1cAAAAASUVORK5CYII=",__vite_glob_0_22="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAaUlEQVR42q2SSxKAMAhDyf0PjUo3HU0CozJdpemjfBCRGR8CCwDs4ikk6s5r19sHIOuU02oUoIw8+w3gHvPsBMBNwxIcQEMGJfheiCYqUAtQRg02gN08GqP67niMvwJYCe0e+Hm3e/A+DqB0c/EQNZBjAAAAAElFTkSuQmCC",__vite_glob_0_23="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAWklEQVR42t3SUQrAIAwD0Ob+h46brhvtKo76oawIgpgnASFCysSgAUAuTm4IsK67H1LAGVRIkRh2QBTymJ69AB/uvWrvOWDc2cI4tgrwyn0Fnnr85UdaBeSnAIEPVvFHhd1kAAAAAElFTkSuQmCC",__vite_glob_0_24="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAMFJREFUOE+tk8ENgjAUhv8XD87gBMDB1KRHYQMYQ1nAMZxAx4AN6JVEwoEygTN40KcVDBoPRWqP7Xtf0+/9JTguMv3+MpSYIfmJdUWma1V2ABFuASQglKMgDAkg05U69IB1ChA/NqWuVGqD+KKrHwCraANmD4Tc1vw8Z8QgavWpOP7JgbNE5yf0UqZLdB6jswMRxiDsRo3wVcTY60rln0G6oTTxtIG+gzRIhEnXBIDjXwiCaMFz9mw3v5/ThdqmKc53ACR3EUMIvNEAAAAASUVORK5CYII=",__vite_glob_0_25="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPpJREFUOE/lk01KA0EQhd/LBUyL3iA9CILZCDmJJ3DXEwKCIuM+IYwbIaR15QU8i4IJZDEdTxCYxr3pCgkJhPyMg1la237vK6peNXFgschffc5aDPzxsbb7dIUAZd0QkOBNVP8T4LifjUBKbvT5fwIo6z4IGeQn+lpNxp/z2f1pra4m41dSznITNdb3sZWCsu6BQBvAGyAXEAjIIYArESQ+1t1CwPxR2eyR4O26UARdH+tkM429d6D6rkeiuTQ85Ubf7Iryl0PKUginuzqvYIWAMt9kAVDWJQQ6ZQwrjYjc+zhKF4Cjl6/LyjSkICqlIIIQyLtvU3s/eIQZ+YpfEUh526gAAAAASUVORK5CYII=",__vite_glob_0_26="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAi5JREFUOE99k09IVGEUxX9nZhIVmlW4MHIWgYFQUdZKCNHRec8Q0v5solUWLmon1U4wDKKQgnZBFAUtLMNFvjcKMRTSMsSS2riwyEU7C0Zi5t2cN/PMsbG3ue+797vnu/eec0WNz1w3SRAcRjoYhs0WicUW5Hlr269rq8M6OxM0NIwD1yqJX0IrHcDMMLtFMjmqyclilLcJYD09zSQS00ATZiNI2ejFsCJwgbvAN4JgUNnsaogdVliyrvsOs2XW1y8rl1uv2Vp/fyOFwkPMWvD9EwIrA7juCGZDrKwc0tLS71rJkc/a2upIpZaAB/K8exHAKlKvZmYWQ0DHWUYakue9qZy7ke7I845WzseRXsjzUrJ0uoVEYkq+f2zzFccZAJ4SBCULsdgr4IJ8v2TL83XdRYrFbpnrnsFsWL6frmIkkzm5QePLChunlc2+roo7zlukCVkmc45Y7EZU3rYqnu8I4LofgHFZX18rQfAR368XBJUeB5CeUSyeCgHi8WnMzkctWHv7LpqaSky1lofoOL+AK/L9x5X+PgFXN4eYyaSRHsn3WyrxS8B9eV5jxMIYMMza2n7Nz//8L40dHbtJJlcIggllszcjIcVwnPdIn8nnLyqXK9QUUlnqT4AUnlcSUvBXyl1de6mrm0IyisWzmp39WjX13t59xOOToa9QGNTc3PfS77/LVF8/hnQd+AGUQOJA84by9gC3yedHt1ZYBbBFJNE6H0EKNgS1sNM6/wH04O4sooRIvQAAAABJRU5ErkJggg==",__vite_glob_0_27="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAYBJREFUOE+lk70vQ2EUxp/naklQH70SgxCTySAWY1lYJExiMLRaOtxaDMIiFaOESFr1cStNDB06+gN8DWIWk9FAolUhQei9R1p6aZs2pO96nvM75zznvESVj1XmoyzAu5Ucp3BUgA4ANwAPo5rzsLhgCWA6eu9QXpUEiJFisYgcdCZVdzBIMx8rAMzuiN34eDgjMVBhtBVdU4MlAPdGusVWa8QAjlX0RZDJsK4zpjXeZXW5DtyRdLfNME9AdP3JVMGaHlAXLIAvlNoH4SmXLIJzEM8Ehi2NqQzpc63HuQ58oeQdyPZ80ARGFJElkIMCbBr1zkXbS2oP4FReI4LTaEB1fQHCqUcAzVYQSNDAvEmlXwEvhUa82FiBPEe1tqZ8B0fZar9HyK4M4Cko6wQdJeMJrvSA2psDeEMPk6TE/2Tgj2hV19Rl6w584WREQD+/N1MJJpDrGpvat+vnS8EhzYSeeky8T5B0AWIvvUTeErjINDi3Yx6+WWv8Z+sF8qp/4yfBCn4RaedSVAAAAABJRU5ErkJggg==",__vite_glob_0_28="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1753847021771'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='8416'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M509.732571%2049.590857L60.708571%20262.802286%20509.732571%20476.16l448.950858-213.284571z'%20fill='%239A9C9B'%20p-id='8417'%3e%3c/path%3e%3cpath%20d='M60.708571%20262.802286v182.857143l172.763429%2030.427428%20103.570286%20152.356572%20172.617143%2030.427428v-182.857143z'%20fill='%239A9C9B'%20p-id='8418'%3e%3c/path%3e%3cpath%20d='M509.732571%20821.979429l138.093715-132.608%20172.690285%2030.427428%20138.166858-221.184v282.112L509.732571%20994.011429%2060.708571%20780.726857V498.614857l172.617143%20160.256%20103.643429%20152.356572%20172.617143%209.874285v0.877715z'%20fill='%239A9C9B'%20p-id='8419'%3e%3c/path%3e%3cpath%20d='M509.732571%20476.086857v182.857143l138.093715-121.929143%20172.690285%2030.500572%20138.166858-182.857143V262.802286z'%20fill='%239A9C9B'%20p-id='8420'%3e%3c/path%3e%3c/svg%3e",__vite_glob_0_29="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAdFJREFUOE+VkzFrk1EYhZ+TtFpsbPxSnMQirUEEFQsuHbSCLqKLizgobhZTlK5OOnVQcJAkdnCKkEH/gKPiIlQHK2TI1wpap1SaWofakn5HU0xIk9bqHc+597nvPe97xRYryH86LdcmECOYvUbTKD5RzQzOtG9Xu7AvVz4ZgzdIiVbPeE1odDGTftuqbwCCJ7PHcHRTcNRmWFL/VpUBpcXK4ePcV9TwlczOnYtp/aWkrm0ObZK9HjtRvT30sQlI5cJpxKkdDleMY6B+TEm4HMV4xc/uglK58o/297bDDEXZPb/DvCixu+FHcE9BPvwiONgQDTlQXI5GLVUwX+ue5B5bl1oBNp/rgKxgvAVQBI7Irlks4FgJfABICJ9H6m1WaJaVeBzu7+7iEfbVP0FWsPfUqzCu5zMASoJ7QSuCZPMy60FzDvqezqfiayvvhQ79LVDjb4JVo0LEroebBinIh3cFk9sBbArV8fSNjkFqCs8dTy2EL5Aud3bCM8TjZ6pjQ987AH1Tc+nlscHZetR1M5UNryBfQBoAz0fweqmmInfSqx1/IciHzwTXjD9Ub6WHG5B/mcqN9gb5cKkl2ZH2z7ITSEGuPCXpuu131Uz67P9W8AtPt6wUtl04tAAAAABJRU5ErkJggg==",__vite_glob_0_30="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAMBJREFUOE9jZKAQMFKonwFuwOnFQTMY/v/XABnIyMDwl4nhb7lR3KYzhCwAG7B/vwML7xOh31xCigzMrFx49fz59Y3h27v7V03j1ulALYMYoPrf4Le0uhMhC8HyT2/sY5B++5SFMWz1X7AL/l9IVWP4w3AT7HgiwP9//xkY2f/LMurPeQIxYL8DCwOv2m8i9CKUGL9nYWSEuWDUgEEQiOCoPJM2jeH/fy2iopKR4RKjyew8eEokShMORcQlPTw2AACBA1sRJUnWuAAAAABJRU5ErkJggg==",__vite_glob_0_31="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1753749457280'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='5056'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M429.824%20128c-60.672%200-109.824%2049.152-109.824%20109.824v64.512h164.7616c14.9504%200%2027.2384%2021.9136%2027.2384%2036.864H237.824c-60.672%200-109.824%2049.152-109.824%20109.824v145.2032c0%2060.672%2049.152%20109.824%20109.824%20109.824h45.312v-102.912c0-60.672%2048.7936-109.824%20109.4656-109.824h201.5744a109.5168%20109.5168%200%200%200%20109.8752-109.4656V237.824c0-60.672-49.152-109.824-109.8752-109.824h-164.352z%20m-27.648%2061.8496c15.36%200%2027.648%204.608%2027.648%2027.2384%200%2022.6816-12.288%2034.2016-27.648%2034.2016-14.9504%200-27.2384-11.52-27.2384-34.1504%200-22.6816%2012.288-27.2896%2027.2384-27.2896z'%20fill='%233C78AA'%20p-id='5057'%3e%3c/path%3e%3cpath%20d='M740.864%20320v102.912a109.4656%20109.4656%200%200%201-109.4656%20109.824H429.824a109.5168%20109.5168%200%200%200-109.824%20109.4144v144.0256A109.824%20109.824%200%200%200%20429.824%20896h164.352a109.824%20109.824%200%200%200%20109.824-109.7728v-64.512h-164.7616c-15.0016%200-27.2384-21.9136-27.2384-36.864h274.176a109.824%20109.824%200%200%200%20109.8752-109.8752V429.824a109.824%20109.824%200%200%200-109.8752-109.824h-45.312zM370.688%20493.312l-0.1536%200.1536c0.512-0.0512%200.9728%200%201.4848-0.1536H370.688z%20m251.136%20279.3984c15.0016%200%2027.2896%2011.52%2027.2896%2034.2016a27.2384%2027.2384%200%200%201-27.2896%2027.2384c-15.36%200-27.648-4.608-27.648-27.2384%200-22.6816%2012.288-34.2016%2027.648-34.2016z'%20fill='%23FDD835'%20p-id='5058'%3e%3c/path%3e%3c/svg%3e",__vite_glob_0_32="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1753751708025'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='12169'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M976%2080H496c-32%200-37.92-27.84-48-48-16-32-21.44-32-48-32H48a48%2048%200%200%200-48%2048v272h1024V128a48%2048%200%200%200-48-48z'%20fill='%23FFA000'%20p-id='12170'%3e%3c/path%3e%3cpath%20d='M0%20240m48%200l928%200q48%200%2048%2048l0%20608q0%2048-48%2048l-928%200q-48%200-48-48l0-608q0-48%2048-48Z'%20fill='%23FFCA28'%20p-id='12171'%3e%3c/path%3e%3cpath%20d='M128%20160h768a48%2048%200%200%201%2048%2048v32H80v-32a48%2048%200%200%201%2048-48zM320%20928h704v96H320zM544%20384h288v128H544zM432%20656h512v128H432z'%20fill='%23FFFFFF'%20p-id='12172'%3e%3c/path%3e%3cpath%20d='M640.48%20992V594.24l-133.12%20382.72h-104.32l179.04-514.88h58.4V400h7.52l40-80%2040%2080h6.08l1.44%2016v46.08h58.4l179.04%20514.88h-104.32l-133.12-379.2V992h-95.04z'%20fill='%2378ADD7'%20p-id='12173'%3e%3c/path%3e%3cpath%20d='M688%20355.68L718.08%20416H720v62.08h63.04l168%20482.88H880L720%20503.84V976h-64V499.52L496%20960.96h-70.56l168-482.88H656V416h1.44L688%20355.68m0-71.52l-28.64%2057.28L638.08%20384H624v62.08h-53.28l-7.52%2021.44-168%20483.04-14.72%2042.4h138.24l7.52-21.44L624%20688.96V1008h128V691.68l98.24%20280%207.52%2021.28h138.24l-14.72-42.4L812.8%20467.52l-7.52-21.44H752V384h-13.6l-21.28-42.56L688%20284.16z'%20fill='%23FFFFFF'%20p-id='12174'%3e%3c/path%3e%3cpath%20d='M352%20960.96h672V1024H352zM467.52%20688h440.96v63.04H467.52zM583.04%20415.04h210.08v63.04H583.04z'%20fill='%234073B4'%20p-id='12175'%3e%3c/path%3e%3cpath%20d='M719.52%20960.96H1024V1024H719.52z'%20fill='%234372A0'%20p-id='12176'%3e%3c/path%3e%3cpath%20d='M656.48%20960.96h63.04V1024h-63.04z'%20fill='%23357DB8'%20p-id='12177'%3e%3c/path%3e%3cpath%20d='M719.52%20688h188.96v63.04H719.52z'%20fill='%234372A0'%20p-id='12178'%3e%3c/path%3e%3cpath%20d='M656.48%20688h63.04v63.04h-63.04zM656.48%20415.04h63.04v63.04h-63.04z'%20fill='%23357DB8'%20p-id='12179'%3e%3c/path%3e%3cpath%20d='M719.52%20415.04h73.44v63.04h-73.44z'%20fill='%234372A0'%20p-id='12180'%3e%3c/path%3e%3c/svg%3e",__vite_glob_0_33="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAb0lEQVR42tWRQQ7AIAgE4eXiyym0IQHUqk08dE+K7CwRBBERMXyQ+BANUAptmWulOUBfw2j8T8Dge6cAM48gZwHZ3IOcAzyVvnhlCz4tn6UvABtAHtXfNdFCLP3u2QV4cwN4kxpt7LBuA8CCsll1AanamhEGPlVCAAAAAElFTkSuQmCC",_hoisted_1$4=["src","alt"],_hoisted_2$4=["src","alt"],_sfc_main$4=defineComponent({__name:"Icon",props:{name:{},size:{default:"md"},color:{},rotate:{},clickable:{type:Boolean,default:!1},iconClass:{},alt:{default:"icon"},customStyle:{}},emits:["click"],setup(e,{emit:t}){const o=e,d=t,r=computed(()=>o.name?.endsWith(".svg")),p=computed(()=>o.name?.match(/\.(png|jpg|jpeg|gif|webp)$/i)),u=computed(()=>o.iconClass&&!o.name),D=computed(()=>o.name?o.name.startsWith("http")||o.name.startsWith("/")?o.name:new URL(Object.assign({"../icon/tree/3dviewer.png":__vite_glob_0_0,"../icon/tree/CVSummaryCanvas.ico":__vite_glob_0_1,"../icon/tree/ChannelMatrix.png":__vite_glob_0_2,"../icon/tree/Folders.svg":__vite_glob_0_3,"../icon/tree/HeaderCanvas.ico":__vite_glob_0_4,"../icon/tree/PictureCanvasIcon.ico":__vite_glob_0_5,"../icon/tree/Run.png":__vite_glob_0_6,"../icon/tree/ShotcoreCanvasIcon.ico":__vite_glob_0_7,"../icon/tree/SurveyData.png":__vite_glob_0_8,"../icon/tree/TimeChannel.png":__vite_glob_0_9,"../icon/tree/TimeChannelMatrix.png":__vite_glob_0_10,"../icon/tree/TimeChannelMulti.png":__vite_glob_0_11,"../icon/tree/TimechannelWave.png":__vite_glob_0_12,"../icon/tree/ToolString.png":__vite_glob_0_13,"../icon/tree/ToolStringCanvasIcon.ico":__vite_glob_0_14,"../icon/tree/Well.png":__vite_glob_0_15,"../icon/tree/WellLogCanvas.ico":__vite_glob_0_16,"../icon/tree/WellProfileCanvasIcon.ico":__vite_glob_0_17,"../icon/tree/WellSketchCanvas.ico":__vite_glob_0_18,"../icon/tree/Wellbore.png":__vite_glob_0_19,"../icon/tree/buttonAddDataset.png":__vite_glob_0_20,"../icon/tree/channel.png":__vite_glob_0_21,"../icon/tree/channelMulti.png":__vite_glob_0_22,"../icon/tree/channelWave.png":__vite_glob_0_23,"../icon/tree/data-set.png":__vite_glob_0_24,"../icon/tree/download.png":__vite_glob_0_25,"../icon/tree/downloadFailed.png":__vite_glob_0_26,"../icon/tree/downloadSuccess.png":__vite_glob_0_27,"../icon/tree/geo.svg":__vite_glob_0_28,"../icon/tree/null.png":__vite_glob_0_29,"../icon/tree/presentation.png":__vite_glob_0_30,"../icon/tree/python.svg":__vite_glob_0_31,"../icon/tree/wells.svg":__vite_glob_0_32,"../icon/tree/wellsite.png":__vite_glob_0_33})[`../icon/tree/${o.name}`],import.meta.url).href:""),v=computed(()=>!o.color||!r.value?void 0:{white:"brightness(0) invert(1)",black:"brightness(0)",primary:"hue-rotate(210deg) saturate(2)",success:"hue-rotate(120deg) saturate(2)",warning:"hue-rotate(45deg) saturate(2)",danger:"hue-rotate(0deg) saturate(2)"}[o.color]||`hue-rotate(${o.color})`),T=computed(()=>{const V={...o.customStyle};return typeof o.size=="number"&&(V.width=`${o.size}px`,V.height=`${o.size}px`),o.rotate&&(V.transform=`rotate(${o.rotate}deg)`),V}),z=V=>{o.clickable&&d("click",V)};return(V,y)=>(openBlock(),createElementBlock("span",{class:normalizeClass(["icon-wrapper",[`icon-size-${V.size}`,{"icon-clickable":V.clickable}]]),style:normalizeStyle(T.value),onClick:z},[r.value?(openBlock(),createElementBlock("img",{key:0,src:D.value,alt:V.alt,class:"icon-svg",style:normalizeStyle({filter:v.value})},null,12,_hoisted_1$4)):p.value?(openBlock(),createElementBlock("img",{key:1,src:D.value,alt:V.alt,class:"icon-image"},null,8,_hoisted_2$4)):u.value?(openBlock(),createElementBlock("i",{key:2,class:normalizeClass(V.iconClass)},null,2)):renderSlot(V.$slots,"default",{key:3},void 0,!0)],6))}}),__unplugin_components_0=_export_sfc(_sfc_main$4,[["__scopeId","data-v-b74f6e1d"]]),_hoisted_1$3={key:0,class:"selection-actions"},_hoisted_2$3={class:"selection-info"},_hoisted_3$2={class:"dialog-footer"},_sfc_main$3=Object.assign({name:"ImportModelDialog"},{__name:"importModelDialog",props:{modelValue:{type:Boolean,default:!1},currentNode:{type:Object,default:()=>null},treeData:{type:Array,default:()=>[]},apiBaseUrl:{type:String,default:()=>window.location.protocol+"//"+window.location.host+"/api"}},emits:["update:modelValue","model-imported","tree-save-required","addModel"],setup(e,{emit:t}){const o=e,d=t,r=ref(null),p=ref(!1),u=ref([]),D=ref([]),v=ref(!1),T=ref([]);computed(()=>u.value.length>0&&D.value.length===u.value.length);const z=computed({get(){return o.modelValue},set(S){d("update:modelValue",S)}});watch(z,S=>{S&&y()});const V=()=>{!r.value||!u.value.length||u.value.forEach(S=>{T.value.includes(S.id)&&r.value.toggleRowSelection(S,!0)})},y=async()=>{v.value=!0;try{const S=o.treeData[0]?.id;if(!S)throw new Error("未找到油田ID");const K=M();T.value=K.map(se=>se.id);const f=await axios.get(`${o.apiBaseUrl}/visual3D/project/GetModelList?oilfieldId=${S}`);if(f.data?.success)u.value=f.data.data,await nextTick(),V();else throw new Error(f.data?.message||"获取模型列表失败")}catch(S){console.error("获取模型列表错误:",S),ElMessage.error(S.message||"获取模型列表失败")}finally{v.value=!1}},G=S=>S?new Date(S).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",te=S=>{D.value=S},B=()=>{z.value=!1,D.value=[],r.value?.clearSelection()},L=S=>{const K=se=>{if(!Array.isArray(se))return null;for(const le of se){if(le.moduleType===1)return le.children||[];if(le.children&&le.children.length>0){const X=K(le.children);if(X!==null)return X}}return null};return K(S)||[]},M=()=>L(o.treeData),$=async()=>{if(!p.value)try{if(D.value.length===0){ElMessage.warning("请至少选择一个模型");return}p.value=!0,o.currentNode.children||(o.currentNode.children=[]);const S=[],K=[];for(const f of D.value){const se={id:f.id,label:f.name,moduleType:9,children:[]};K.push(se)}d("addModel",K),d("tree-save-required"),B()}catch(S){console.error("导入模型错误:",S),ElMessage.error(S.message||"导入模型失败")}finally{p.value=!1}};return(S,K)=>{const f=resolveComponent("el-table-column"),se=resolveComponent("el-table"),le=resolveComponent("el-button"),X=resolveComponent("el-dialog"),J=resolveDirective("loading");return openBlock(),createBlock(X,{title:"Load the model",modelValue:z.value,"onUpdate:modelValue":K[0]||(K[0]=P=>z.value=P),width:"700px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:B,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_3$2,[createVNode(le,{onClick:B},{default:withCtx(()=>K[1]||(K[1]=[createTextVNode(" Cancel ")])),_:1}),createVNode(le,{type:"primary",loading:p.value,onClick:$},{default:withCtx(()=>[createTextVNode(toDisplayString(p.value?"Importing...":"Confirm"),1)]),_:1},8,["loading"])])]),default:withCtx(()=>[withDirectives((openBlock(),createBlock(se,{ref_key:"tableRef",ref:r,data:u.value,style:{width:"100%",height:"300px"},onSelectionChange:te},{default:withCtx(()=>[createVNode(f,{type:"selection",width:"55"}),createVNode(f,{prop:"name",label:"Model Name",width:"120"}),createVNode(f,{prop:"category",label:"Category",width:"100"}),createVNode(f,{prop:"remark",label:"Remark",width:"120","show-overflow-tooltip":""},{default:withCtx(({row:P})=>[createTextVNode(toDisplayString(P.remark||"-"),1)]),_:1}),createVNode(f,{prop:"createdOn",label:"Created On",width:"150"},{default:withCtx(({row:P})=>[createTextVNode(toDisplayString(G(P.createdOn)),1)]),_:1}),createVNode(f,{prop:"owner",label:"Owner",width:"100"},{default:withCtx(({row:P})=>[createTextVNode(toDisplayString(P.owner||"-"),1)]),_:1})]),_:1},8,["data"])),[[J,v.value]]),u.value.length>0?(openBlock(),createElementBlock("div",_hoisted_1$3,[createBaseVNode("span",_hoisted_2$3," Selected "+toDisplayString(D.value.length)+" / "+toDisplayString(u.value.length)+" Item ",1)])):createCommentVNode("",!0)]),_:1},8,["modelValue"])}}}),ImportModelDialog=_export_sfc(_sfc_main$3,[["__scopeId","data-v-552fab7d"]]),_sfc_main$2={name:"ProjectTree",components:{Icon:__unplugin_components_0,ContextMenu:__unplugin_components_1,curvePreview:_sfc_main$5,ImportModelDialog,...ElementPlusIconsVue},data(){return{customMenuItems:[{label:"New",action:"create",children:[{label:"Import Well",action:"importWell",moduleType:0},{label:"Import Model",action:"importModel",moduleType:1},{label:"Import Wellbore",action:"importWellbore",moduleType:2},{label:"Import Curve",action:"importCurve",moduleType:4}]},{label:"Rename",action:"rename"},{label:"Delete",action:"delete"},{label:"Refresh",action:"refresh"}],defaultProps:{children:"children",label:"label"},form:{oilfieldId:"",projectName:"",note:""},rules:{oilfieldId:[{required:!0,message:"Please select oil field",trigger:"change"}],projectName:[{required:!0,message:"Please enter project name",trigger:"blur"},{min:2,max:50,message:"Length should be 2 to 50 characters",trigger:"blur"}],note:[{max:200,message:"Length should not exceed 200 characters",trigger:"blur"}]},treeData:[],dialogVisible:!1,isConfirmDisabled_createProject:!1,renameDialogVisible:!1,renameForm:{newName:""},currentNode:null,isLoading:!1,isLoadingOilfields:!1,appId:"",id:"",oilfields:[],vWebApiUrl:window.location.protocol+"//"+window.location.host+"/api",renameRules:{newName:[{required:!0,message:"请输入新名称",trigger:"blur"},{min:2,max:50,message:"长度应为2至50个字符",trigger:"blur"}]},importWellDialogVisible:!1,isImportingWell:!1,isLoadingWells:!1,wells:[],importWellForm:{wellId:""},importWellRules:{wellId:[{required:!0,message:"请选择井",trigger:"change"}]},importWellboreDialogVisible:!1,isImportingWellbore:!1,isLoadingWellbores:!1,wellbores:[],importWellboreForm:{wellboreId:""},importWellboreRules:{wellboreId:[{required:!0,message:"请选择井眼",trigger:"change"}]},importCurveDialogVisible:!1,isImportingCurve:!1,isLoadingDatasets:!1,isLoadingCurves:!1,datasets:[],curves:[],importCurveForm:{datasetId:"",curveId:""},importCurveRules:{datasetId:[{required:!0,message:"请选择数据集",trigger:"change"}],curveId:[{required:!0,message:"请选择曲线",trigger:"change"}]},checkedNodes:[],checkedNodeIds:[],importModelDialogVisible:!1,settingDialogVisible:!1,settingForm:{wellboreThickness:null,displayMode:"normal",min:null,max:null,minCalibrate:null,maxCalibrate:null,minColor:"#FF0000",maxColor:"#FF0000"},settingCurrentNode:null,settingCache:{},chooseDatasetDialogVisible:!1,isChoosingDataset:!1,isLoadingDatasetsForChoose:!1,datasetsForChoose:[],chooseDatasetForm:{datasetId:""},chooseDatasetRules:{datasetId:[{required:!0,message:"请选择数据集",trigger:"change"}]},curveTitle:"",curvePreviewDialog:!1}},methods:{handleNodeClick(e){console.log("点击节点:",e),this.currentNode=e,this.$refs.tree.setCurrentKey(e.id)},handleAction(e){switch(e){case"createProject":window.open(window.location.protocol+"//"+window.location.host+"/static/app/#/3dviews/?appId="+this.appId);break;case"importWell":if(this.currentNode.moduleType!==0){this.$message.error("请选择工程节点");return}else this.handleMenuAction("importWell",this.currentNode);break;case"importWellbore":if(this.currentNode.moduleType!==2){this.$message.error("请选择井节点");return}else this.handleMenuAction("importWellbore",this.currentNode);break;case"importCurve":if(this.currentNode.moduleType!==4){this.$message.error("请选择井眼节点");return}else this.handleMenuAction("importCurve",this.currentNode);break;default:console.log(e+"操作...");break}},getIcon(e){return{0:"Folders.svg",1:"geo.svg",2:"Well.png",4:"Wellbore.png",5:"data-set.png",6:"SurveyData.png",7:"channel.png",8:"Well.png",9:"geo.svg",10:"3dviewer.png"}[e.moduleType]||"Folders.svg"},async saveTree(e=!0){try{const o=new URLSearchParams(window.location.hash.split("?")[1]).get("id");if(!this.appId||!o)throw new Error("缺少必要的参数");let d=[],r=[];e&&(d=this.$refs.tree?this.$refs.tree.getCheckedKeys():[],r=this.$refs.tree?this.$refs.tree.getHalfCheckedKeys():[]),console.log("保存时的选中状态:",{checkedKeys:d,halfCheckedKeys:r,checkedNodeIds:this.checkedNodeIds,saveCheckedState:e});const p=this.addCheckedInfoToTreeData(this.treeData,d,r);return await axios.post(`${this.vWebApiUrl}/project/project/SaveTree`,{appId:this.appId,projectId:o,treeData:p})}catch(t){throw console.error("保存树结构错误:",t),t}},handleContextMenu(e,t){e.preventDefault(),this.currentNode=t;const o={0:{label:"Add Well",action:"importWell",moduleType:0},1:{label:"Add Model",action:"importModel",moduleType:1},4:{label:"Add Curve",action:"importCurve",moduleType:4},6:{label:"Setting",action:"setting",moduleType:6},7:{label:"Curve Preview",action:"curvePreview",moduleType:7},10:{label:"Choose Dataset",action:"chooseDataset",moduleType:10}},d=[{label:"Refresh",action:"refresh"}];t.moduleType!==0&&t.moduleType!==1&&d.splice(1,0,{label:"Delete",action:"delete"});const r=t.moduleType;if(r!==void 0&&o[r]?r===7?this.customMenuItems=[o[r],o[6],...d]:this.customMenuItems=[o[r],...d]:this.customMenuItems=d,r===7){const p=this.getWellNodeId(t),u={...t,wellNodeId:p,nodeDataId:t?.id};this.$refs.contextMenu.show(e,u)}else this.$refs.contextMenu.show(e,t)},handleMenuAction(e,t){switch(console.log("Menu action:",e,"Node:",t),t||(t=this.currentNode,console.log("Using currentNode as fallback:",t)),e){case"delete":this.handleDelete(t);break;case"refresh":this.loadTreeData();break;case"importWell":case"importModel":case"importWellbore":case"importCurve":if(!t){this.$message.error("请选择一个节点");return}this.handleImport(e,t);break;case"setting":this.showSettingDialog(t);break;case"curvePreview":this.showCurvePrivew(t);break;case"chooseDataset":this.showChooseDatasetDialog(t);break}},showRenameDialog(e){if(!e){console.error("No node provided for rename"),this.$message.error("无法重命名：节点信息不完整");return}console.log("Node to rename (full):",e),this.currentNode=e;let t="";typeof e.label=="string"?t=e.label:e.data&&typeof e.data.label=="string"&&(t=e.data.label),t||console.warn("无法获取节点名称，使用空字符串"),this.renameForm.newName=t,this.renameDialogVisible=!0},showCurvePrivew(e){this.curveTitle=`${e.label} Preview`,this.curvePreviewDialog=!0,this.$nextTick(()=>{const t=usePreviewStore();t.setChannelId(e.id),t.setNodeInfo(e),t.setProjectId(this.projectId||""),t.setView(!0),t.setPreviewCurve(!0)})},handleCurvePreviewDialogClose(){const e=usePreviewStore();e.setView(!1),e.setPreviewCurve(!1),e.setChannelId(""),e.setNodeInfo({}),e.setProjectId("")},async handleRename(){if(!this.currentNode||!this.renameForm.newName.trim()){this.$message.warning("Please enter a valid name");return}try{const e=this.currentNode,t=this.renameForm.newName.trim(),o=await this.saveTree();if(o.data?.success){const d=o.data.data,p=`${window.location.hash.split("?")[0]}?id=${d.id}&appId=${this.appId}`;window.location.hash=p,await this.reloadTree(),this.$message.success("Node renamed successfully"),this.renameDialogVisible=!1}else throw new Error(o.data?.message||"Failed to rename node")}catch(e){console.error("Error renaming node:",e),this.$message.error(e.message||"Failed to rename node")}},async handleDelete(e){if(!e){console.error("No node provided for delete"),this.$message.error("无法删除：节点信息不完整");return}this.$confirm("此操作将永久删除该节点, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{this.removeNodeFromTree(this.treeData,e.id),this.removeDeletedNodeFromChecked(e.id);const t=await this.saveTree();if(t.data?.success)await this.reloadTree(),this.$message.success("删除成功");else throw new Error(t.data?.message||"删除失败")}catch(t){console.error("删除节点错误:",t),this.$message.error(t.message||"删除失败")}}).catch(()=>{this.$message.info("已取消删除")})},removeNodeFromTree(e,t){for(let o=0;o<e.length;o++){if(e[o].id===t)return e.splice(o,1),!0;if(e[o].children&&e[o].children.length>0&&this.removeNodeFromTree(e[o].children,t))return!0}return!1},removeDeletedNodeFromChecked(e){const t=this.checkedNodeIds.indexOf(e);t>-1&&(this.checkedNodeIds.splice(t,1),console.log(`已从选中数组中移除节点ID: ${e}`))},findParent(e,t){for(const o of e)if(o.children&&o.children.length>0){if(o.children.findIndex(r=>r.id===t)!==-1)return o;{const r=this.findParent(o.children,t);if(r)return r}}return null},getWellNodeId(e){let t=e.id,o=e;if(o.moduleType===2)return o.nodeDataId||o.id;for(;t;){const d=this.findParent(this.treeData,t);if(d){if(d.moduleType===2)return d.nodeDataId||d.id;t=d.id}else break}return null},clearAllCheckedState(){this.checkedNodeIds=[],this.$refs.tree&&this.$refs.tree.setCheckedKeys([]),console.log("已清空所有选中状态")},getCheckedNodeIds(){return[...this.checkedNodeIds]},async handleImport(e,t){switch(e){case"importWell":this.showImportWellDialog(t);break;case"importWellbore":this.showImportWellboreDialog(t);break;case"importCurve":this.showImportCurveDialog(t);break;case"importModel":this.showImportModelDialog(t);break}},async showImportWellDialog(e){this.currentNode=e,this.importWellDialogVisible=!0,await this.loadWells()},async loadWells(){this.isLoadingWells=!0;try{const e=this.treeData[0]?.id;if(!e)throw new Error("未找到油田ID");const t=await axios.get(`${this.vWebApiUrl}/oil/oilwell/GetWellListAllByOilFieldId?oilFieldId=${e}`);if(t.data?.success)this.wells=t.data.data;else throw new Error(t.data?.message||"获取井列表失败")}catch(e){console.error("获取井列表错误:",e),this.$message.error(e.message||"获取井列表失败")}finally{this.isLoadingWells=!1}},handleImportWellDialogClose(){this.importWellDialogVisible=!1,this.importWellForm.wellId="",this.$refs.importWellFormRef?.resetFields()},async handleImportWellConfirm(){if(!this.isImportingWell)try{await this.$refs.importWellFormRef.validate(),this.isImportingWell=!0;const e=this.wells.find(u=>u.id===this.importWellForm.wellId);if(!e)throw new Error("未找到选中的井信息");const t=await axios.get(`${this.vWebApiUrl}/oil/oilwellbore/GetWellboreByWellId?wellId=${e.id}`);if(!t.data?.success)throw new Error(t.data?.message||"获取井眼数据失败");const o=t.data.data.map(u=>({id:u.id,label:u.wellboreNumber,moduleType:4,children:[{id:crypto.randomUUID(),wellboreId:u.id,label:"实际轨迹",moduleType:6},{id:crypto.randomUUID(),wellboreId:u.id,label:"设计轨迹",moduleType:6},{id:crypto.randomUUID(),wellboreId:u.id,label:"插值成像",moduleType:10,datasetId:null}]})),d={id:e.id,label:e.wellName,moduleType:2,children:[]};if(d.children=o,this.currentNode.children||(this.currentNode.children=[]),this.currentNode.children.find(u=>u.id===d.id))throw new Error("该井已存在于当前节点下");this.currentNode.children.push(d);const p=await this.saveTree(!1);if(p.data?.success)await this.reloadTreeWithoutRestoringChecked(),this.$message.success("导入井成功"),this.handleImportWellDialogClose();else throw new Error(p.data?.message||"导入井失败")}catch(e){console.error("导入井错误:",e),this.$message.error(e.message||"导入井失败")}finally{this.isImportingWell=!1}},handleRenameDialogClose(){this.renameDialogVisible=!1,this.$refs.renameFormRef.resetFields()},showCreateProjectPrompt(){this.$msgbox({message:"No project found. Would you like to create a new one?",title:"Prompt",showCancelButton:!0,closeOnClickModal:!1,confirmButtonText:"Yes",cancelButtonText:"No"}).then(()=>{this.dialogVisible=!0}).catch(()=>{console.log("User cancelled")})},async getOilFieldList(){this.isLoadingOilfields=!0;try{const e=await axios.get(`${this.vWebApiUrl}/oil/oilfield/ListAll`);if(e.data?.success)this.oilfields=e.data.data;else throw new Error(e.data?.message||"Failed to get oil fields")}catch(e){console.error("Error getting oil fields:",e),this.$message.error(e.message||"Failed to get oil fields")}finally{this.isLoadingOilfields=!1}},async createProject(){if(!this.isConfirmDisabled_createProject)try{await this.$refs.formRef.validate(),this.isConfirmDisabled_createProject=!0;const{projectName:e,note:t,oilfieldId:o}=this.form,r=new URLSearchParams(window.location.hash.split("?")[1]).get("appId");if(!r)throw new Error("Missing required appId parameter");if(!e?.trim())throw new Error("Project name cannot be empty");if(!o)throw new Error("Please select oil field");const p=`${this.vWebApiUrl}/visual3D/project/CreateProject?appId=${encodeURIComponent(r)}&projectName=${encodeURIComponent(e.trim())}&remark=${encodeURIComponent(t?.trim()||"")}&oilfieldId=${encodeURIComponent(o)}`,u=await axios.post(p);if(u.data?.success){u.data.data?.profile&&(this.treeData=JSON.parse(u.data.data.profile));const v=`${window.location.hash.split("?")[0]}?id=${u.data.data.id}&appId=${r}`;window.location.hash=v,this.dialogVisible=!1,this.$refs.formRef.resetFields(),this.$message.success("Project created successfully")}else throw new Error(u.data?.message||"Failed to create project")}catch(e){console.error("Error creating project:",e),this.$message.error(e.message||"Failed to create project")}finally{this.isConfirmDisabled_createProject=!1}},handleDialogClose(){this.dialogVisible=!1,this.$refs.formRef.resetFields()},async loadTreeData(){this.isLoading=!0;try{const e=new URLSearchParams(window.location.hash.split("?")[1]),t=e.get("appId"),o=e.get("id");if(!t)throw new Error("Missing required appId parameter");if(!o)throw new Error("Missing required projectId parameter");const d=axios.get(`${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(t)}&projectId=${encodeURIComponent(o)}`).then(r=>{if(r.data?.success)try{const p=JSON.parse(r.data.data.profile),{cleanTreeData:u,checkedKeys:D,checkedNodeIds:v}=this.extractCheckedInfoFromTreeData(p);console.log("从treeData中提取的选中状态:",{checkedKeys:D,checkedNodeIds:v}),this.treeData=u,this.checkedNodeIds=v,D.length>0&&(console.log("checkedKeys",D),this.$refs.tree.setCheckedKeys(D),console.log("已恢复树组件选中状态")),this.treeData?.length>0&&this.$refs.tree.setCurrentKey(this.treeData[0].id)}catch{throw new Error("Invalid tree data format")}else throw new Error(r.data?.message||"Failed to load tree data")})}catch(e){console.error("Error loading tree data:",e),this.$message.error(e.message||"Failed to load tree data")}finally{this.isLoading=!1}},async reloadTree(){try{const t=new URLSearchParams(window.location.hash.split("?")[1]).get("id");if(!this.appId||!t)throw new Error("缺少必要的参数");const o=await axios.get(`${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(this.appId)}&projectId=${encodeURIComponent(t)}`);if(o.data?.success){const d=JSON.parse(o.data.data.profile),{cleanTreeData:r,checkedKeys:p,checkedNodeIds:u}=this.extractCheckedInfoFromTreeData(d);console.log("重新加载时从treeData中提取的选中状态:",{checkedKeys:p,checkedNodeIds:u}),this.treeData=r,this.checkedNodeIds=u,await this.$nextTick(),p.length>0&&(this.$refs.tree.setCheckedKeys(p),console.log("重新加载时已恢复树组件选中状态")),this.treeData?.length>0&&this.$refs.tree.setCurrentKey(this.treeData[0].id)}else throw new Error(o.data?.message||"加载树结构失败")}catch(e){console.error("重新加载树结构错误:",e),this.$message.error(e.message||"重新加载树结构失败")}},async reloadTreeWithoutRestoringChecked(){try{const t=new URLSearchParams(window.location.hash.split("?")[1]).get("id");if(!this.appId||!t)throw new Error("缺少必要的参数");const o=this.$refs.tree?this.$refs.tree.getCheckedKeys():[],d=this.$refs.tree?this.$refs.tree.getHalfCheckedKeys():[];console.log("保存当前选中状态:",{currentCheckedKeys:o,currentHalfCheckedKeys:d,checkedNodeIds:this.checkedNodeIds});const r=await axios.get(`${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(this.appId)}&projectId=${encodeURIComponent(t)}`);if(r.data?.success){const p=JSON.parse(r.data.data.profile),{cleanTreeData:u,checkedKeys:D,checkedNodeIds:v}=this.extractCheckedInfoFromTreeData(p);if(console.log("导入新节点后重新加载，保持当前选中状态:",{checkedKeys:D,checkedNodeIds:v}),this.treeData=u,await this.$nextTick(),o.length>0){this.$refs.tree.setCheckedKeys(o),console.log("已恢复当前选中状态，选中的节点ID:",o),this.checkedNodeIds=[...o],console.log("已更新checkedNodeIds数组:",this.checkedNodeIds);const T=this.$refs.tree.getCheckedKeys();console.log("恢复后的选中状态验证:",{original:o,restored:T,isEqual:JSON.stringify(o.sort())===JSON.stringify(T.sort())})}else console.log("没有需要恢复的选中状态"),this.checkedNodeIds=[];this.treeData?.length>0&&this.$refs.tree.setCurrentKey(this.treeData[0].id)}else throw new Error(r.data?.message||"加载树结构失败")}catch(e){console.error("重新加载树结构错误:",e),this.$message.error(e.message||"重新加载树结构失败")}},addCheckedInfoToTreeData(e,t,o){const d=JSON.parse(JSON.stringify(e)),r={_checkedInfo:{checkedKeys:t,halfCheckedKeys:o,checkedNodeIds:this.checkedNodeIds},data:d};return console.log("添加选中信息后的树数据:",r),r},extractCheckedInfoFromTreeData(e){let t=e,o=[],d=[];return e._checkedInfo&&e.data?(o=e._checkedInfo.checkedKeys||[],d=e._checkedInfo.checkedNodeIds||[],t=e.data,console.log("从元数据中提取选中信息:",{checkedKeys:o,checkedNodeIds:d})):console.log("未找到选中信息元数据，使用默认值"),{cleanTreeData:t,checkedKeys:o,checkedNodeIds:d}},triggerCheckedEvents(){if(this.$refs.tree){const e=this.$refs.tree.getCheckedNodes();console.log("触发已选中节点的事件:",e),e.forEach(t=>{this.handleCheckForRestore(t,!0)})}},handleCheckForRestore(e,t){if(console.log("恢复选中状态时触发事件, data:",e,"isChecked:",t),e.moduleType===6){const o=e.wellboreId;let d;e.label==="实际轨迹"?d="1":e.label==="设计轨迹"&&(d="0"),this.$emit("checkbox-choose",{id:o,curveType:d,isChecked:t,moduleType:e.moduleType,wellboreId:""})}else if(e.moduleType===9){const o=e.id;this.$emit("checkbox-choose",{id:o,curveType:"99",isChecked:t,moduleType:e.moduleType,wellboreId:""})}else if(e.moduleType===7){const o=e.id,d=e.wellboreId;this.$emit("checkbox-choose",{id:o,curveType:"99",isChecked:t,moduleType:e.moduleType,wellboreId:d})}else if(e.moduleType===10){const o=e.id,d=e.wellboreId;e.datasetId&&this.$emit("checkbox-choose",{id:o,curveType:"99",isChecked:t,moduleType:e.moduleType,wellboreId:d,datasetId:e.datasetId})}},async showImportWellboreDialog(e){this.currentNode=e,this.importWellboreDialogVisible=!0,await this.loadWellbores()},async loadWellbores(){this.isLoadingWellbores=!0;try{const e=await axios.get(`${this.vWebApiUrl}/oil/oilwellbore/GetWellboreByWellId?wellId=${this.currentNode.id}`);if(e.data?.success)this.wellbores=e.data.data;else throw new Error(e.data?.message||"获取井眼列表失败")}catch(e){console.error("获取井眼列表错误:",e),this.$message.error(e.message||"获取井眼列表失败")}finally{this.isLoadingWellbores=!1}},handleImportWellboreDialogClose(){this.importWellboreDialogVisible=!1,this.importWellboloadTreeDatareForm.wellboreId="",this.$refs.importWellboreFormRef?.resetFields()},async handleImportWellboreConfirm(){if(!this.isImportingWellbore)try{await this.$refs.importWellboreFormRef.validate(),this.isImportingWellbore=!0;const e=this.wellbores.find(r=>r.id===this.importWellboreForm.wellboreId);if(!e)throw new Error("未找到选中的井眼信息");const t={id:e.id,label:e.wellboreNumber,moduleType:4,children:[{id:crypto.randomUUID(),label:"实际轨迹",moduleType:6},{id:crypto.randomUUID(),label:"设计轨迹",moduleType:6},{id:crypto.randomUUID(),label:"插值成像",moduleType:10}]};if(this.currentNode.children||(this.currentNode.children=[]),this.currentNode.children.find(r=>r.id===t.id))throw new Error("该井眼已存在于当前节点下");this.currentNode.children.push(t);const d=await this.saveTree(!1);if(d.data?.success)await this.reloadTreeWithoutRestoringChecked(),this.$message.success("导入井眼成功"),this.handleImportWellboreDialogClose();else throw new Error(d.data?.message||"导入井眼失败")}catch(e){console.error("导入井眼错误:",e),this.$message.error(e.message||"导入井眼失败")}finally{this.isImportingWellbore=!1}},async showImportCurveDialog(e){this.currentNode=e,this.importCurveDialogVisible=!0,await this.loadDatasets()},async loadDatasets(){this.isLoadingDatasets=!0;try{const e=await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetDatasetListByWellboreId?wellboreId=${this.currentNode.id}`);if(e.data?.success)this.datasets=e.data.data;else throw new Error(e.data?.message||"获取数据集列表失败")}catch(e){console.error("获取数据集列表错误:",e),this.$message.error(e.message||"获取数据集列表失败")}finally{this.isLoadingDatasets=!1}},async handleDatasetChange(e){this.importCurveForm.curveId="",e?await this.loadCurves(e):this.curves=[]},async loadCurves(e){this.isLoadingCurves=!0;try{const t=await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetChannelList?datasetId=${e}&&wellboreId=${this.currentNode.id}`);if(t.data?.success)this.curves=t.data.data;else throw new Error(t.data?.message||"获取曲线列表失败")}catch(t){console.error("获取曲线列表错误:",t),this.$message.error(t.message||"获取曲线列表失败")}finally{this.isLoadingCurves=!1}},handleImportCurveDialogClose(){this.importCurveDialogVisible=!1,this.importCurveForm.datasetId="",this.importCurveForm.curveId="",this.curves=[],this.$refs.importCurveFormRef?.resetFields()},async handleImportCurveConfirm(){if(!this.isImportingCurve)try{await this.$refs.importCurveFormRef.validate(),this.isImportingCurve=!0;const e=this.datasets.find(r=>r.id===this.importCurveForm.datasetId),t=this.curves.find(r=>r.id===this.importCurveForm.curveId);if(!e||!t)throw new Error("未找到选中的数据集或曲线信息");const o={id:t.id,label:t.name+"("+e.name+")",moduleType:7,wellboreId:this.currentNode.id,datasetId:e.id,children:[]};this.currentNode.children.push(o);const d=await this.saveTree(!1);if(d.data?.success)await this.reloadTreeWithoutRestoringChecked(),this.$message.success("导入曲线成功"),this.handleImportCurveDialogClose();else throw new Error(d.data?.message||"导入曲线失败")}catch(e){console.error("导入曲线错误:",e),this.$message.error(e.message||"导入曲线失败")}finally{this.isImportingCurve=!1}},generateProjectName(){const e=this.oilfields.find(d=>d.id===this.form.oilfieldId),t=e?e.cOilFieldName:"",o=Math.random().toString(36).substring(2,8);this.form.projectName=`三维可视化_${t}_${o}`},handleCheck(e,t){console.log("ProjectTree handleCheck被调用, data:",e),console.log("data.moduleType:",e.moduleType),console.log("checked对象:",{checkedNodes:t.checkedNodes.length,checkedKeys:t.checkedKeys.length}),console.log("checked.checkedNodes是否包含data:",t.checkedNodes.includes(e));const o=t.checkedNodes.includes(e);if(o)this.checkedNodeIds.includes(e.id)||this.checkedNodeIds.push(e.id);else{const d=this.checkedNodeIds.indexOf(e.id);d>-1&&this.checkedNodeIds.splice(d,1)}if(console.log("当前选中的节点ID数组:",this.checkedNodeIds),e.moduleType===6){const d=e.wellboreId;let r;e.label==="实际轨迹"?r="1":e.label==="设计轨迹"&&(r="0"),console.log("轨迹节点选中状态:",o),this.$emit("checkbox-choose",{id:d,curveType:r,isChecked:o,moduleType:e.moduleType,wellboreId:""})}else if(e.moduleType===9){const d=e.id,r="99";console.log("地层模型节点, data:",e),console.log("地层模型ID:",d),console.log("地层模型选中状态:",o),console.log("发送地层模型事件:",{id:d,curveType:r,isChecked:o,moduleType:e.moduleType}),this.$emit("checkbox-choose",{id:d,curveType:r,isChecked:o,moduleType:e.moduleType,wellboreId:""})}else if(e.moduleType===7){const d=e.id,r=e.wellboreId,p="99";console.log("曲线节点, data:",e),console.log("曲线ID:",d),console.log("曲线节点选中状态:",o),this.$emit("checkbox-choose",{id:d,curveType:p,isChecked:o,moduleType:e.moduleType,wellboreId:r})}else if(e.moduleType===10){const d=e.id,r=e.wellboreId,p="99";if(e.datasetId)this.$emit("checkbox-choose",{id:d,curveType:p,isChecked:o,moduleType:e.moduleType,wellboreId:r,datasetId:e.datasetId});else{alert("Please choose a dataset first");return}}},showImportModelDialog(e){this.currentNode=e,this.importModelDialogVisible=!0},async handleModelImported(e){console.log(e,"data============");const t=await this.saveTree();if(t.data?.success)await this.reloadTree(),this.$emit("model-imported",e);else throw new Error(t.data?.message||"保存树结构失败")},handleAddModel(e){this.currentNode.children=[],this.currentNode.children.push(...e)},async handleTreeSaveRequired(){try{const e=await this.saveTree();if(e.data?.success)await this.reloadTree();else throw new Error(e.data?.message||"保存树结构失败")}catch(e){console.error("保存树结构错误:",e),this.$message.error(e.message||"保存树结构失败")}},async showSettingDialog(e){const t=e.id;if(console.log("start this.settingCache:",this.settingCache),this.settingCache[t]){const o=this.settingCache[t];this.settingForm.wellboreThickness=o.wellboreThickness,this.settingForm.min=o.min,this.settingForm.max=o.max,this.settingCurrentNode=e,this.settingForm.displayMode=o.displayMode,this.settingForm.color=o.color,this.settingForm.minCalibrate=o.minCalibrate,this.settingForm.maxCalibrate=o.maxCalibrate,this.settingForm.minColor=o.minColor,this.settingForm.maxColor=o.maxColor}else{const o=e.wellboreId,d=`${this.vWebApiUrl}/visual3D/project/GetWellboreInfo?wellboreId=${o}`,r=await axios.post(d);r.data?.success&&(this.settingForm.wellboreThickness=r.data.data.caliper);const p=`${this.vWebApiUrl}/visual3D/project/GetChannelData?wellboreId=${o}&channelId=${e.id}`,u=await axios.post(p);if(u.data?.success){const D=u.data.data.map(v=>({modelData:v.modelData}));console.log("points:",D),this.settingForm.min=Math.min(...D.map(v=>v.modelData)),this.settingForm.max=Math.max(...D.map(v=>v.modelData))}this.settingCache[t]={wellboreThickness:this.settingForm.wellboreThickness,min:this.settingForm.min,max:this.settingForm.max,displayMode:this.settingForm.displayMode,color:this.settingForm.color,minCalibrate:this.settingForm.minCalibrate,maxCalibrate:this.settingForm.maxCalibrate,minColor:this.settingForm.minColor,maxColor:this.settingForm.maxColor},this.settingCurrentNode=e}console.log("end this.settingCache:",this.settingCache),this.$emit("setting-change",this.settingCurrentNode,this.settingForm.wellboreThickness,this.settingForm.displayMode,this.settingForm.min,this.settingForm.max,this.settingForm.color,this.settingForm.minCalibrate,this.settingForm.maxCalibrate,this.settingForm.minColor,this.settingForm.maxColor)},handleSettingDialogClose(){this.settingDialogVisible=!1},handleSettingConfirm(){this.$emit("setting-change",this.settingCurrentNode,this.settingForm.wellboreThickness,this.settingForm.displayMode,this.settingForm.min,this.settingForm.max,this.settingForm.color,this.settingForm.minCalibrate,this.settingForm.maxCalibrate,this.settingForm.minColor,this.settingForm.maxColor),this.settingCurrentNode&&(this.settingCache[this.settingCurrentNode.id]={wellboreThickness:this.settingForm.wellboreThickness,min:this.settingForm.min,max:this.settingForm.max,displayMode:this.settingForm.displayMode,minCalibrate:this.settingForm.minCalibrate,maxCalibrate:this.settingForm.maxCalibrate,minColor:this.settingForm.minColor,maxColor:this.settingForm.maxColor}),this.handleSettingDialogClose()},updateSettingCache(e){console.log("进入updateSettingCache");const t=e.formNode.id,o=e.options,d=o.find(y=>y.label==="粗细")?.value,r=o.find(y=>y.label==="展示方式")?.value,p=o.find(y=>y.label==="Min Value")?.value,u=o.find(y=>y.label==="Max Value")?.value,D=o.find(y=>y.label==="颜色")?.value,v=o.find(y=>y.label==="Min Calibrate")?.value,T=o.find(y=>y.label==="Max Calibrate")?.value,z=o.find(y=>y.label==="Min Color")?.value,V=o.find(y=>y.label==="Max Color")?.value;console.log("formOption.options:",e.options),this.settingCache[t]={wellboreThickness:d,min:p,max:u,displayMode:r,color:D,minCalibrate:v,maxCalibrate:T,minColor:z,maxColor:V},this.$emit("setting-change",e.formNode,d,r,p,u,D,v,T,z,V)},async showChooseDatasetDialog(e){this.currentNode=e,this.chooseDatasetDialogVisible=!0,e.datasetId&&(this.chooseDatasetForm.datasetId=e.datasetId),await this.loadDatasetsForChoose()},async loadDatasetsForChoose(){this.isLoadingDatasetsForChoose=!0;try{const e=this.currentNode.wellboreId,t=await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetDatasetListByWellboreId?wellboreId=${e}`);if(t.data?.success)this.datasetsForChoose=t.data.data;else throw new Error(t.data?.message||"获取数据集列表失败")}catch(e){console.error("获取数据集列表错误:",e),this.$message.error(e.message||"获取数据集列表失败")}finally{this.isLoadingDatasetsForChoose=!1}},handleChooseDatasetDialogClose(){this.chooseDatasetDialogVisible=!1,this.chooseDatasetForm.datasetId="",this.$refs.chooseDatasetFormRef?.resetFields()},async handleChooseDatasetConfirm(){if(!this.isChoosingDataset)try{await this.$refs.chooseDatasetFormRef.validate(),this.isChoosingDataset=!0;const e=this.datasetsForChoose.find(o=>o.id===this.chooseDatasetForm.datasetId);if(!e)throw new Error("未找到选中的数据集信息");this.currentNode.datasetId=this.chooseDatasetForm.datasetId,console.log("this.currentNode:",this.currentNode.datasetId),this.currentNode.label=`插值成像(${e.name})`;const t=await this.saveTree(!1);if(t.data?.success)await this.reloadTreeWithoutRestoringChecked(),this.$message.success("选择数据集成功"),this.handleChooseDatasetDialogClose();else throw new Error(t.data?.message||"保存失败")}catch(e){console.error("选择数据集错误:",e),this.$message.error(e.message||"选择数据集失败")}finally{this.isChoosingDataset=!1}},logTreeInfo(){console.log("=== El-Tree 信息 ==="),console.log("1. 树数据 (treeData):",this.treeData),console.log("2. 当前选中节点 (currentNode):",this.currentNode),console.log("3. 选中的节点ID数组 (checkedNodeIds):",this.checkedNodeIds),console.log("4. 树组件实例:",this.$refs.tree),this.$refs.tree&&(console.log("5. 当前选中的节点key:",this.$refs.tree.getCurrentKey()),console.log("6. 当前选中的节点数据:",this.$refs.tree.getCurrentNode()),console.log("7. 选中的节点keys:",this.$refs.tree.getCheckedKeys()),console.log("8. 选中的节点数据:",this.$refs.tree.getCheckedNodes()),console.log("9. 半选中的节点keys:",this.$refs.tree.getHalfCheckedKeys()),console.log("10. 半选中的节点数据:",this.$refs.tree.getHalfCheckedNodes())),console.log("11. 设置缓存 (settingCache):",this.settingCache),console.log("12. 应用ID (appId):",this.appId),console.log("13. 项目ID (id):",this.id),console.log("14. 是否正在加载 (isLoading):",this.isLoading);const e=this.$refs.tree?this.$refs.tree.getCheckedKeys():[],t=this.$refs.tree?this.$refs.tree.getHalfCheckedKeys():[],o=this.addCheckedInfoToTreeData(this.treeData,e,t);console.log("15. 保存格式预览:",o),console.log("=== 信息输出完毕 ===")},async saveTreeInfo(){try{const e=await this.saveTree(),o=new URLSearchParams(window.location.hash.split("?")[1]).get("id");if(e.data?.success)this.$message.success("保存成功"),await this.reloadTree(),this.$emit("save-scene",o);else throw new Error(e.data?.message||"保存失败")}catch(e){console.error("保存树结构错误:",e),this.$message.error(e.message||"保存树结构失败")}}},mounted(){const e=new URLSearchParams(window.location.hash.split("?")[1]);this.appId=e.get("appId"),this.id=e.get("id"),this.getOilFieldList(),this.id?this.loadTreeData():this.showCreateProjectPrompt(),useResourceTreeStore().registerTreeInstance(this)}},_hoisted_1$2={class:"project-tree-container"},_hoisted_2$2={class:"debug-toolbar"},_hoisted_3$1={class:"custom-tree-node"},_hoisted_4$1={class:"dialog-footer"},_hoisted_5$1={class:"dialog-footer"},_hoisted_6$1={class:"dialog-footer"},_hoisted_7$1={class:"dialog-footer"},_hoisted_8$1={class:"dialog-footer"},_hoisted_9$1={class:"dialog-footer"},_hoisted_10$1={class:"dialog-footer"};function _sfc_render$1(e,t,o,d,r,p){const u=resolveComponent("el-button"),D=__unplugin_components_0,v=resolveComponent("el-tree"),T=__unplugin_components_1,z=resolveComponent("el-option"),V=resolveComponent("el-select"),y=resolveComponent("el-form-item"),G=resolveComponent("el-input"),te=resolveComponent("el-form"),B=resolveComponent("el-dialog"),L=resolveComponent("ImportModelDialog"),M=resolveComponent("el-input-number"),$=resolveComponent("el-color-picker"),S=_sfc_main$5,K=resolveDirective("loading");return openBlock(),createElementBlock("div",_hoisted_1$2,[createBaseVNode("div",_hoisted_2$2,[createVNode(u,{type:"primary",size:"small",onClick:p.saveTreeInfo,style:{margin:"10px"}},{default:withCtx(()=>t[26]||(t[26]=[createTextVNode(" Save ")])),_:1},8,["onClick"])]),withDirectives((openBlock(),createBlock(v,{data:r.treeData,props:r.defaultProps,"node-key":"id","default-expand-all":"","highlight-current":"","show-checkbox":"","expand-on-click-node":!1,ref:"tree",class:"custom-tree",onNodeContextmenu:p.handleContextMenu,onNodeClick:p.handleNodeClick,onCheck:p.handleCheck},{default:withCtx(({node:f,data:se})=>[createBaseVNode("span",_hoisted_3$1,[createVNode(D,{name:p.getIcon(se),size:"sm"},null,8,["name"]),createBaseVNode("span",null,toDisplayString(f.label),1)])]),_:1},8,["data","props","onNodeContextmenu","onNodeClick","onCheck"])),[[K,r.isLoading]]),createVNode(T,{menuItems:r.customMenuItems,ref:"contextMenu",onMenuAction:p.handleMenuAction},null,8,["menuItems","onMenuAction"]),createVNode(B,{title:"Create Project",modelValue:r.dialogVisible,"onUpdate:modelValue":t[3]||(t[3]=f=>r.dialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!r.isConfirmDisabled_createProject,onClose:p.handleDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_4$1,[createVNode(u,{onClick:p.handleDialogClose,disabled:r.isConfirmDisabled_createProject},{default:withCtx(()=>t[28]||(t[28]=[createTextVNode(" Cancel ")])),_:1},8,["onClick","disabled"]),createVNode(u,{type:"primary",loading:r.isConfirmDisabled_createProject,disabled:r.isConfirmDisabled_createProject,onClick:p.createProject},{default:withCtx(()=>[createTextVNode(toDisplayString(r.isConfirmDisabled_createProject?"Creating...":"Confirm"),1)]),_:1},8,["loading","disabled","onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.form,ref:"formRef",rules:r.rules,disabled:r.isConfirmDisabled_createProject,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"Oil Field",prop:"oilfieldId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.form.oilfieldId,"onUpdate:modelValue":t[0]||(t[0]=f=>r.form.oilfieldId=f),placeholder:"Please select oil field",class:"input-field",loading:r.isLoadingOilfields,filterable:""},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.oilfields,f=>(openBlock(),createBlock(z,{key:f.id,label:f.cOilFieldName,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),createVNode(y,{label:"Project Name",prop:"projectName",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(G,{modelValue:r.form.projectName,"onUpdate:modelValue":t[1]||(t[1]=f=>r.form.projectName=f),placeholder:"Please enter project name",class:"input-field",maxlength:50,clearable:""},{append:withCtx(()=>[createVNode(u,{onClick:p.generateProjectName},{default:withCtx(()=>t[27]||(t[27]=[createTextVNode("自动")])),_:1},8,["onClick"])]),_:1},8,["modelValue"])]),_:1}),createVNode(y,{label:"Remark",prop:"note",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(G,{modelValue:r.form.note,"onUpdate:modelValue":t[2]||(t[2]=f=>r.form.note=f),type:"textarea",rows:3,placeholder:"Please enter remark",class:"input-field",maxlength:200,"show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules","disabled"])]),_:1},8,["modelValue","show-close","onClose"]),(openBlock(),createBlock(B,{title:"Rename Project",modelValue:r.renameDialogVisible,"onUpdate:modelValue":t[5]||(t[5]=f=>r.renameDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleRenameDialogClose,"destroy-on-close":"",key:r.currentNode?.data?.id},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_5$1,[createVNode(u,{onClick:p.handleRenameDialogClose},{default:withCtx(()=>t[29]||(t[29]=[createTextVNode(" Cancel ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",onClick:p.handleRename},{default:withCtx(()=>t[30]||(t[30]=[createTextVNode(" Confirm ")])),_:1},8,["onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.renameForm,ref:"renameFormRef",rules:r.renameRules,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"New Name",prop:"newName",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(G,{modelValue:r.renameForm.newName,"onUpdate:modelValue":t[4]||(t[4]=f=>r.renameForm.newName=f),placeholder:"Please enter new name",class:"input-field",maxlength:50,"show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"])),createVNode(B,{title:"Import Well",modelValue:r.importWellDialogVisible,"onUpdate:modelValue":t[7]||(t[7]=f=>r.importWellDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleImportWellDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_6$1,[createVNode(u,{onClick:p.handleImportWellDialogClose},{default:withCtx(()=>t[31]||(t[31]=[createTextVNode(" Cancel ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",loading:r.isImportingWell,onClick:p.handleImportWellConfirm},{default:withCtx(()=>[createTextVNode(toDisplayString(r.isImportingWell?"Importing...":"Import"),1)]),_:1},8,["loading","onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.importWellForm,ref:"importWellFormRef",rules:r.importWellRules,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"Well",prop:"wellId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.importWellForm.wellId,"onUpdate:modelValue":t[6]||(t[6]=f=>r.importWellForm.wellId=f),placeholder:"Please select well",class:"input-field",loading:r.isLoadingWells,filterable:""},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.wells,f=>(openBlock(),createBlock(z,{key:f.id,label:f.wellName,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"]),createVNode(B,{title:"Import Wellbore",modelValue:r.importWellboreDialogVisible,"onUpdate:modelValue":t[9]||(t[9]=f=>r.importWellboreDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleImportWellboreDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_7$1,[createVNode(u,{onClick:p.handleImportWellboreDialogClose},{default:withCtx(()=>t[32]||(t[32]=[createTextVNode(" Cancel ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",loading:r.isImportingWellbore,onClick:p.handleImportWellboreConfirm},{default:withCtx(()=>[createTextVNode(toDisplayString(r.isImportingWellbore?"Importing...":"Import"),1)]),_:1},8,["loading","onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.importWellboreForm,ref:"importWellboreFormRef",rules:r.importWellboreRules,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"Wellbore",prop:"wellboreId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.importWellboreForm.wellboreId,"onUpdate:modelValue":t[8]||(t[8]=f=>r.importWellboreForm.wellboreId=f),placeholder:"Please select wellbore",class:"input-field",loading:r.isLoadingWellbores,filterable:""},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.wellbores,f=>(openBlock(),createBlock(z,{key:f.id,label:f.wellboreNumber,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"]),createVNode(B,{title:"Import Curve",modelValue:r.importCurveDialogVisible,"onUpdate:modelValue":t[12]||(t[12]=f=>r.importCurveDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleImportCurveDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_8$1,[createVNode(u,{onClick:p.handleImportCurveDialogClose},{default:withCtx(()=>t[33]||(t[33]=[createTextVNode(" Cancel ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",loading:r.isImportingCurve,onClick:p.handleImportCurveConfirm},{default:withCtx(()=>[createTextVNode(toDisplayString(r.isImportingCurve?"Importing...":"Import"),1)]),_:1},8,["loading","onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.importCurveForm,ref:"importCurveFormRef",rules:r.importCurveRules,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"Dataset",prop:"datasetId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.importCurveForm.datasetId,"onUpdate:modelValue":t[10]||(t[10]=f=>r.importCurveForm.datasetId=f),placeholder:"Please select dataset",class:"input-field",loading:r.isLoadingDatasets,filterable:"",onChange:p.handleDatasetChange},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.datasets,f=>(openBlock(),createBlock(z,{key:f.id,label:f.name,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","onChange"])]),_:1}),createVNode(y,{label:"Curve",prop:"curveId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.importCurveForm.curveId,"onUpdate:modelValue":t[11]||(t[11]=f=>r.importCurveForm.curveId=f),placeholder:"Please select curve",class:"input-field",loading:r.isLoadingCurves,filterable:"",disabled:!r.importCurveForm.datasetId},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.curves,f=>(openBlock(),createBlock(z,{key:f.id,label:f.name,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","disabled"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"]),createVNode(L,{modelValue:r.importModelDialogVisible,"onUpdate:modelValue":t[13]||(t[13]=f=>r.importModelDialogVisible=f),"current-node":r.currentNode,"tree-data":r.treeData,"api-base-url":r.vWebApiUrl,onModelImported:p.handleModelImported,onTreeSaveRequired:p.handleTreeSaveRequired,onAddModel:p.handleAddModel},null,8,["modelValue","current-node","tree-data","api-base-url","onModelImported","onTreeSaveRequired","onAddModel"]),createVNode(B,{title:"设置",modelValue:r.settingDialogVisible,"onUpdate:modelValue":t[22]||(t[22]=f=>r.settingDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleSettingDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_9$1,[createVNode(u,{onClick:p.handleSettingDialogClose},{default:withCtx(()=>t[34]||(t[34]=[createTextVNode(" 取消 ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",onClick:p.handleSettingConfirm},{default:withCtx(()=>t[35]||(t[35]=[createTextVNode(" 确认 ")])),_:1},8,["onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.settingForm,ref:"settingFormRef","label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"wellboreThickness",prop:"wellboreThickness",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(M,{modelValue:r.settingForm.wellboreThickness,"onUpdate:modelValue":t[14]||(t[14]=f=>r.settingForm.wellboreThickness=f),min:1,max:2e3,step:10,class:"input-field"},null,8,["modelValue"])]),_:1}),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:0,label:"DisplayMode",prop:"displayMode",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.settingForm.displayMode,"onUpdate:modelValue":t[15]||(t[15]=f=>r.settingForm.displayMode=f),placeholder:"Please select display mode",class:"input-field"},{default:withCtx(()=>[createVNode(z,{label:"Normal",value:"normal"}),createVNode(z,{label:"2D",value:"2D"}),createVNode(z,{label:"3D",value:"3D"})]),_:1},8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:1,label:"Min value",prop:"min",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(M,{modelValue:r.settingForm.min,"onUpdate:modelValue":t[16]||(t[16]=f=>r.settingForm.min=f),min:1,step:10,class:"input-field"},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:2,label:"Max value",prop:"max",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(M,{modelValue:r.settingForm.max,"onUpdate:modelValue":t[17]||(t[17]=f=>r.settingForm.max=f),min:1,step:10,class:"input-field"},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:3,label:"Min Calibrate",prop:"minCalibrate",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(M,{modelValue:r.settingForm.minCalibrate,"onUpdate:modelValue":t[18]||(t[18]=f=>r.settingForm.minCalibrate=f),min:0,step:1,class:"input-field"},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:4,label:"Max Calibrate",prop:"maxCalibrate",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(M,{modelValue:r.settingForm.maxCalibrate,"onUpdate:modelValue":t[19]||(t[19]=f=>r.settingForm.maxCalibrate=f),min:0,step:1,class:"input-field"},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:5,label:"Min Color",prop:"minColor",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode($,{modelValue:r.settingForm.minColor,"onUpdate:modelValue":t[20]||(t[20]=f=>r.settingForm.minColor=f),class:"input-field","show-alpha":""},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0),r.settingCurrentNode&&r.settingCurrentNode.moduleType===7?(openBlock(),createBlock(y,{key:6,label:"Max Color",prop:"maxColor",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode($,{modelValue:r.settingForm.maxColor,"onUpdate:modelValue":t[21]||(t[21]=f=>r.settingForm.maxColor=f),class:"input-field","show-alpha":""},null,8,["modelValue"])]),_:1})):createCommentVNode("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","onClose"]),createVNode(B,{title:"Choose Dataset",modelValue:r.chooseDatasetDialogVisible,"onUpdate:modelValue":t[24]||(t[24]=f=>r.chooseDatasetDialogVisible=f),width:"550px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!0,onClose:p.handleChooseDatasetDialogClose,"destroy-on-close":""},{footer:withCtx(()=>[createBaseVNode("div",_hoisted_10$1,[createVNode(u,{onClick:p.handleChooseDatasetDialogClose},{default:withCtx(()=>t[36]||(t[36]=[createTextVNode(" Cancel ")])),_:1},8,["onClick"]),createVNode(u,{type:"primary",loading:r.isChoosingDataset,onClick:p.handleChooseDatasetConfirm},{default:withCtx(()=>[createTextVNode(toDisplayString(r.isChoosingDataset?"Saving...":"Confirm"),1)]),_:1},8,["loading","onClick"])])]),default:withCtx(()=>[createVNode(te,{model:r.chooseDatasetForm,ref:"chooseDatasetFormRef",rules:r.chooseDatasetRules,"label-position":"right"},{default:withCtx(()=>[createVNode(y,{label:"Dataset",prop:"datasetId",class:"form-item","label-width":"120px"},{default:withCtx(()=>[createVNode(V,{modelValue:r.chooseDatasetForm.datasetId,"onUpdate:modelValue":t[23]||(t[23]=f=>r.chooseDatasetForm.datasetId=f),placeholder:"Please select dataset",class:"input-field",loading:r.isLoadingDatasetsForChoose,filterable:""},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.datasetsForChoose,f=>(openBlock(),createBlock(z,{key:f.id,label:f.name,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"]),createVNode(B,{title:r.curveTitle,draggable:"",modelValue:r.curvePreviewDialog,"onUpdate:modelValue":t[25]||(t[25]=f=>r.curvePreviewDialog=f),width:"1000px","close-on-click-modal":!1,onClose:p.handleCurvePreviewDialogClose},{default:withCtx(()=>[createVNode(S)]),_:1},8,["title","modelValue","onClose"])])}const ProjectTree=_export_sfc(_sfc_main$2,[["render",_sfc_render$1],["__scopeId","data-v-cf98f788"]]);class Lut{constructor(t,o=32){this.isLut=!0,this.lut=[],this.map=[],this.n=0,this.minV=0,this.maxV=1,this.setColorMap(t,o)}set(t){return t.isLut===!0&&this.copy(t),this}setMin(t){return this.minV=t,this}setMax(t){return this.maxV=t,this}setColorMap(t,o=32){this.map=ColorMapKeywords[t]||ColorMapKeywords.rainbow,this.n=o;const d=1/this.n,r=new Color,p=new Color;this.lut.length=0,this.lut.push(new Color(this.map[0][1]));for(let u=1;u<o;u++){const D=u*d;for(let v=0;v<this.map.length-1;v++)if(D>this.map[v][0]&&D<=this.map[v+1][0]){const T=this.map[v][0],z=this.map[v+1][0];r.setHex(this.map[v][1],LinearSRGBColorSpace),p.setHex(this.map[v+1][1],LinearSRGBColorSpace);const V=new Color().lerpColors(r,p,(D-T)/(z-T));this.lut.push(V)}}return this.lut.push(new Color(this.map[this.map.length-1][1])),this}copy(t){return this.lut=t.lut,this.map=t.map,this.n=t.n,this.minV=t.minV,this.maxV=t.maxV,this}getColor(t){t=MathUtils.clamp(t,this.minV,this.maxV),t=(t-this.minV)/(this.maxV-this.minV);const o=Math.round(t*this.n);return this.lut[o]}addColorMap(t,o){return ColorMapKeywords[t]=o,this}createCanvas(){const t=document.createElement("canvas");return t.width=1,t.height=this.n,this.updateCanvas(t),t}updateCanvas(t){const o=t.getContext("2d",{alpha:!1}),d=o.getImageData(0,0,1,this.n),r=d.data;let p=0;const u=1/this.n,D=new Color,v=new Color,T=new Color;for(let z=1;z>=0;z-=u)for(let V=this.map.length-1;V>=0;V--)if(z<this.map[V][0]&&z>=this.map[V-1][0]){const y=this.map[V-1][0],G=this.map[V][0];D.setHex(this.map[V-1][1],LinearSRGBColorSpace),v.setHex(this.map[V][1],LinearSRGBColorSpace),T.lerpColors(D,v,(z-y)/(G-y)),r[p*4]=Math.round(T.r*255),r[p*4+1]=Math.round(T.g*255),r[p*4+2]=Math.round(T.b*255),r[p*4+3]=255,p+=1}return o.putImageData(d,0,0),t}}const ColorMapKeywords={rainbow:[[0,255],[.2,65535],[.5,65280],[.8,16776960],[1,16711680]],cooltowarm:[[0,3952322],[.2,10206463],[.5,14474460],[.8,16163717],[1,11797542]],blackbody:[[0,0],[.2,7864320],[.5,15086080],[.8,16776960],[1,16777215]],grayscale:[[0,0],[.2,4210752],[.5,8355712],[.8,12566463],[1,16777215]]};class VarTubeGeometry extends BufferGeometry{constructor(t,o,d=8,r,p,u){super();const D=o.length-1,v=t.getLength();this.type="VarTubeGeometry",this.parameters={lut:u,path:t,tubularSegments:D,radialSegments:d,closed};const T=se(t,o);this.tangents=T.tangents,this.normals=T.normals,this.binormals=T.binormals;const z=new Vector3,V=new Vector3,y=new Vector2;let G=new Vector3;const te=[],B=[],L=[],M=[],$=[];S(),this.setIndex($),this.setAttribute("position",new Float32BufferAttribute(te,3)),this.setAttribute("normal",new Float32BufferAttribute(B,3)),this.setAttribute("uv",new Float32BufferAttribute(L,2)),this.setAttribute("color",new Float32BufferAttribute(M,3));function S(){for(let X=0;X<=D;X++)K(X,o[X]);le(),f()}function K(X,J){let P=J[0]/v;P>1&&(P=1),G=t.getPointAt(P,G);const q=MathUtils.mapLinear(J[1],r[0],r[1],p[0],p[1]),U=T.normals[X],Y=T.binormals[X];var ne=null;u&&(ne=u.getColor(J[1]));for(let ce=0;ce<=d;ce++){const we=ce/d*Math.PI*2,pe=Math.sin(we),de=-Math.cos(we);V.x=de*U.x+pe*Y.x,V.y=de*U.y+pe*Y.y,V.z=de*U.z+pe*Y.z,V.normalize(),B.push(V.x,V.y,V.z),ne&&M.push(ne.r,ne.g,ne.b),z.x=G.x+q*V.x,z.y=G.y+q*V.y,z.z=G.z+q*V.z,te.push(z.x,z.y,z.z)}}function f(){for(let X=1;X<=D;X++)for(let J=1;J<=d;J++){const P=(d+1)*(X-1)+(J-1),q=(d+1)*X+(J-1),U=(d+1)*X+J,Y=(d+1)*(X-1)+J;$.push(P,q,Y),$.push(q,U,Y)}}function se(X,J){const P=new Vector3,q=[],U=o.length-1,Y=[],ne=[],ce=new Vector3,we=new Matrix4,pe=(ie,me,Me)=>Math.max(me,Math.min(Me,ie));for(let ie=0;ie<J.length;ie++){let me=J[ie][0]/v;me>1&&(me=1),q[ie]=X.getTangentAt(me,new Vector3),q[ie].normalize()}Y[0]=new Vector3,ne[0]=new Vector3;let de=Number.MAX_VALUE;const fe=Math.abs(q[0].x),Ce=Math.abs(q[0].y),ye=Math.abs(q[0].z);fe<=de&&(de=fe,P.set(1,0,0)),Ce<=de&&(de=Ce,P.set(0,1,0)),ye<=de&&P.set(0,0,1),ce.crossVectors(q[0],P).normalize(),Y[0].crossVectors(q[0],ce),ne[0].crossVectors(q[0],Y[0]);for(let ie=1;ie<=U;ie++){if(Y[ie]=Y[ie-1].clone(),ne[ie]=ne[ie-1].clone(),ce.crossVectors(q[ie-1],q[ie]),ce.length()>Number.EPSILON){ce.normalize();const me=Math.acos(pe(q[ie-1].dot(q[ie]),-1,1));Y[ie].applyMatrix4(we.makeRotationAxis(ce,me))}ne[ie].crossVectors(q[ie],Y[ie])}return{tangents:q,normals:Y,binormals:ne}}function le(){for(let X=0;X<=D;X++)for(let J=0;J<=d;J++)y.x=X/D,y.y=J/d,L.push(y.x,y.y)}}}function Box3Grid(e){this.type="Box3Grid",this.Root=new Group,this.Root.name="Box3Grid",this.option=Object.assign({minRange:new Vector3(-500,-500,-500),maxRange:new Vector3(500,500,500),fontSize:50,lineWidth:2,scale:100,fontColor:new Color(0,0,0),xName:"X",yName:"Y",zName:"Z"},e||{}),this.TEXTS={},this.direction=new Vector3,this.init()}Object.assign(Box3Grid.prototype,{init(){const e=(p,u)=>({area:[u.x-p.x,u.y-p.y,u.z-p.z],position:[(u.x+p.x)/2,(u.y+p.y)/2,(u.z+p.z)/2]}),{maxRange:t,minRange:o}=this.option,{area:d,position:r}=e(o,t);this.area=d,this.position=r,this.Root.add(this.drawBox()),this.setTexts()},displayText(e){const{text:t,size:o,position:d,anisotropy:r,color:p,width:u,height:D}=Object.assign({text:"text",size:4,position:new Vector3,color:new Float32BufferAttribute(255)},e);var v=new CanvasTexture(this.generateSprite({text:t,color:p.getStyle(),width:u,height:D}));v.format=void 0,v.type=UnsignedByteType,r&&(v.anisotropy=r);var T=new Sprite(new SpriteMaterial({depthWrite:!1,map:v,blending:NormalBlending}));return T.position.set(d.x,d.y,d.z),u&&D?T.scale.set(u/D*o,o):T.scale.set(o,o),T},generateSprite(e){const t=Object.assign({text:"text",color:"#000000",width:64,height:64,fontSize:14},e||{});var o=document.createElement("canvas"),d=t.width,r=t.height,p=r-4;o.width=d,o.height=r;var u=o.getContext("2d");u.beginPath(),u.font=r+"px Arial,Helvetica,sans-serif",u.textAlign="center",u.fillStyle=t.color;var D=d/2,v=p-2;return u.fillText(t.text,D,v),o},drawBox(){const{area:e,position:t}=this;var o=new(void 0)(...e),d=[0,1,1,1,0,0,1,0,1,1,0,1,1,0,0,0,1,0,0,0,1,1,0,1,1,1,0,1,1,0,0,0,1,1,0,1,1,0,0,0,0,1,1,1,0,0,1,0];o.setAttribute("uv",new Float32BufferAttribute(d,2));const{maxRange:r}=this.option;var p=()=>{var T=Math.max(...e),z=Math.floor(Math.log10(T)),V=3-z;return Math.pow(10,V)},u=p(),D=[[e[2],e[1],r.z,r.y],[e[2],e[1],r.z,r.y],[e[0],e[2],r.x,r.z],[e[0],e[2],r.x,r.z],[e[0],e[1],r.x,r.y],[e[0],e[1],r.x,r.y]].map(T=>new MeshBasicMaterial({side:BackSide,depthWrite:!1,transparent:!0,map:this.getGridLineTexture(T[0]*u,T[1]*u,T[2]*u,T[3]*u,this.option.scale*u)})),v=new Mesh(o,D);return v.name="gridBox",v.position.set(...t),v},setTexts(){const{minRange,maxRange,anisotropy,fontColor:color,scale,fontSize,textFormat}=this.option,position=this.position,offset=fontSize/2,nameOffset=fontSize,nameWidth=64*5,nameHeight=64,_V=new Vector3;var drawXYZ=(e,t,o)=>{const d=new Group;for(var r=Math.floor(t/scale)*scale,p=new Vector3(0,0,0);r>e;){if(r!==t){const u=typeof textFormat=="function"?textFormat(r,o):r;p[o]=r,d.add(this.displayText({text:u,position:p,width:nameHeight*5,height:nameHeight,anisotropy,size:fontSize,color}))}r-=scale}return d};const xText=drawXYZ(minRange.x,maxRange.x,"x"),yText=drawXYZ(minRange.y,maxRange.y,"y"),zText=drawXYZ(minRange.z,maxRange.z,"z"),xPositon=[[0,"-","-"],[0,"-","+"],[0,"+","+"],[0,"+","-"]],yPositon=[["-",0,"-"],["-",0,"+"],["+",0,"+"],["+",0,"-"]],zPositon=[["-","-",0],["-","+",0],["+","+",0],["+","-",0]],computeOffset=(str,i,offset=0)=>str==="-"?eval(minRange.getComponent(i)+str+offset):str==="+"?eval(maxRange.getComponent(i)+str+offset):str,computeVector=(e,t)=>{for(var o=0;o<e.length;o++)["-","+"].indexOf(e[o])===-1?_V.setComponent(o,e[o]):_V.setComponent(o,computeOffset(e[o],o,t));return _V},computeNameV=(arr,offset)=>{for(var i=0;i<arr.length;i++)["-","+"].indexOf(arr[i])===-1?_V.setComponent(i,position[i]):_V.setComponent(i,eval(arr[i]+offset));return _V},addTexts=(e,t,o,d,r)=>{for(let p=0;p<e.length;p++){const u=t.clone();let D=computeNameV(e[p],nameOffset);u.add(this.displayText({text:this.option[`${o}Name`],position:D,size:fontSize,width:nameWidth,height:nameHeight,anisotropy,color})),u.position.copy(computeVector(e[p],offset)),Object.assign(u.userData,{a:d,b:r,index:p}),this.Root.add(u),this.TEXTS[`${o}${p}`]=u}};addTexts(xPositon,xText,"x",1,2),addTexts(yPositon,yText,"y",0,2),addTexts(zPositon,zText,"z",0,1)},upDate(e){const t=e.position;for(let o in this.TEXTS){const d=this.TEXTS[o],{a:r,b:p,index:u}=d.userData;d.visible=this.check(t,u,r,p)}},getGridLineTexture(e,t,o=0,d=0,r){const p=()=>{const D=document.createElement("canvas");D.width=e,D.height=t;const v=D.getContext("2d");v.beginPath(),v.lineWidth=this.option.lineWidth||20;var T=o%r,z=d%r;for(v.moveTo(0,0),v.lineTo(0,t),v.moveTo(0,0),v.lineTo(e,0);T<e||z<t;)T<e&&(v.moveTo(T,0),v.lineTo(T,t)),z<t&&(v.moveTo(0,z),v.lineTo(e,z)),T+=r,z+=r;return v.moveTo(e,0),v.lineTo(e,t),v.moveTo(0,t),v.lineTo(e,t),v.strokeStyle=this.option.fontColor.getStyle(),v.stroke(),D};var u=new CanvasTexture(p());return this.option.anisotropy&&(u.anisotropy=this.option.anisotropy),u},addToScene(e){e.add(this.Root)},check(e,t,o,d){const r=this.option.minRange.getComponent(o),p=this.option.minRange.getComponent(d),u=this.option.maxRange.getComponent(o),D=this.option.maxRange.getComponent(d),v=e.getComponent(o),T=e.getComponent(d);switch(t){case 1:return T>D&&v>r||v<r&&T<D;case 2:return T<D&&v>u||v<u&&T>D;case 3:return T>p&&v>u||v<u&&T<p;default:return T>p&&v<r||v>r&&T<p}}});function LineD3(e){return this.computeLine(e)}Object.assign(LineD3.prototype,{setOffsetLine(e,t,o,d){var r={x:t.x+d*o.x,y:t.y+d*o.y,z:t.z+d*o.z};return e&&e.push(r),r},computeFrenetFrames(e,t,o){const d=new Vector3,r=[],p=t.length-1,u=[],D=[],v=new Vector3,T=new Matrix4,z=(B,L,M)=>Math.max(L,Math.min(M,B));o=o||t[p][0];for(let B=0;B<t.length;B++){let L=t[B][0]/o;L>1&&(L=1),r[B]=e.getTangentAt(L,new Vector3),r[B].normalize()}u[0]=new Vector3,D[0]=new Vector3;let V=Number.MAX_VALUE;const y=Math.abs(r[0].x),G=Math.abs(r[0].y),te=Math.abs(r[0].z);y<=V&&(V=y,d.set(1,0,0)),G<=V&&(V=G,d.set(0,1,0)),te<=V&&d.set(0,0,1),v.crossVectors(r[0],d).normalize(),u[0].crossVectors(r[0],v),D[0].crossVectors(r[0],u[0]);for(let B=1;B<=p;B++){if(u[B]=u[B-1].clone(),D[B]=D[B-1].clone(),v.crossVectors(r[B-1],r[B]),v.length()>Number.EPSILON){v.normalize();const L=Math.acos(z(r[B-1].dot(r[B]),-1,1));u[B].applyMatrix4(T.makeRotationAxis(v,L))}D[B].crossVectors(r[B],u[B])}return{tangents:r,normals:u,binormals:D}},computeLine(e){for(var t=e.line,o=[],d=[],r=[],p=e.arrRadius,u=this.computeFrenetFrames(t,p,e.wellLength),D=new Vector3,v=new Vector3,T=e.toMin,z=e.toMax,V=e.angle,y=0;y<p.length;y++){let $=Math.abs(p[y][0]/e.wellLength);$>1&&($=1),t.getPointAt($,D);var G=MathUtils.mapLinear(p[y][1],e.dataMin,e.dataMax,T,z),te=u.normals[y],B=u.binormals[y],L=Math.sin(V),M=-Math.cos(V);v.x=M*te.x+L*B.x,v.y=M*te.y+L*B.y,v.z=M*te.z+L*B.z,v.normalize(),this.setOffsetLine(o,D,v,G),this.setOffsetLine(d,D,v,T),this.setOffsetLine(r,D,v,z)}return{linePoints:o,inlinePoints:d,outLinePoints:r}}});function AreaD3(e){return this.computeArea(e)}Object.assign(AreaD3.prototype,{computeArea(e){const t=[],o=[],d=[],r=[],p=[],u=[],D=e.line,v=e.lut,T=e.arrRadius,z=this.computeFrenetFrames(D,T,e.wellLength);for(var V=new Vector3,y=new Vector3,G=e.toMin,te=e.toMax,B=e.angle,L=0;L<T.length;L++){let q=Math.abs(T[L][0]/e.wellLength);q>1&&(q=1),D.getPointAt(q,V);var M=T[L][1],$=MathUtils.mapLinear(M,e.dataMin,e.dataMax,G,te),S=z.normals[L],K=z.binormals[L],f=Math.sin(B),se=-Math.cos(B);y.x=se*S.x+f*K.x,y.y=se*S.y+f*K.y,y.z=se*S.z+f*K.z,y.normalize(),r.push(y.x,y.y,y.z,y.x,y.y,y.z);var le=this.setOffsetLine(p,V,y,G);this.setOffsetLine(u,V,y,te);var X=this.setOffsetLine(null,V,y,$);if(t.push(le.x,le.y,le.z,X.x,X.y,X.z),L<T.length-1){var J=L*2;d.push(J,J+1,J+2,J+1,J+3,J+2)}var P=v.getColor(M);o.push(P.r,P.g,P.b,P.r,P.g,P.b)}return{areaPoints:t,areaColors:o,areaIndexs:d,areaNormals:r,inlinePoints:p,outLinePoints:u}},setOffsetLine(e,t,o,d){var r={x:t.x+d*o.x,y:t.y+d*o.y,z:t.z+d*o.z};return e&&e.push(r),r},computeFrenetFrames(e,t,o){const d=new Vector3,r=[],p=t.length-1,u=[],D=[],v=new Vector3,T=new Matrix4,z=(B,L,M)=>Math.max(L,Math.min(M,B));o=o||t[p][0];for(let B=0;B<t.length;B++){let L=t[B][0]/o;L>1&&(L=1),r[B]=e.getTangentAt(L,new Vector3),r[B].normalize()}u[0]=new Vector3,D[0]=new Vector3;let V=Number.MAX_VALUE;const y=Math.abs(r[0].x),G=Math.abs(r[0].y),te=Math.abs(r[0].z);y<=V&&(V=y,d.set(1,0,0)),G<=V&&(V=G,d.set(0,1,0)),te<=V&&d.set(0,0,1),v.crossVectors(r[0],d).normalize(),u[0].crossVectors(r[0],v),D[0].crossVectors(r[0],u[0]);for(let B=1;B<=p;B++){if(u[B]=u[B-1].clone(),D[B]=D[B-1].clone(),v.crossVectors(r[B-1],r[B]),v.length()>Number.EPSILON){v.normalize();const L=Math.acos(z(r[B-1].dot(r[B]),-1,1));u[B].applyMatrix4(T.makeRotationAxis(v,L))}D[B].crossVectors(r[B],u[B])}return{tangents:r,normals:u,binormals:D}}});const _sfc_main$1=defineComponent({name:"ThreeDShows",setup(){let e,t,o,d,r,p,u=new Group;ref([]);const D=window.location.protocol+"//"+window.location.host+"/api",v=ref("south"),T=ref(""),z=ref(""),V=ref(null),y=ref(null),G=ref([]),te=ref(0),B=ref(0),L=ref(1),M=ref("最高: 0"),$=ref("80%: 0"),S=ref("60%: 0"),K=ref("40%: 0"),f=ref("20%: 0"),se=ref("最低: 0");ref(!1),ref(!1),ref([]),ref({modelFile:null});const le=ref(""),X=ref(!1),J=ref(!1),P=ref({xMin:0,xMax:0,zMin:0,zMax:0}),q=ref([]),U=ref([]),Y=ref({x:0,y:0,z:0}),ne=ref(1e3),ce=ref(!1);class we extends Curve{constructor(g){super(),this.points=g}getPoint(g){const c=Math.floor(g*(this.points.length-1)),l=this.points[c],n=this.points[Math.min(c+1,this.points.length-1)],A=g*(this.points.length-1)-c;return new Vector3(l.x*(1-A)+n.x*A,l.y*(1-A)+n.y*A,l.z*(1-A)+n.z*A)}}const pe=()=>{const s=document.querySelector(".three-container"),[g,c]=[s.offsetWidth,s.offsetHeight];console.log("【调试】初始化3D场景"),t=new WebGLRenderer({antialias:!0}),t.setSize(g,c),t.setClearColor(0),t.localClippingEnabled=!0,s.appendChild(t.domElement),o=new Scene,o.background=new Color(0),o.add(u),e=new PerspectiveCamera(75,g/c,.1,1e4),e.position.set(0,0,20),e.lookAt(0,0,0),d=new OrbitControls(e,t.domElement),d.enableDamping=!0,d.dampingFactor=.05;const l=new AmbientLight(16777215,.6);o.add(l);const n=new DirectionalLight(16777215,1);n.position.set(1,1,1),o.add(n),window.addEventListener("resize",me),console.log("【调试】场景初始化完成")},de=(s,g,c,l,n)=>{const A=`${c}_${l}`;console.log("创建时的唯一标识符号"+A);var a=null;const m=new we(s),h=new TubeGeometry(m,64,n,8,!1),b=new MeshPhongMaterial({color:g,side:DoubleSide,transparent:!0,opacity:1,shininess:100,emissive:g,emissiveIntensity:.5});a=new Mesh(h,b),a.userData.curveId=A,a.userData.points=s.map(N=>({...N,originalY:N.originalY||N.y})),a.userData.wellboreId=c,a.userData.curveType=l,a.userData.displayMode="normal",a.userData.heightScale=L.value,a.userData.caliper=n,a.userData.originalColor=g,o.add(a)},fe=(s,g,c,l,n)=>{n||(n=15),de(s,g,c,l,n)},Ce=(s,g)=>{const c=`${s}_${g}`;console.log("删除时的唯一标识符"+c);try{const l=[],n=[`${c}_inline`,`${c}_outline`];o.children.forEach(A=>{A instanceof Line&&A.userData&&A.userData.lineId&&n.includes(A.userData.lineId)&&l.push(A)}),o.traverse(A=>{A instanceof Mesh&&A.userData&&A.userData.curveId===c&&l.push(A)}),o.traverse(A=>{A instanceof Line&&A.userData&&A.userData.lineId&&A.userData.lineId.includes(c)&&l.push(A)}),l.length>0?(console.log(`找到 ${l.length} 个与曲线 ${c} 相关的对象`),l.forEach(A=>{try{o.remove(A),A.geometry&&A.geometry.dispose(),A.material&&(Array.isArray(A.material)?A.material.forEach(a=>a.dispose()):A.material.dispose())}catch(a){console.error(`移除对象时出错 (${c}):`,a)}})):console.log(`未找到与曲线 ${c} 相关的对象`)}catch(l){console.error(`removeCurve全局错误 (${c}):`,l)}},ye=(s,g,c,l,n,A)=>{try{console.log("创建2D曲线:",A);const k=s.filter(R=>R&&typeof R=="object"&&"x"in R&&"y"in R&&"z"in R);if(k.length<2){console.error(`曲线 ${A} 的有效点数量不足`);return}const H=new CatmullRomCurve3(k.map(R=>new Vector3(R.x,R.y,R.z)));var a=[],m=3,h=150,b=1/0,N=-1/0;for(let R=0;R<k.length;R++)if(k[R].modelData!==void 0){const Q=k[R].modelData;b=Math.min(b,Q),N=Math.max(N,Q)}(!isFinite(b)||!isFinite(N))&&(b=0,N=1),b===N&&(N=b+1);for(var _=[b,N],E=[1*b,3*N],I=0;I<k.length;I++){const R=k[I].modelData!==void 0?k[I].modelData:l==="1"||l==="2"?n*5:b;a.push([I,R])}const j=new Lut("rainbow",128);j.setMax(N),j.setMin(b);var x=new LineDashedMaterial({color:"#ffffff",dashSize:1,gapSize:1});const O=k.map(R=>new Vector3(R.x,R.y,R.z)),W=new CatmullRomCurve3(O),Z=W.getLength();if(Z<=0){console.error(`曲线 ${A} 的长度无效`);return}var w=new AreaD3({line:W,wellLength:Z,toMin:m,toMax:h,dataMin:b,dataMax:N,lut:j,arrRadius:a,angle:0});ot(w.inlinePoints,x,A+"_inline");var F=new BufferGeometry;F.setIndex(w.areaIndexs),F.setAttribute("position",new Float32BufferAttribute(w.areaPoints,3)),F.setAttribute("normal",new Float32BufferAttribute(w.areaNormals,3)),F.setAttribute("color",new Float32BufferAttribute(w.areaColors,3));var C=new Mesh(F,new MeshBasicMaterial({side:DoubleSide,vertexColors:!0}));C.userData={curveId:A,points:k.map(R=>({...R,originalY:R.originalY||R.y})),wellboreId:c,curveType:l,displayMode:"2D",heightScale:L.value,caliper:n,originalColor:g},o.add(C),console.log("2D曲线创建完成:",A)}catch(k){console.error(`创建2D曲线时出错 (${A}):`,k)}},ie=(s,g,c,l,n,A,a,m,h,b,N,_)=>{try{console.log("创建3D曲线:",A);const C=s.filter(j=>j&&typeof j=="object"&&"x"in j&&"y"in j&&"z"in j);if(C.length<2){console.error(`曲线 ${A} 的有效点数量不足`);return}const k=new CatmullRomCurve3(C.map(j=>new Vector3(j.x,j.y,j.z)));let H;if(l==="0"||l==="1"){const j=new TubeGeometry(k,64,n,8,!1),O=new MeshPhongMaterial({color:g,side:DoubleSide,transparent:!0,opacity:1,shininess:100,emissive:g,emissiveIntensity:.2});H=new Mesh(j,O)}else{const j=[];let O=1/0,W=-1/0,Z=1/0,R=-1/0;for(let oe=0;oe<C.length;oe++)if(C[oe].modelData!==void 0){const re=C[oe].modelData;O=Math.min(O,re),W=Math.max(W,re)}(!isFinite(O)||!isFinite(W))&&(O=0,W=1),O===W&&(W=O+1);for(var E=(b-h)/(m-a),I=h-E*a,x=0;x<C.length;x++){const oe=C[x].modelData!==void 0?C[x].modelData:O,re=E*oe+I;Z=Math.min(Z,re),R=Math.max(R,re),j.push([x,re])}let Q;if(N&&_){const oe=Ae=>{const ae=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(Ae);return ae?{r:parseInt(ae[1],16)/255,g:parseInt(ae[2],16)/255,b:parseInt(ae[3],16)/255}:null},re=oe(N),ee=oe(_);if(re&&ee){const Ae=[];for(let ae=0;ae<128;ae++){const ue=ae/127,be={r:re.r+(ee.r-re.r)*ue,g:re.g+(ee.g-re.g)*ue,b:re.b+(ee.b-re.b)*ue};Ae.push(be)}Q=new Lut("rainbow",128);for(let ae=0;ae<Ae.length;ae++){const ue=Ae[ae];Q.lut[ae]=new Color(ue.r,ue.g,ue.b)}}else Q=new Lut("rainbow",128)}else Q=new Lut("rainbow",128);Z&&R?(Q.setMax(R),Q.setMin(Z)):(Q.setMax(W),Q.setMin(O));var w=new VarTubeGeometry(k,j,20,[Z,R],[Z*1,R*3],Q),F=new MeshPhongMaterial({side:DoubleSide,vertexColors:!0});H=new Mesh(w,F)}H.userData={curveId:A,points:C.map(j=>({...j,originalY:j.originalY||j.y})),wellboreId:c,curveType:l,displayMode:"3D",heightScale:L.value,caliper:n,originalColor:g,minVal:a,maxVal:m,minCalibration:h,maxCalibration:b,minColor:N,maxColor:_},o.add(H),console.log("3D曲线创建完成:",A)}catch(C){console.error(`创建3D曲线时出错 (${A}):`,C)}},me=()=>{const s=document.querySelector(".three-container"),[g,c]=[s.offsetWidth,s.offsetHeight];e.aspect=g/c,e.updateProjectionMatrix(),t.setSize(g,c)},Me=()=>{requestAnimationFrame(Me),d&&d.update();const s=o.getObjectByName("WireCubeGrid");s&&s.visible&&Ue(s,e);const g=o.getObjectByName("CuttingWireCubeGrid");g&&Ue(g,e),t&&o&&e&&t.render(o,e)},$e=()=>{if(window.removeEventListener("resize",me),t&&t.dispose(),o)for(G.value.forEach(s=>{s&&(o.remove(s),s.geometry&&s.geometry.dispose(),s.material&&(Array.isArray(s.material)?s.material.forEach(g=>g.dispose()):s.material.dispose()))}),G.value=[],U.value.forEach(s=>{s&&(o.remove(s),s.geometry&&s.geometry.dispose(),s.material&&(Array.isArray(s.material)?s.material.forEach(g=>g.dispose()):s.material.dispose()))}),U.value=[];o.children.length>0;){const s=o.children[0];s.geometry&&s.geometry.dispose(),s.material&&(Array.isArray(s.material)?s.material.forEach(g=>g.dispose()):s.material.dispose()),o.remove(s)}},Pe=s=>s.map(g=>({x:g.dispE,y:-g.tvd*L.value,z:g.dispN,modelData:g.modelData,originalY:-g.tvd})),Ee=async(s,g)=>{try{const c=`${D}/visual3D/project/GetDesignCurve?wellboreId=${s}&curveType=${g}`,l=await axios.post(c);if(l.data?.success){const n=Pe(l.data.data.map(A=>({dispE:A.dispE,tvd:A.tvd,dispN:A.dispN})));console.log("轨迹曲线点数据:",n),g==="0"?fe(n,65280,s,g,l.data.data[0].caliper):g==="1"&&fe(n,16711680,s,g,l.data.data[0].caliper)}else throw new Error(l.data?.message||"获取设计曲线失败")}catch(c){throw console.error("获取设计曲线错误:",c),c}},ze=async(s,g)=>{try{const c=`${D}/visual3D/project/GetChannelData?wellboreId=${s}&channelId=${g}`,l=await axios.post(c);if(l.data?.success){const n=Pe(l.data.data.map(A=>({dispE:A.dispE,tvd:A.tvd,dispN:A.dispN,modelData:A.modelData})));console.log("曲线点数据:",n),fe(n,16711680,g,99)}else throw new Error(l.data?.message||"获取数据集下的曲线错误")}catch(c){throw console.error("获取数据集下的曲线错误:",c),c}},Ke=(s,g)=>{const l=s.split(`
`).map(w=>w.trim());let n=null;for(let w=0;w<l.length;w++)if(l[w]==="@"){n=w;break}if(n===null)throw new Error("未找到 header 结束标记 '@'");let A="",a="",m="";for(let w=0;w<n;w++)if(l[w].startsWith("@")){m=l[w+1],A=l[w+2],a=l[w+3];break}if(!A)throw new Error("未找到网格信息行");if(!a)throw new Error("未找到网格间距信息行");const h=m.split(",").map(w=>w.trim()).filter(w=>w!==""),b=A.split(",").map(w=>w.trim()).filter(w=>w!==""),N=a.split(",").map(w=>w.trim()).filter(w=>w!=="");if(b.length<6)throw new Error("网格信息不足，请检查文件格式！");const _={cols:parseInt(b[0]),rows:parseInt(b[1]),xMin:parseFloat(b[4]),xMax:parseFloat(b[5]),yMin:parseFloat(b[2]),yMax:parseFloat(b[3]),minHeight:0,nullValue:1e30,rotation:0,xStep:0,yStep:0,data:[]};_.xStep=parseFloat(N[1]),_.yStep=parseFloat(N[2]),_.nullValue=parseFloat(h[1]==""?h[2]:h[1]);const E=[];for(let w=n+1;w<l.length;w++){const F=l[w];if(F==="")continue;const C=F.split(/\s+/).filter(k=>k!=="");if(g==="grd")for(const k of C)try{const H=parseFloat(k);E.push(-H)}catch{console.warn("无法转换数据:",k)}else for(const k of C)try{const H=parseFloat(k);E.push(H)}catch{console.warn("无法转换数据:",k)}}const I=_.rows*_.cols,x=E.length;if(console.log(`预期数据数量: ${I}, 实际数据数量: ${x}`),x!==I)throw new Error(`数据数量与预期不符，预期 ${I}，实际 ${x}`);for(let w=0;w<_.rows;w++){_.data[w]=[];for(let F=0;F<_.cols;F++){const C=w*_.cols+F;_.data[w][F]=E[C]}}return _},Xe=s=>{const c=s.split(`
`).map(w=>w.trim()),l=c[0],n=c[2],A=c[3],a=c[4];if(!n)throw new Error("未找到网格信息行");if(!a)throw new Error("未找到网格间距信息行");if(!A)throw new Error("未找到网格行列信息行");if(!l)throw new Error("未找到网格缺省值信息行");const m=l.split(/\s+/),h=n.split(/\s+/),b=A.split(/\s+/),N=a.split(/\s+/),_={cols:parseInt(b[1]),rows:parseInt(b[2]),xMin:parseFloat(h[1]),xMax:parseFloat(h[2]),yMin:parseFloat(h[3]),yMax:parseFloat(h[4]),minHeight:0,nullValue:parseFloat(m[5]),rotation:0,xStep:0,yStep:0,data:[]};_.xStep=parseFloat(N[1]),_.yStep=parseFloat(N[2]);const E=[];for(let w=6;w<c.length;w++){const F=c[w];if(F==="")continue;const C=F.split(/\s+/).filter(k=>k!=="");for(const k of C)try{const H=parseFloat(k);E.push(-H)}catch{console.warn("无法转换数据:",k)}}const I=_.rows*_.cols,x=E.length;if(console.log(`预期数据数量: ${I}, 实际数据数量: ${x}`),x!==I)throw new Error(`数据数量与预期不符，预期 ${I}，实际 ${x}`);for(let w=0;w<_.rows;w++){_.data[w]=[];for(let F=0;F<_.cols;F++){const C=w*_.cols+F;_.data[w][F]=E[C]}}return _},Je=s=>{const g=s.xMax-s.xMin,c=s.yMax-s.yMin;console.log("地形尺寸:",{width:c,height:g}),console.log("原始坐标范围:",{xMin:s.xMin,xMax:s.xMax,yMin:s.yMin,yMax:s.yMax});const l=new PlaneGeometry(c,g,s.cols-1,s.rows-1),n=l.attributes.position.array;let A=1/0,a=-1/0;for(let x=0;x<s.rows;x++)for(let w=0;w<s.cols;w++){const F=s.data[x][w];F!==null&&!isNaN(F)&&Math.abs(F)<s.nullValue&&(A=Math.min(A,F),a=Math.max(a,F))}console.log("高度范围:",{minHeight:A,maxHeight:a}),Math.abs(a-A)<.1&&(a=A+1,console.log("高度差太小，调整为:",{minHeight:A,maxHeight:a})),T.value=`
        网格大小: ${s.rows} × ${s.cols}<br>
        X范围: ${s.xMin.toFixed(2)} - ${s.xMax.toFixed(2)}<br>
        Y范围: ${s.yMin.toFixed(2)} - ${s.yMax.toFixed(2)}<br>
        高度范围: ${A.toFixed(2)} - ${a.toFixed(2)}<br>
        网格间距: ${s.xStep} × ${s.yStep}
        `;const m=!0,h=new Set;for(let x=0,w=0;x<n.length;x+=3,w++){const F=w%s.cols,C=Math.floor(w/s.cols),k=s.xMin+F/(s.cols-1)*g,H=s.yMin+C/(s.rows-1)*c,j=s.rows-1-C,O=s.data[j][F];let W=A;O!==null&&!isNaN(O)&&Math.abs(O)<s.nullValue&&(W=O*L.value,h.add(w)),n[x]=k,n[x+1]=W,n[x+2]=H}const b=[];for(let x=0;x<s.rows-1;x++)for(let w=0;w<s.cols-1;w++){const F=x*s.cols+w,C=F+1,k=F+s.cols,H=k+1;h.has(F)&&h.has(C)&&h.has(k)&&b.push(F,C,k),h.has(C)&&h.has(k)&&h.has(H)&&b.push(C,H,k)}l.setIndex(b),l.attributes.position.needsUpdate=!0,l.computeVertexNormals();const N=Math.max((a-A)/20,.1),_=Math.max(N*.1,.05);new ShaderMaterial({uniforms:{contourInterval:{value:N},contourWidth:{value:_},minHeight:{value:A},maxHeight:{value:a}},vertexShader:`
                varying float vHeight;
                varying vec4 vPosition;
                uniform float minHeight;
                uniform float maxHeight;
                
                void main() {
                    // 这里使用position.y作为高度值，现在是原始高度
                    vHeight = position.y;
                    vPosition = modelViewMatrix * vec4(position, 1.0);
                    gl_Position = projectionMatrix * vPosition;
                }
            `,fragmentShader:`
                uniform float contourInterval;
                uniform float contourWidth;
                uniform float minHeight;
                uniform float maxHeight;
                varying float vHeight;
                varying vec4 vPosition;
                
                void main() {
                    // 计算标准化高度
                    float normalizedHeight = (vHeight - minHeight) / (maxHeight - minHeight);
                    
                    // 确保normalizedHeight在0到1之间
                    normalizedHeight = clamp(normalizedHeight, 0.0, 1.0);
                    
                    // 计算等高线
                    float modHeight = mod(vHeight, contourInterval);
                    float distanceToContour = min(modHeight, contourInterval - modHeight);
                    float contourFactor = smoothstep(0.0, contourWidth, distanceToContour);
                    
                    // 根据高度计算颜色 - 使用更简单的方式，确保能生成颜色
                    vec3 color = vec3(0.0);
                    
                    // 简单的颜色渐变
                    color.r = normalizedHeight;
                    color.g = 1.0 - normalizedHeight;
                    color.b = normalizedHeight * 0.5;
                    
                    // 混合等高线和地形颜色
                    vec3 finalColor = mix(vec3(0.0, 0.0, 0.0), color, contourFactor);
                    
                    // 确保不会出现全黑的情况
                    finalColor = max(finalColor, vec3(0.1));
                    
                    gl_FragColor = vec4(finalColor, 1.0);
                }
            `,side:DoubleSide});const E=new MeshLambertMaterial({vertexColors:!0,side:DoubleSide});if(!l.attributes.color){const x=[];for(let w=0,F=0;w<n.length;w+=3,F++){const C=F%s.cols,k=Math.floor(F/s.cols),H=s.rows-1-k,j=s.data[H][C];let O={r:.5,g:.5,b:.5};if(j!==null&&!isNaN(j)&&Math.abs(j)<s.nullValue){const W=(j-A)/(a-A);W<.2?O={r:0,g:0,b:.5+W*2.5}:W<.4?O={r:0,g:(W-.2)*5,b:1-(W-.2)*5}:W<.6?O={r:(W-.4)*5,g:1,b:0}:W<.8?O={r:1,g:1-(W-.6)*5,b:0}:O={r:.8,g:.1,b:.1}}x.push(O.r,O.g,O.b)}l.setAttribute("color",new Float32BufferAttribute(x,3))}const I=new Mesh(l,E);return I.userData={baseContourWidth:_,originalXMin:s.xMin,originalXMax:s.xMax,originalYMin:s.yMin,originalYMax:s.yMax,useRealCoordinates:!0,useOriginalHeight:m,minHeight:A,maxHeight:a,originalScale:1},J.value&&q.value.length>0&&(I.material.clippingPlanes=q.value,I.material.clipShadows=!0,I.material.needsUpdate=!0),I},qe=(s,g)=>{const l=s.geometry.attributes.position.array;s.userData.useOriginalHeight;const n=s.userData.minHeight||0,A=s.userData.maxHeight||0;let a={x:0,y:0,z:0},m={x:0,y:0,z:0},h={x:0,y:0,z:0},b={x:0,y:0,z:0},N=1/0,_=1/0,E=1/0,I=1/0;for(let F=0;F<l.length;F+=3){const C=l[F],k=l[F+1],H=l[F+2],j=Math.sqrt(Math.pow(C-g.xMin,2)+Math.pow(H-g.yMin,2)),O=Math.sqrt(Math.pow(C-g.xMax,2)+Math.pow(H-g.yMin,2)),W=Math.sqrt(Math.pow(C-g.xMin,2)+Math.pow(H-g.yMax,2)),Z=Math.sqrt(Math.pow(C-g.xMax,2)+Math.pow(H-g.yMax,2));j<N&&(N=j,a={x:C,y:H,z:k}),O<_&&(_=O,m={x:C,y:H,z:k}),W<E&&(E=W,h={x:C,y:H,z:k}),Z<I&&(I=Z,b={x:C,y:H,z:k})}console.log("最接近四个角的顶点:",{leftBottom:a,rightBottom:m,leftTop:h,rightTop:b});const x={leftBottom:{x:a.x,y:a.y,z:a.z},rightBottom:{x:m.x,y:m.y,z:m.z},leftTop:{x:h.x,y:h.y,z:h.z},rightTop:{x:b.x,y:b.y,z:b.z}},w={leftBottom:{x:g.xMin,y:g.yMin},rightBottom:{x:g.xMax,y:g.yMin},leftTop:{x:g.xMin,y:g.yMax},rightTop:{x:g.xMax,y:g.yMax}};return{modelCoordinates:x,originalCoordinates:w,heightRange:{min:n,max:A}}},Ze=(s,g)=>{try{console.log("【调试】处理文件数据，当前filePath =",z.value);let c=null;if(g==="zmap"||g==="grd")c=Ke(s,g);else if(g==="")c=Xe(s);else{alert("文件格式不支持");return}const l=Je(c);V.value=l;const n=B.value*te.value;l.position.y=n,l.userData.verticalOffset=n,l.userData.modelIndex=B.value,l.userData.id=z.value,console.log("【调试】创建的模型ID =",l.userData.id),B.value++,G.value.push(l),o.add(l),console.log("【调试】模型已添加到场景，当前terrainMeshes数量:",G.value.length);const A=qe(l,c),a=(c.xMin+c.xMax)/2,m=(c.yMin+c.yMax)/2,h=(l.userData.minHeight+l.userData.maxHeight)/2,b=c.yMax-c.yMin,N=c.xMax-c.xMin,_=l.userData.maxHeight-l.userData.minHeight,E=Math.max(b,N,_);v.value==="south"?(e.position.set(a,h,c.yMin-E*2.5),e.up.set(0,1,0)):v.value==="north"&&(e.position.set(a,h,c.yMax+E*2.5),e.up.set(0,1,0)),e.lookAt(a,h,m),e.near=.1,e.far=E*20,e.updateProjectionMatrix(),d.target.set(a,h,m),d.update();let I=1/0,x=-1/0;for(let F=0;F<c.rows;F++)for(let C=0;C<c.cols;C++){const k=c.data[F][C];k!==null&&!isNaN(k)&&Math.abs(k)<c.nullValue&&(I=Math.min(I,k),x=Math.max(x,k))}c.minHeight=I,y.value=c;const w=x-I;if(M.value=`最高: ${x.toFixed(1)}`,$.value=`80%: ${(I+w*.8).toFixed(1)}`,S.value=`60%: ${(I+w*.6).toFixed(1)}`,K.value=`40%: ${(I+w*.4).toFixed(1)}`,f.value=`20%: ${(I+w*.2).toFixed(1)}`,se.value=`最低: ${I.toFixed(1)}`,We(I,x),!o.getObjectByName("WireCubeGrid")){const F=xe({...c,minHeight:c.minHeight*L.value});o.add(F)}P.value.xMin===0&&P.value.xMax===0&&(P.value.xMin=c.xMin,P.value.xMax=c.xMax,P.value.zMin=c.yMin,P.value.zMax=c.yMax),d.target.set(a,h,m),d.update(),Y.value.x=a,Y.value.y=h,Y.value.z=m,ne.value=E*2}catch(c){console.error("处理文件数据失败:",c),alert("处理文件数据失败: "+c.message)}},Se=s=>{const g=s.match(/\.([^.]+)$/);return g?g[1]:""},Ve=async s=>{try{if(G.value.some(a=>a&&a.userData&&a.userData.id===s)){console.log("【调试】防止重复：模型ID已存在于terrainMeshes中，跳过加载",s);return}z.value=s,console.log("【调试】设置filePath.value =",z.value),s="/api/oil/GeologicModel/DownloadFile?id="+s;const g=await fetch(s);if(!g.ok)throw new Error(`HTTP error! status: ${g.status}`);const c=g.headers.get("Content-Disposition");let l="";if(c){const a=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(c);a!=null&&a[1]&&(l=a[1].replace(/['"]/g,""))}const n=await g.text(),A=Se(l||s);if(G.value.some(a=>a&&a.userData&&a.userData.id===z.value)){console.log("【调试】文件已下载，但模型已存在，跳过处理");return}Ze(n,A)}catch(g){console.error("下载或处理文件失败:",g),alert("下载或处理文件失败: "+g.message)}},Re=s=>{G.value.forEach((g,c)=>{if(g){const l=c*s;g instanceof Group?g.position.y=l:(g.position.y=l,g.userData.verticalOffset=l)}})};watch(te,s=>{Re(s)}),watch(L,(s,g)=>{if(!g||s===g)return;console.log(`高度缩放从 ${g} 变更为 ${s}`);const c=s/g,l=[];l.length>0&&setTimeout(()=>{try{l.forEach(n=>{try{const A=n.curveId,a=[`${A}_inline`,`${A}_outline`],m=[];o.traverse(h=>{h instanceof Mesh&&h.userData&&h.userData.curveId===A&&m.push(h),h instanceof Line&&h.userData&&h.userData.lineId&&a.includes(h.userData.lineId)&&m.push(h)}),m.forEach(h=>{o.remove(h),h.geometry&&h.geometry.dispose(),h.material&&(Array.isArray(h.material)?h.material.forEach(b=>b.dispose()):h.material.dispose())})}catch(A){console.error(`移除曲线时出错 (${n.curveId}):`,A)}}),setTimeout(()=>{try{console.log(`开始重新创建 ${l.length} 条曲线`),l.forEach((n,A)=>{try{console.log(`处理第 ${A+1} 条曲线: ${n.curveId}, 模式: ${n.displayMode}, 颜色: ${n.originalColor}`);const a=n.points.map(h=>{const b=h.originalY!==void 0?h.originalY:h.y/(n.heightScale||g);return{x:h.x,y:b*s,z:h.z,modelData:h.modelData,originalY:b}}),m=n.originalColor||16711680;console.log(`创建曲线 ${n.curveId}, 点数: ${a.length}, 颜色: ${m.toString(16)}`),n.displayMode==="2D"?ye(a,m,n.wellboreId,n.curveType,n.caliper,n.curveId):n.displayMode==="3D"?ie(a,m,n.wellboreId,n.curveType,n.caliper,n.curveId):de(a,m,n.wellboreId,n.curveType,n.caliper)}catch(a){console.error(`重新创建曲线 ${n.curveId} 时出错:`,a)}}),console.log("所有曲线重新创建完成")}catch(n){console.error("重新创建曲线过程中出错:",n)}},100)}catch(n){console.error("处理曲线缩放时出错:",n)}},0);try{G.value.forEach(n=>{try{if(n&&n.geometry){const A=n.geometry.attributes.position.array;for(let a=1;a<A.length;a+=3)A[a]*=c;n.geometry.attributes.position.needsUpdate=!0,n.geometry.computeVertexNormals(),n.geometry.computeBoundingBox(),n.geometry.computeBoundingSphere(),n.userData&&(n.userData.minHeight*=c,n.userData.maxHeight*=c)}}catch(A){console.error("更新地层模型时出错:",A)}})}catch(n){console.error("更新地层模型过程中出错:",n)}try{U.value.forEach(n=>{try{if(n&&n.geometry){const A=n.geometry.attributes.position.array;for(let a=1;a<A.length;a+=3)A[a]*=c;n.geometry.attributes.position.needsUpdate=!0,n.geometry.computeVertexNormals(),n.geometry.computeBoundingBox(),n.geometry.computeBoundingSphere()}}catch(A){console.error("更新插值成像模型时出错:",A)}})}catch(n){console.error("更新插值成像模型过程中出错:",n)}if(y.value)try{if((()=>{const A=[];o.children.forEach(a=>{(a.name&&(a.name.includes("WireCubeGrid")||a.name.includes("CuttingWireCubeGrid"))||a.userData&&(a.userData.isWireCubeGrid||a.userData.isCuttingGrid))&&A.push(a)}),A.forEach(a=>{o.remove(a),a.traverse(m=>{m.geometry&&m.geometry.dispose(),m.material&&(Array.isArray(m.material)?m.material.forEach(h=>h.dispose()):m.material.dispose())})})})(),J.value){const A={...y.value,xMin:P.value.xMin,xMax:P.value.xMax,yMin:P.value.zMin,yMax:P.value.zMax,minHeight:y.value.minHeight*s},a=xe(A);a.name="CuttingWireCubeGrid",a.userData.isCuttingGrid=!0,o.add(a)}else{const A=xe({...y.value,minHeight:y.value.minHeight*s});A.name="WireCubeGrid",A.userData.isWireCubeGrid=!0,o.add(A)}}catch(n){console.error("更新网格时出错:",n)}try{if(e&&d){const n=e.position.clone(),A=d.target.clone();n.y*=c,A.y*=c,e.position.copy(n),d.target.copy(A),d.update()}}catch(n){console.error("更新相机和控制器时出错:",n)}}),watch(v,()=>{if(!V.value)return;const s=V.value.userData.originalXMin,g=V.value.userData.originalXMax,c=V.value.userData.originalYMin,l=V.value.userData.originalYMax,n=V.value.userData.minHeight,A=V.value.userData.maxHeight,a=(s+g)/2,m=(c+l)/2,h=(n+A)/2,b=l-c,N=g-s,_=A-n,E=Math.max(b,N,_);v.value==="south"?(e.position.set(a,h,c-E*2.5),e.up.set(0,1,0)):v.value==="north"&&(e.position.set(a,h,l+E*2.5),e.up.set(0,1,0)),e.lookAt(a,h,m),e.near=.1,e.far=E*20,e.updateProjectionMatrix(),d.target.set(a,h,m),d.update()}),watch(z,s=>{s&&Ve(s)});const je=(s,g,c,l,n,A)=>{if(console.log("【调试】handleNodeCheck 被调用:",{id:s,curveType:g,isChecked:c,moduleType:l,wellboreId:n,datasetId:A}),l===6)c?Ee(s,g):Ce(s,g);else if(l===10)console.log("【调试】处理插值成像节点"),c?(console.log("【调试】添加插值成像"),Be(s,n,A)):He(s);else if(l===9)if(c){console.log("【调试】添加地层模型, ID:",s);const a=G.value.filter(m=>m&&m.userData&&m.userData.id===s);if(a.length>0){console.log(`【调试】已存在${a.length}个相同ID的模型，不重复添加`),G.value.forEach((m,h)=>{m&&m.userData&&m.userData.id===s&&(m.visible=!0)});return}z.value=s,Ve(s)}else{const a=[];if(G.value.forEach((m,h)=>{m&&m.userData&&m.userData.id===s&&a.push(m)}),console.log("移除地层模型节点"),console.log(a),o.children.forEach((m,h)=>{m instanceof Mesh&&m.userData&&m.userData.id===s&&(a.includes(m)||a.push(m))}),a.length>0){a.forEach(h=>{o.remove(h),h.visible=!1,h.geometry&&h.geometry.dispose(),h.material&&(Array.isArray(h.material)?h.material.forEach(b=>b.dispose()):h.material.dispose())});const m=G.value.length;if(G.value=G.value.filter(h=>!a.includes(h)),console.log(`【调试】terrainMeshes长度变化: ${m} -> ${G.value.length}`),G.value.forEach((h,b)=>{if(h){h.userData.modelIndex=b;const N=b*te.value;h.position.y=N,h.userData.verticalOffset=N}}),B.value=G.value.length,console.log("【调试】模型已成功移除"),G.value.length===0){const h=o.getObjectByName("WireCubeGrid");h&&o.remove(h);const b=o.getObjectByName("CuttingWireCubeGrid");b&&(o.remove(b),b.traverse(N=>{N.geometry&&N.geometry.dispose(),N.material&&(Array.isArray(N.material)?N.material.forEach(_=>_.dispose()):N.material.dispose())})),Ne()}}else{console.warn("【调试】未找到ID为",s,"的模型");const m=o.children.filter(h=>h instanceof Mesh);console.log("【调试】场景中还有",m.length,"个网格对象"),m.length>0&&(console.log("【调试】执行强制清理"),m.forEach(h=>{console.log("【调试】强制清理网格:",h.userData?.id||"未知ID"),o.remove(h),h.geometry&&h.geometry.dispose(),h.material&&(Array.isArray(h.material)?h.material.forEach(b=>b.dispose()):h.material.dispose())}),G.value=[],B.value=0,console.log("【调试】强制清理完成"),Ne())}}else if(l===7)if(c)ze(n,s);else{console.log(s+"-----"+g),Ce(s,g);const a=`${s}_${g}`;o.traverse(m=>{console.log("检查对象:",m.userData),m instanceof Mesh&&m.userData&&m.userData.curveId===a&&(console.log("移除管道模型:",a),o.remove(m),m.geometry&&m.geometry.dispose(),m.material&&(Array.isArray(m.material)?m.material.forEach(h=>h.dispose()):m.material.dispose()))})}};function xe(s,g=5300,c=6){const l=s.xMin,n=s.xMax,A=s.yMin,a=s.yMax,m=s.minHeight,h=0,b=new Group;b.userData.gridInfo={xMin:l,xMax:n,zMin:A,zMax:a,yMin:m,yMax:h,isGrid:!0},new Vector3(1,0,0),new Vector3(-1,0,0),new Vector3(0,1,0),new Vector3(0,-1,0),new Vector3(0,0,1),new Vector3(0,0,-1);const N=[new Vector3(n,(m+h)/2,(A+a)/2),new Vector3(l,(m+h)/2,(A+a)/2),new Vector3((l+n)/2,h,(A+a)/2),new Vector3((l+n)/2,m,(A+a)/2),new Vector3((l+n)/2,(m+h)/2,a),new Vector3((l+n)/2,(m+h)/2,A)],_=[[],[],[],[],[],[]],E=[[],[],[],[],[],[]];function I(w,F,C,k,H,j,O,W,Z,R,Q,oe,re){const ee=[];for(let Ae=0;Ae<=c;Ae++){const ae=Ae/c,ue=new Vector3(w+(R-w)*ae,F+(Q-F)*ae,C+(oe-C)*ae),be=new Vector3(k+(O-k)*ae,H+(W-H)*ae,j+(Z-j)*ae),he=new Line(new BufferGeometry().setFromPoints([ue,be]),new LineBasicMaterial({color:13421772}));he.userData.isGridLine=!0,he.userData.faceIdx=re,he.userData.lineType="horizontal",he.userData.t=ae,b.add(he),ee.push(he);const Ie=new Vector3(w+(k-w)*ae,F+(H-F)*ae,C+(j-C)*ae),_e=new Vector3(R+(O-R)*ae,Q+(W-Q)*ae,oe+(Z-oe)*ae),ge=new Line(new BufferGeometry().setFromPoints([Ie,_e]),new LineBasicMaterial({color:13421772}));ge.userData.isGridLine=!0,ge.userData.faceIdx=re,ge.userData.lineType="vertical",ge.userData.t=ae,b.add(ge),ee.push(ge)}_[re]=ee}function x(w,F,C,k="x",H){const j=[];for(let O=0;O<=C;O++){if((H===2||H===3)&&(O===0||O===C))continue;const W=O/C,Z=new Vector3(w.x+(F.x-w.x)*W,w.y+(F.y-w.y)*W,w.z+(F.z-w.z)*W);let R=0;k==="x"&&(R=Math.round(Z.x)),k==="y"&&(R=Math.round(Z.y)),k==="z"&&(R=Math.round(Z.z));let Q=new Vector3(0,0,0);const oe=300,re=200;if((O===0||O===C)&&(k==="x"&&(Q.x=(O===0?-1:1)*oe),k==="y"&&(Q.y=(O===0?-1:1)*oe),k==="z"&&(Q.z=(O===0?-1:1)*oe)),k==="y"&&(H===0?Q.x+=50:H===1&&(Q.x-=200)),H===3){Q.y+=re,Q.z+=re;const Ae=200;k==="x"?Q.z-=Ae:k==="z"&&(Q.x-=Ae)}Math.abs(Z.y-m)<10&&(Q.y+=300);const ee=et(R.toString());ee.position.copy(Z.clone().add(Q)),ee.userData.isGridLabel=!0,ee.userData.faceIdx=H,ee.userData.axis=k,ee.userData.t=W,ee.userData.value=R,k==="y"&&(ee.userData.isYAxisLabel=!0),b.add(ee),j.push(ee),E[H].push(ee)}return j}return I(n,m,A,n,m,a,n,h,a,n,h,A,0),I(l,m,A,l,m,a,l,h,a,l,h,A,1),I(l,h,A,n,h,A,n,h,a,l,h,a,2),I(l,m,A,n,m,A,n,m,a,l,m,a,3),I(l,m,a,n,m,a,n,h,a,l,h,a,4),I(l,m,A,n,m,A,n,h,A,l,h,A,5),x(new Vector3(n,m,A),new Vector3(n,m,a),c,"z",0),x(new Vector3(n,m,a),new Vector3(n,h,a),c,"y",0),x(new Vector3(n,h,a),new Vector3(n,h,A),c,"z",0),x(new Vector3(n,h,A),new Vector3(n,m,A),c,"y",0),x(new Vector3(l,m,A),new Vector3(l,m,a),c,"z",1),x(new Vector3(l,m,a),new Vector3(l,h,a),c,"y",1),x(new Vector3(l,h,a),new Vector3(l,h,A),c,"z",1),x(new Vector3(l,h,A),new Vector3(l,m,A),c,"y",1),x(new Vector3(l,h,A),new Vector3(n,h,A),c,"x",2),x(new Vector3(n,h,A),new Vector3(n,h,a),c,"z",2),x(new Vector3(n,h,a),new Vector3(l,h,a),c,"x",2),x(new Vector3(l,h,a),new Vector3(l,h,A),c,"z",2),x(new Vector3(n,h,a),new Vector3(l,h,a),c,"x",4),x(new Vector3(n,h,A),new Vector3(l,h,A),c,"x",5),b.name="WireCubeGrid",b.userData.faceCenters=N,b.userData.faceLines=_,b.userData.faceLabels=E,b.userData.isWireCubeGrid=!0,b.userData.gridBounds={xMin:l,xMax:n,yMin:m,yMax:h,zMin:A,zMax:a},b}function Ue(s,g){if(!s||!s.userData||!s.userData.faceCenters||!s.userData.faceLines)return;const c=s.userData.faceCenters,l=s.userData.faceLines,n=s.userData.faceLabels,A=new Vector3;g.getWorldPosition(A);const a=c.map(_=>A.distanceTo(_)),m=a.map((_,E)=>({d:_,i:E})).sort((_,E)=>_.d-E.d).slice(0,3).map(_=>_.i),h=[0,1],b=h.filter(_=>!m.includes(_));let N=[];if(b.length===0)N=[h.map(E=>({faceIdx:E,distance:a[E]})).sort((E,I)=>I.distance-E.distance)[0].faceIdx];else{const _=b.includes(0),E=b.includes(1);_?N.push(0):E&&N.push(1)}for(let _=0;_<6;_++){const E=!m.includes(_);l[_]&&l[_].forEach(I=>{I.visible=E}),n&&n[_]&&n[_].forEach(I=>{I.userData.isYAxisLabel?I.visible=N.includes(_):I.visible=E})}}function et(s,g="#FFFFFF",c=45){const l=document.createElement("canvas");l.width=512,l.height=256;const n=l.getContext("2d");n.font=`bold ${c}px Arial`,n.fillStyle=g,n.textAlign="center",n.textBaseline="middle",n.clearRect(0,0,l.width,l.height),n.fillText(s,l.width/2,l.height/2);const A=new CanvasTexture(l),a=new SpriteMaterial({map:A,transparent:!0,clippingPlanes:[]}),m=new Sprite(a);return m.scale.set(1800,900,1),m}const tt=(s,g,c,l,n,A,a,m,h,b)=>{console.log("setCurveSetting",s,g,c,A,a,m,h,b);try{const N=[],_=[`${s}_inline`,`${s}_outline`];o.children.forEach(C=>{C instanceof Line&&C.userData&&C.userData.lineId&&_.includes(C.userData.lineId)&&N.push(C)}),o.traverse(C=>{C instanceof Mesh&&C.userData&&C.userData.curveId===s&&N.push(C)}),o.traverse(C=>{C instanceof Line&&C.userData&&C.userData.lineId&&C.userData.lineId.includes(s)&&(N.includes(C)||N.push(C))}),console.log(`找到 ${N.length} 个与曲线 ${s} 相关的对象需要清理`);let E=null;for(const C of N)if(C instanceof Mesh&&C.userData&&C.userData.curveId===s){E=C;break}if(!E){console.log(`未找到ID为 ${s} 的曲线对象`);return}const I=E.userData.points;if(!I||!Array.isArray(I)||I.length<2){console.error(`曲线 ${s} 的点数据无效`);return}const x=E.userData.wellboreId,w=E.userData.curveType;N.forEach(C=>{try{o.remove(C),C.geometry&&C.geometry.dispose(),C.material&&(Array.isArray(C.material)?C.material.forEach(k=>k.dispose()):C.material.dispose())}catch(k){console.error("清理对象时出错:",k)}}),console.log(`已清理 ${N.length} 个对象，开始创建新的 ${c} 模式曲线`);const F=I.filter(C=>C&&typeof C=="object"&&"x"in C&&"y"in C&&"z"in C);if(F.length<2){console.error(`曲线 ${s} 的有效点数量不足`);return}c==="2D"?ye(F,A,x,w,g,s):c==="3D"?ie(F,A,x,w,g,s,l,n,a,m,h,b):de(F,A,x,w,g),console.log(`${c} 模式曲线创建完成: ${s}`)}catch(N){console.error(`setCurveSetting全局错误 (${s}):`,N)}};function ot(s,g,c){const l=new BufferGeometry;l.setFromPoints(s);const n=new Line(l,g);n.computeLineDistances(),n.userData.lineId=c,o.add(n)}const We=(s,g)=>{if(console.log("生成颜色图例，高度范围:",{minHeight:s,maxHeight:g}),s===void 0||g===void 0||s===g){X.value=!1;return}const c=x=>{let w={r:.5,g:.5,b:.5};return x<.2?w={r:0,g:0,b:.5+x*2.5}:x<.4?w={r:0,g:(x-.2)*5,b:1-(x-.2)*5}:x<.6?w={r:(x-.4)*5,g:1,b:0}:x<.8?w={r:1,g:1-(x-.6)*5,b:0}:w={r:.8,g:.1,b:.1},w},l=c(0),n={r:Math.round(l.r*255),g:Math.round(l.g*255),b:Math.round(l.b*255)},A=c(1),a={r:Math.round(A.r*255),g:Math.round(A.g*255),b:Math.round(A.b*255)};console.log("最低点颜色:",l,"-> RGB:",n),console.log("最高点颜色:",A,"-> RGB:",a);const m=c(.25),h={r:Math.round(m.r*255),g:Math.round(m.g*255),b:Math.round(m.b*255)},b=c(.5),N={r:Math.round(b.r*255),g:Math.round(b.g*255),b:Math.round(b.b*255)},_=c(.75),E={r:Math.round(_.r*255),g:Math.round(_.g*255),b:Math.round(_.b*255)},I=`linear-gradient(to top, 
        rgb(${n.r}, ${n.g}, ${n.b}) 0%, 
        rgb(${h.r}, ${h.g}, ${h.b}) 25%, 
        rgb(${N.r}, ${N.g}, ${N.b}) 50%, 
        rgb(${E.r}, ${E.g}, ${E.b}) 75%, 
        rgb(${a.r}, ${a.g}, ${a.b}) 100%)`;le.value=I,X.value=!0,console.log("生成的CSS渐变:",I),console.log("色标已生成：从最低点颜色到最高点颜色的直接渐变")},Ne=()=>{X.value=!1,le.value=""},Le=()=>{if(!J.value){q.value=[],G.value.forEach(A=>{A&&A.material&&(A.material.clippingPlanes=[],A.material.needsUpdate=!0)});const l=[];o.traverse(A=>{A.name&&A.name.includes("CuttingWireCubeGrid")&&l.push(A),A.userData&&A.userData.isCuttingGrid&&l.push(A)}),l.forEach(A=>{try{console.log(`禁用切割: 移除切割网格对象: ${A.name||"未命名"}`),o.remove(A),A.geometry&&A.geometry.dispose(),A.material&&(Array.isArray(A.material)?A.material.forEach(a=>a.dispose()):A.material.dispose()),A.traverse(a=>{a.geometry&&a.geometry.dispose(),a.material&&(Array.isArray(a.material)?a.material.forEach(m=>m.dispose()):a.material.dispose())})}catch(a){console.error("移除切割网格对象时出错:",a)}});const n=o.getObjectByName("WireCubeGrid");if(n)n.visible=!0;else if(y.value){const A=xe({...y.value,minHeight:y.value.minHeight*L.value});A.name="WireCubeGrid",A.userData.isWireCubeGrid=!0,o.add(A)}return}const s=[];P.value.xMin!==void 0&&P.value.xMin!==null&&s.push(new Plane(new Vector3(1,0,0),-P.value.xMin)),P.value.xMax!==void 0&&P.value.xMax!==null&&s.push(new Plane(new Vector3(-1,0,0),P.value.xMax)),P.value.zMin!==void 0&&P.value.zMin!==null&&s.push(new Plane(new Vector3(0,0,1),-P.value.zMin)),P.value.zMax!==void 0&&P.value.zMax!==null&&s.push(new Plane(new Vector3(0,0,-1),P.value.zMax)),q.value=s,G.value.forEach(l=>{l&&l.material&&(l.material.clippingPlanes=s,l.material.clipShadows=!0,l.material.needsUpdate=!0)});const g=o.getObjectByName("WireCubeGrid");g&&(g.visible=!1);const c=[];if(o.traverse(l=>{l.name&&l.name.includes("CuttingWireCubeGrid")&&c.push(l),l.userData&&l.userData.isCuttingGrid&&c.push(l)}),c.forEach(l=>{try{console.log(`更新切割: 移除切割网格对象: ${l.name||"未命名"}`),o.remove(l),l.geometry&&l.geometry.dispose(),l.material&&(Array.isArray(l.material)?l.material.forEach(n=>n.dispose()):l.material.dispose()),l.traverse(n=>{n.geometry&&n.geometry.dispose(),n.material&&(Array.isArray(n.material)?n.material.forEach(A=>A.dispose()):n.material.dispose())})}catch(n){console.error("移除切割网格对象时出错:",n)}}),y.value)try{const l={...y.value,xMin:P.value.xMin,xMax:P.value.xMax,yMin:P.value.zMin,yMax:P.value.zMax,minHeight:y.value.minHeight*L.value},n=xe(l);n.name="CuttingWireCubeGrid",n.userData.isCuttingGrid=!0,o.add(n)}catch(l){console.error("创建切割网格时出错:",l)}},Be=async(s,g,c)=>{try{const l=`${D}/visual3D/project/GetInterpolationImageData?wellboreId=${g}&datasetId=${c}`,n=await axios.post(l);if(n.data?.success){r=n.data.data.xCoordinates,p=n.data.data.yCoordinates;const A=n.data.data.data;console.log("插值成像数据:",A);const a="-999.250",m=A.map(b=>{const N=b["depth(m)"];if(N==null||isNaN(parseFloat(N)))return null;const _=[];if(Object.keys(b).filter(I=>I.startsWith("gr_interpolated_")&&I.endsWith("(api)")).sort((I,x)=>{const w=parseInt(I.split("_")[2].split("(")[0]),F=parseInt(x.split("_")[2].split("(")[0]);return w-F}).forEach(I=>{const x=b[I];x!==a&&x!==void 0&&x!==null&&!isNaN(parseFloat(x))&&_.push(parseFloat(x))}),_.length>0){const I=String(N).padEnd(14),x=_.map(w=>w.toFixed(3)).join("     ");return`${I}${x}`}return null}).filter(b=>b!==null);if(m.length===0){console.error("没有有效的插值成像数据");return}const h=m.join(`
`);console.log("rawdata:",h),rt(h,s,g,c)}else throw new Error(n.data?.message||"获取插值成像数据失败")}catch(l){console.error("获取插值成像数据错误:",l)}},rt=(s,g,c,l)=>{try{const n=s.split(`
`).filter(W=>W.trim()!==""),A=[];let a=1/0,m=-1/0;if(n.forEach(W=>{const Z=W.trim().split(/\s+/);if(Z.length>=2){const R=Z[0].replace(/,/g,""),Q=parseFloat(R);if(isNaN(Q)||!isFinite(Q)){console.warn(`跳过无效深度值: ${R}`);return}const oe=[];for(let re=1;re<Z.length;re++){const ee=parseFloat(Z[re]);!isNaN(ee)&&isFinite(ee)&&(oe.push(ee),a=Math.min(a,ee),m=Math.max(m,ee))}oe.length>0&&A.push({depth:Q,values:oe})}}),console.log("解析的数据:",A),console.log("数值范围:",{minValue:a,maxValue:m}),A.length===0){console.error("没有有效的插值成像数据");return}if(!isFinite(a)||!isFinite(m)||a===m){console.error("数值范围无效:",{minValue:a,maxValue:m});return}const h=new Lut("rainbow",128);h.setMin(a),h.setMax(m),console.log("LUT设置 - 最小值:",a,"最大值:",m,"范围:",m-a);const b=50,N=64,_=10,E=new BufferGeometry,I=[],x=[],w=[];let F=0;const C=N,k=Math.max(16,A[0].values.length);A.forEach((W,Z)=>{if(!isFinite(W.depth)||!isFinite(L.value)){console.warn(`跳过无效层数据: depth=${W.depth}, heightScale=${L.value}`);return}const R=-Math.abs(W.depth*L.value);if(!isFinite(R)){console.warn(`跳过无效Y坐标: ${R}`);return}let Q=[...W.values];for(;Q.length<16;)Q=Q.concat(W.values);Q=Q.slice(0,16);const oe=Q.map(Ae=>b),re=[...oe,oe[0]],ee=[...Q,Q[0]];for(let Ae=0;Ae<C;Ae++){const ae=Ae/C,ue=ae*2*Math.PI;if(!isFinite(ue)){console.warn(`跳过无效角度: ${ue}`);continue}const be=ae*16,he=Math.floor(be),Ie=he+1,_e=re[he],ge=re[Ie],Qe=ee[he],Oe=ee[Ie],De=be-he;if(!isFinite(_e)||!isFinite(ge)||!isFinite(Qe)||!isFinite(Oe)||!isFinite(De)){console.warn("跳过无效插值参数");continue}const Fe=(1-De)*_e+De*ge,Ye=(1-De)*Qe+De*Oe;if(!isFinite(Fe)||!isFinite(Ye)){console.warn("跳过无效插值结果");continue}const ke=Math.cos(ue)*Fe+r,Te=Math.sin(ue)*Fe+p;if(!isFinite(ke)||!isFinite(R)||!isFinite(Te)){console.warn(`跳过无效坐标: x=${ke}, y=${R}, z=${Te}`);continue}I.push(ke,R,Te);const ve=h.getColor(Ye);ve&&isFinite(ve.r)&&isFinite(ve.g)&&isFinite(ve.b)?x.push(ve.r,ve.g,ve.b):x.push(.5,.5,.5)}});const H=I.length/(3*A.length);if(console.log("每层实际顶点数:",H,"总顶点数:",I.length/3),H<3||!isFinite(H)){console.error("顶点数不足以创建面");return}for(let W=0;W<A.length-1;W++)for(let Z=0;Z<H;Z++){const R=W*H+Z,Q=W*H+(Z+1)%H,oe=(W+1)*H+Z,re=(W+1)*H+(Z+1)%H,ee=I.length/3-1;R<=ee&&Q<=ee&&oe<=ee&&re<=ee&&(w.push(R,oe,Q),w.push(Q,oe,re))}if(console.log("创建的面数:",w.length/3,"顶点数:",I.length/3),I.length===0||x.length===0||w.length===0){console.error("几何体数据不完整:",{positions:I.length,colors:x.length,indices:w.length});return}if(x.length!==I.length){console.error("颜色数组长度与位置数组长度不匹配:",{positions:I.length,colors:x.length});return}E.setAttribute("position",new Float32BufferAttribute(I,3)),E.setAttribute("color",new Float32BufferAttribute(x,3)),E.setIndex(w);try{E.computeVertexNormals()}catch(W){console.error("计算法线时出错:",W);const Z=new Array(I.length).fill(0);for(let R=1;R<Z.length;R+=3)Z[R]=1;E.setAttribute("normal",new Float32BufferAttribute(Z,3))}const j=new MeshLambertMaterial({side:DoubleSide,vertexColors:!0}),O=new Mesh(E,j);if(O.userData={nodeId:g,type:"interpolationImage",minValue:a,maxValue:m,wellboreId:c,datasetId:l},!o.userData.enhancedLighting){const W=o.children.find(oe=>oe.type==="AmbientLight");if(W)W.intensity=.7;else{const oe=new AmbientLight(16777215,.7);o.add(oe)}const Z=new DirectionalLight(16777215,1);Z.position.set(1e4,5e3,1e4),Z.castShadow=!0,o.add(Z);const R=new DirectionalLight(16777215,.6);R.position.set(-1e4,3e3,-1e4),o.add(R);const Q=new PointLight(16777215,.5,5e4);Q.position.set(r,0,p),o.add(Q),o.userData.enhancedLighting=!0,console.log("增强光照系统已启用")}o.add(O),U.value.push(O),console.log("优化插值成像圆柱体已创建 - 平滑度:",C,"段，LUT精度:",512)}catch(n){console.log("创建插值成像圆柱体失败:",n)}},He=s=>{const g=[];U.value.forEach((c,l)=>{c&&c.userData&&c.userData.nodeId===s&&g.push(c)}),o.children.forEach(c=>{c instanceof Mesh&&c.userData&&c.userData.nodeId===s&&c.userData.type==="interpolationImage"&&(g.includes(c)||g.push(c))}),g.length>0?(g.forEach(c=>{o.remove(c),c.geometry&&c.geometry.dispose(),c.material&&(Array.isArray(c.material)?c.material.forEach(l=>l.dispose()):c.material.dispose())}),U.value=U.value.filter(c=>!g.includes(c)),console.log("插值成像模型已移除")):console.warn("未找到ID为",s,"的插值成像模型")},at=()=>{if(!e||!d){console.warn("相机或控制器未初始化");return}const s=new Vector3(Y.value.x,Y.value.y,Y.value.z);console.log("摄像头定位到:",s);const g=e.position.clone(),c=d.target.clone(),l=ne.value;let n;v.value==="south"?n=new Vector3(s.x,s.y,s.z-l):v.value==="north"?n=new Vector3(s.x,s.y,s.z+l):n=new Vector3(s.x+l*.5,s.y+l*.5,s.z+l*.5);const A=1e3,a=Date.now(),m=()=>{const h=Date.now()-a,b=Math.min(h/A,1),N=1-Math.pow(1-b,3),_=g.clone().lerp(n,N),E=c.clone().lerp(s,N);e.position.copy(_),d.target.copy(E),d.update(),b<1?requestAnimationFrame(m):console.log("摄像头定位完成")};m()},st=()=>{if(!e||!d){console.warn("相机或控制器未初始化");return}if(!y.value){console.warn("没有地层模型信息，无法重置视图");return}const s=y.value,g=(s.xMin+s.xMax)/2,c=(s.yMin+s.yMax)/2,l=(s.minHeight*L.value+0)/2,n=s.yMax-s.yMin,A=s.xMax-s.xMin,a=Math.abs(s.minHeight*L.value),m=Math.max(n,A,a);console.log("重置相机视图到场景中心:",{centerX:g,centerY:c,centerZ:l}),Y.value.x=g,Y.value.y=l,Y.value.z=c,ne.value=m*2;let h;v.value==="south"?h=new Vector3(g,l,s.yMin-m*2.5):v.value==="north"?h=new Vector3(g,l,s.yMax+m*2.5):h=new Vector3(g,l+m,c+m);const b=new Vector3(g,l,c),N=e.position.clone(),_=d.target.clone(),E=1e3,I=Date.now(),x=()=>{const w=Date.now()-I,F=Math.min(w/E,1),C=1-Math.pow(1-F,3),k=N.clone().lerp(h,C),H=_.clone().lerp(b,C);e.position.copy(k),d.target.copy(H),d.update(),F<1?requestAnimationFrame(x):console.log("相机视图重置完成")};x()},lt=()=>{console.log("toggleControls 被调用，当前状态:",ce.value),ce.value=!ce.value,console.log("切换后状态:",ce.value)},it=s=>{try{const g={camera:{position:e.position.clone(),target:d.target.clone()},viewMode:v.value,heightScale:L.value,modelSpacing:te.value,clippingEnabled:J.value,clippingBounds:{...P.value},cameraTarget:{...Y.value},cameraDistance:ne.value,terrainModels:G.value.map(c=>({id:c.userData.id,verticalOffset:c.userData.verticalOffset,modelIndex:c.userData.modelIndex})),interpolationModels:U.value.map(c=>({nodeId:c.userData.nodeId,type:c.userData.type,minValue:c.userData.minValue,maxValue:c.userData.maxValue,wellboreId:c.userData.wellboreId,datasetId:c.userData.datasetId})),curves:[]};o.traverse(c=>{if(c instanceof Mesh&&c.userData&&c.userData.curveId){const l={curveId:c.userData.curveId,points:c.userData.points,wellboreId:c.userData.wellboreId,curveType:c.userData.curveType,displayMode:c.userData.displayMode,heightScale:c.userData.heightScale,caliper:c.userData.caliper,originalColor:c.userData.originalColor};c.userData.displayMode==="3D"&&(l.minVal=c.userData.minVal,l.maxVal=c.userData.maxVal,l.minCalibration=c.userData.minCalibration,l.maxCalibration=c.userData.maxCalibration,l.minColor=c.userData.minColor,l.maxColor=c.userData.maxColor,console.log("保存3D曲线参数:",{curveId:l.curveId,minVal:l.minVal,maxVal:l.maxVal,minCalibration:l.minCalibration,maxCalibration:l.maxCalibration,minColor:l.minColor,maxColor:l.maxColor})),g.curves.push(l)}}),localStorage.setItem("threeSceneData"+s,JSON.stringify(g)),console.log("场景已保存到本地存储")}catch(g){console.error("保存场景时出错:",g)}},Ge=async()=>{try{const s=new URLSearchParams(window.location.hash.split("?")[1]),g=s.get("appId"),c=s.get("id");if(!c){console.log("没有找到场景id");return}const l=localStorage.getItem("threeSceneData"+c);if(!l){console.log("没有找到保存的场景数据");return}const n=JSON.parse(l);console.log("加载的场景数据:",n),v.value=n.viewMode,L.value=n.heightScale,te.value=n.modelSpacing,J.value=n.clippingEnabled,P.value={...n.clippingBounds},Y.value={...n.cameraTarget},ne.value=n.cameraDistance,G.value.forEach(a=>{a&&(o.remove(a),a.geometry&&a.geometry.dispose(),a.material&&(Array.isArray(a.material)?a.material.forEach(m=>m.dispose()):a.material.dispose()))}),G.value=[],U.value.forEach(a=>{a&&(o.remove(a),a.geometry&&a.geometry.dispose(),a.material&&(Array.isArray(a.material)?a.material.forEach(m=>m.dispose()):a.material.dispose()))}),U.value=[];const A=[];o.traverse(a=>{a instanceof Mesh&&a.userData&&a.userData.curveId&&A.push(a),a instanceof Line&&a.userData&&a.userData.lineId&&A.push(a)}),A.forEach(a=>{o.remove(a),a.geometry&&a.geometry.dispose(),a.material&&(Array.isArray(a.material)?a.material.forEach(m=>m.dispose()):a.material.dispose())});for(const a of n.terrainModels)z.value=a.id,await Ve(a.id);for(const a of n.interpolationModels)Be(a.nodeId,a.wellboreId,a.datasetId);for(const a of n.curves){const m=a.points.map(h=>({x:h.x,y:h.y,z:h.z,modelData:h.modelData,originalY:h.originalY}));a.displayMode==="2D"?ye(m,a.originalColor,a.wellboreId,a.curveType,a.caliper,a.curveId):a.displayMode==="3D"?(console.log("恢复3D曲线:",{curveId:a.curveId,minVal:a.minVal,maxVal:a.maxVal,minCalibration:a.minCalibration,maxCalibration:a.maxCalibration,minColor:a.minColor,maxColor:a.maxColor}),ie(m,a.originalColor,a.wellboreId,a.curveType,a.caliper,a.curveId,a.minVal,a.maxVal,a.minCalibration,a.maxCalibration,a.minColor,a.maxColor)):de(m,a.originalColor,a.wellboreId,a.curveType,a.caliper)}n.camera&&n.camera.position&&n.camera.target&&(e.position.copy(n.camera.position),d.target.copy(n.camera.target),d.update()),Le(),console.log("场景加载完成")}catch(s){console.log("加载场景时出错:",s)}};return onMounted(()=>{pe(),Me(),Ge()}),onBeforeUnmount(()=>{$e()}),{getDesignCurve:Ee,getChannelData:ze,removeCurve:Ce,updateCurve:fe,viewMode:v,statsInfo:T,filePath:z,maxHeightLabel:M,height80Label:$,height60Label:S,height40Label:K,height20Label:f,minHeightLabel:se,downloadAndProcessFile:Ve,handleNodeCheck:je,modelSpacing:te,adjustModelSpacing:Re,heightScale:L,setCurveSetting:tt,colorLegendStyle:le,isLegendVisible:X,generateColorLegend:We,clearColorLegend:Ne,clippingEnabled:J,clippingBounds:P,clippingPlanes:q,updateClipping:Le,getInterpolationImage:Be,removeInterpolationImage:He,interpolationMeshes:U,cameraTarget:Y,cameraDistance:ne,flyToTarget:at,resetCamera:st,saveScene:it,loadScene:Ge,isControlsCollapsed:ce,toggleControls:lt}}}),_hoisted_1$1={class:"three-container"},_hoisted_2$1=["title"],_hoisted_3={key:0,style:{"font-size":"20px"}},_hoisted_4={key:1,style:{"font-size":"20px"}},_hoisted_5={style:{"margin-top":"5px"}},_hoisted_6={key:0},_hoisted_7={style:{"margin-top":"10px","border-top":"1px solid #666","padding-top":"10px"}},_hoisted_8={style:{"margin-top":"5px"}},_hoisted_9={key:1,id:"colorLabels"},_hoisted_10={id:"maxHeightLabel"},_hoisted_11={id:"minHeightLabel"};function _sfc_render(e,t,o,d,r,p){return openBlock(),createElementBlock("div",_hoisted_1$1,[createBaseVNode("div",{id:"controls",class:normalizeClass({collapsed:e.isControlsCollapsed})},[createBaseVNode("div",{class:"controls-toggle",onClick:t[0]||(t[0]=(...u)=>e.toggleControls&&e.toggleControls(...u)),title:e.isControlsCollapsed?"展开控制面板":"收起控制面板"},[e.isControlsCollapsed?(openBlock(),createElementBlock("span",_hoisted_3,"▶")):(openBlock(),createElementBlock("span",_hoisted_4,"◀"))],8,_hoisted_2$1),createBaseVNode("div",null,[t[24]||(t[24]=createBaseVNode("label",{for:"viewMode"},"视图模式: ",-1)),withDirectives(createBaseVNode("select",{id:"viewMode","onUpdate:modelValue":t[1]||(t[1]=u=>e.viewMode=u)},t[23]||(t[23]=[createBaseVNode("option",{value:"south"},"正南方",-1),createBaseVNode("option",{value:"north"},"正北方",-1)]),512),[[vModelSelect,e.viewMode]])]),createBaseVNode("div",null,[t[25]||(t[25]=createBaseVNode("label",{for:"heightScale"},"高度缩放: ",-1)),withDirectives(createBaseVNode("input",{type:"range",id:"heightScale","onUpdate:modelValue":t[2]||(t[2]=u=>e.heightScale=u),min:"1",max:"10",step:"1"},null,512),[[vModelText,e.heightScale]]),createBaseVNode("span",null,toDisplayString(e.heightScale)+"x",1)]),createBaseVNode("div",_hoisted_5,[createBaseVNode("button",{onClick:t[3]||(t[3]=u=>e.heightScale=1),style:{"margin-right":"5px",padding:"2px 8px","font-size":"12px","background-color":"#4CAF50",color:"white",border:"none","border-radius":"3px",cursor:"pointer"}},"1x"),createBaseVNode("button",{onClick:t[4]||(t[4]=u=>e.heightScale=2),style:{"margin-right":"5px",padding:"2px 8px","font-size":"12px","background-color":"#4CAF50",color:"white",border:"none","border-radius":"3px",cursor:"pointer"}},"2x"),createBaseVNode("button",{onClick:t[5]||(t[5]=u=>e.heightScale=5),style:{"margin-right":"5px",padding:"2px 8px","font-size":"12px","background-color":"#4CAF50",color:"white",border:"none","border-radius":"3px",cursor:"pointer"}},"5x"),createBaseVNode("button",{onClick:t[6]||(t[6]=u=>e.heightScale=10),style:{padding:"2px 8px","font-size":"12px","background-color":"#4CAF50",color:"white",border:"none","border-radius":"3px",cursor:"pointer"}},"10x")]),createBaseVNode("div",null,[createBaseVNode("label",null,[withDirectives(createBaseVNode("input",{type:"checkbox","onUpdate:modelValue":t[7]||(t[7]=u=>e.clippingEnabled=u),onChange:t[8]||(t[8]=(...u)=>e.updateClipping&&e.updateClipping(...u))},null,544),[[vModelCheckbox,e.clippingEnabled]]),t[26]||(t[26]=createTextVNode(" 启用地层切割 "))])]),e.clippingEnabled?(openBlock(),createElementBlock("div",_hoisted_6,[createBaseVNode("div",null,[t[27]||(t[27]=createBaseVNode("label",{for:"xMin"},"X最小值: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"xMin","onUpdate:modelValue":t[9]||(t[9]=u=>e.clippingBounds.xMin=u),onInput:t[10]||(t[10]=(...u)=>e.updateClipping&&e.updateClipping(...u)),step:"100"},null,544),[[vModelText,e.clippingBounds.xMin,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[28]||(t[28]=createBaseVNode("label",{for:"xMax"},"X最大值: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"xMax","onUpdate:modelValue":t[11]||(t[11]=u=>e.clippingBounds.xMax=u),onInput:t[12]||(t[12]=(...u)=>e.updateClipping&&e.updateClipping(...u)),step:"100"},null,544),[[vModelText,e.clippingBounds.xMax,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[29]||(t[29]=createBaseVNode("label",{for:"zMin"},"Z最小值: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"zMin","onUpdate:modelValue":t[13]||(t[13]=u=>e.clippingBounds.zMin=u),onInput:t[14]||(t[14]=(...u)=>e.updateClipping&&e.updateClipping(...u)),step:"100"},null,544),[[vModelText,e.clippingBounds.zMin,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[30]||(t[30]=createBaseVNode("label",{for:"zMax"},"Z最大值: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"zMax","onUpdate:modelValue":t[15]||(t[15]=u=>e.clippingBounds.zMax=u),onInput:t[16]||(t[16]=(...u)=>e.updateClipping&&e.updateClipping(...u)),step:"100"},null,544),[[vModelText,e.clippingBounds.zMax,void 0,{number:!0}]])])])):createCommentVNode("",!0),createBaseVNode("div",_hoisted_7,[t[35]||(t[35]=createBaseVNode("div",{style:{"font-weight":"bold","margin-bottom":"5px",color:"#fff","font-size":"14px"}},"摄像头定位:",-1)),createBaseVNode("div",null,[t[31]||(t[31]=createBaseVNode("label",{for:"targetX"},"目标X: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"targetX","onUpdate:modelValue":t[17]||(t[17]=u=>e.cameraTarget.x=u),step:"100",style:{width:"80px"}},null,512),[[vModelText,e.cameraTarget.x,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[32]||(t[32]=createBaseVNode("label",{for:"targetY"},"目标Y: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"targetY","onUpdate:modelValue":t[18]||(t[18]=u=>e.cameraTarget.y=u),step:"100",style:{width:"80px"}},null,512),[[vModelText,e.cameraTarget.y,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[33]||(t[33]=createBaseVNode("label",{for:"targetZ"},"目标Z: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"targetZ","onUpdate:modelValue":t[19]||(t[19]=u=>e.cameraTarget.z=u),step:"100",style:{width:"80px"}},null,512),[[vModelText,e.cameraTarget.z,void 0,{number:!0}]])]),createBaseVNode("div",null,[t[34]||(t[34]=createBaseVNode("label",{for:"cameraDistance"},"相机距离: ",-1)),withDirectives(createBaseVNode("input",{type:"number",id:"cameraDistance","onUpdate:modelValue":t[20]||(t[20]=u=>e.cameraDistance=u),min:"100",max:"10000",step:"100",style:{width:"80px"}},null,512),[[vModelText,e.cameraDistance,void 0,{number:!0}]])]),createBaseVNode("div",_hoisted_8,[createBaseVNode("button",{onClick:t[21]||(t[21]=(...u)=>e.flyToTarget&&e.flyToTarget(...u)),style:{padding:"5px 10px",background:"#007bff",color:"white",border:"none","border-radius":"3px",cursor:"pointer","font-weight":"bold"}},"定位到目标"),createBaseVNode("button",{onClick:t[22]||(t[22]=(...u)=>e.resetCamera&&e.resetCamera(...u)),style:{"margin-left":"5px",padding:"5px 10px",background:"#6c757d",color:"white",border:"none","border-radius":"3px",cursor:"pointer","font-weight":"bold"}},"重置视图")])])],2),e.isLegendVisible?(openBlock(),createElementBlock("div",{key:0,id:"colorLegend",style:normalizeStyle({background:e.colorLegendStyle})},null,4)):createCommentVNode("",!0),e.isLegendVisible?(openBlock(),createElementBlock("div",_hoisted_9,[createBaseVNode("div",_hoisted_10,toDisplayString(e.maxHeightLabel),1),createBaseVNode("div",_hoisted_11,toDisplayString(e.minHeightLabel),1)])):createCommentVNode("",!0)])}const ThreeDShows=_export_sfc(_sfc_main$1,[["render",_sfc_render],["__scopeId","data-v-0177d523"]]),_hoisted_1={class:"layout-box"},_hoisted_2={class:"dock-container"},_sfc_main=defineComponent({__name:"index",setup(e){const t=ref(),o=ref(),d=ref(),r=ref();ref(),ref();const p=ref({formName:"基本属性",formNode:"",options:[{label:"颜色",value:"#ff0000",type:"color",order:1},{label:"粗细",value:"",type:"number",order:2},{label:"展示方式",value:"normal",type:"select",dicData:[{label:"正常",value:"normal"},{label:"2D",value:"2D"},{label:"3D",value:"3D"}],order:4},{label:"Min Value",value:"",group:"config",type:"input"},{label:"Max Value",value:"",group:"config",type:"input"},{label:"Min Calibrate",value:"",group:"config",type:"input",order:6},{label:"Max Calibrate",value:"",group:"config",type:"input",order:7},{label:"Min Color",value:"#ff0000",group:"config",type:"color",order:8},{label:"Max Color",value:"#ff0000",group:"config",type:"color",order:9}]}),D=ref([{label:"File",value:"first1",types:"3dviews",children:[{label:"Create Project",value:"createProject",icon:"CirclePlus"},{label:"Add Well",value:"importWell",icon:"Upload"},{label:"Add Model",value:"importModel",icon:"Upload"},{label:"Add Wellbore",value:"importWellbore",icon:"Upload"},{label:"Add Curve",value:"importCurve",icon:"Upload"}]}]),v=applyVueInReact(ThreeDShows,{forwardRef:!0,useReactWrapper:!0}),T=applyVueInReact(ProjectTree,{forwardRef:!0,useReactWrapper:!0});applyVueInReact(_sfc_main$6,{forwardRef:!0,useReactWrapper:!0});const z=applyVueInReact(Property,{forwardRef:!0,useReactWrapper:!0});function V(M){o.value?o.value.__veauryVueRef__.setCheckNode(M):console.error("ResourceTree 组件实例未找到")}const y=computed(()=>({width:"100%",height:"calc(100vh - 40px)",background:"#1E1E1E",color:"#CCCCCC"})),G={default:{floatable:!0,maximizable:!0,tabLocked:!1,newWindow:!0,preferredFloatWidth:[300,800],preferredFloatHeight:[200,600]},main:{floatable:!0,maximizable:!0,tabLocked:!1,newWindow:!0,preferredFloatWidth:[400,1e3],preferredFloatHeight:[300,800]}},te=computed(()=>({defaultLayout:B,style:y.value,groups:G,dropMode:"default",mode:"horizontal",maximizable:!0,floatable:!0,newWindow:!0,disableDock:!1,draggable:!0,resizable:!0,onLayoutChange:M=>{const $=S=>{S.children&&S.children.forEach(K=>{K.activeId&&K.group==="main"&&V(K.activeId),K.children&&$(K)})};M.dockbox&&$(M.dockbox)}})),B={dockbox:{mode:"horizontal",children:[{mode:"vertical",size:250,children:[{id:"right",group:"right",tabs:[{id:"explorer",title:"Files",content:reactExports.createElement(T,{ref:M=>{o.value=M},onSaveScene:M=>{console.log("index.vue 接收到save-scene事件:",M),d.value?d.value.__veauryVueRef__.saveScene(M):console.error("threedShowRef.value未定义")},onCheckboxChoose:M=>{console.log("index.vue 接收到checkbox-choose事件:",M),d.value?d.value.__veauryVueRef__.handleNodeCheck(M.id,M.curveType,M.isChecked,M.moduleType,M.wellboreId==""?null:M.wellboreId,M.datasetId==""?null:M.datasetId):console.error("threedShowRef.value未定义")},onSettingChange:(M,$,S,K,f,se,le,X,J,P)=>{if(r.value){console.log("进入onSettingChange");let q=[];M.label==="实际轨迹"||M.label==="设计轨迹"?q=[{label:"颜色",value:se||"#ff0000",type:"color",order:1},{label:"粗细",value:$,type:"number",order:2}]:q=[{label:"颜色",value:se||"#ff0000",type:"color",order:1},{label:"粗细",value:$,type:"number",order:2},{label:"展示方式",value:S,type:"select",dicData:[{label:"正常",value:"normal"},{label:"2D",value:"2D"},{label:"3D",value:"3D"}],order:3},{label:"Min Value",value:K,group:"config",disabled:!0,type:"input",order:4},{label:"Max Value",value:f,group:"config",disabled:!0,type:"input",order:5},{label:"Min Calibrate",value:le,group:"config",type:"input",order:6},{label:"Max Calibrate",value:X,group:"config",type:"input",order:7},{label:"Min Color",value:J||"#ff0000",group:"config",type:"color",order:8},{label:"Max Color",value:P||"#ff0000",group:"config",type:"color",order:9}],p.value={formName:"基本属性",formNode:M,options:q},r.value.__veauryVueRef__.init(p.value)}}}),closable:!1,cached:!0,group:"left"}]}]},{mode:"vertical",size:1e3,children:[{id:"main",group:"main",tabs:[{id:"ThreeDShows",title:"3DVisual",content:reactExports.createElement(v,{ref:M=>{d.value=M},onClick:(M,$,S)=>{}}),closable:!1,cached:!0,group:"left"}],panelLock:{panelStyle:"main",minWidth:400,minHeight:200}}]},{mode:"vertical",size:300,children:[{id:"right",group:"right",tabs:[{id:"property",title:"property",content:reactExports.createElement(z,{ref:M=>{r.value=M},formOption:p.value,onUpdateAvueForm:(M,$,S)=>{console.log(M,$.prop,S)},onApply:M=>{if(o.value&&o.value.__veauryVueRef__.updateSettingCache(M),d.value){const $=M.formNode,S=M.options.find(U=>U.label==="颜色")?.value||"#ff0000",K=M.options.find(U=>U.label==="粗细")?.value||1,f=M.options.find(U=>U.label==="展示方式")?.value||"normal",se=M.options.find(U=>U.label==="Min Value")?.value||0,le=M.options.find(U=>U.label==="Max Value")?.value||100,X=M.options.find(U=>U.label==="Min Calibrate").value,J=M.options.find(U=>U.label==="Max Calibrate").value,P=M.options.find(U=>U.label==="Min Color")?.value||"#FF0000",q=M.options.find(U=>U.label==="Max Color")?.value||"#FF0000";$.label==="实际轨迹"?d.value.__veauryVueRef__.setCurveSetting($.wellboreId+"_1",K,f,null,null,S):$.label==="设计轨迹"?d.value.__veauryVueRef__.setCurveSetting($.wellboreId+"_0",K,f,null,null,S):$.moduleType===7?d.value.__veauryVueRef__.setCurveSetting($.id+"_99",K,f,se,le,S,X,J,P,q):d.value.__veauryVueRef__.setCurveSetting($.id+"_99",K,f,null,null,S,X,J,P,q)}},onReset:M=>{if(console.log("重置按钮点击，表单数据：",M),d.value){console.log("重置setting",M);const $=M.formNode,S=M.options.find(U=>U.label==="颜色")?.value||"#ff0000",K=M.options.find(U=>U.label==="粗细")?.value||1,f=M.options.find(U=>U.label==="展示方式")?.value||"normal",se=M.options.find(U=>U.label==="Min Value")?.value||0,le=M.options.find(U=>U.label==="Max Value")?.value||100,X=M.options.find(U=>U.label==="Min Calibrate").value,J=M.options.find(U=>U.label==="Max Calibrate").value,P=M.options.find(U=>U.label==="Min Color")?.value||"#FF0000",q=M.options.find(U=>U.label==="Max Color")?.value||"#FF0000";$.label==="实际轨迹"?d.value.__veauryVueRef__.setCurveSetting($.wellboreId+"_1",K,f,null,null,S):$.label==="设计轨迹"?d.value.__veauryVueRef__.setCurveSetting($.wellboreId+"_0",K,f,null,null,S):$.moduleType===7?d.value.__veauryVueRef__.setCurveSetting($.id+"_99",K,f,se,le,S,X,J,P,q):d.value.__veauryVueRef__.setCurveSetting($.id+"_99",K,f,null,null,S,X,J,P,q)}if(o.value&&M&&M.formNode&&M.options){const $=M.formNode.id,S=M.options,K=S.find(Y=>Y.label==="粗细")?.value,f=S.find(Y=>Y.label==="展示方式")?.value,se=S.find(Y=>Y.label==="Min Value")?.value,le=S.find(Y=>Y.label==="Max Value")?.value,X=S.find(Y=>Y.label==="颜色")?.value,J=S.find(Y=>Y.label==="Min Calibrate")?.value,P=S.find(Y=>Y.label==="Max Calibrate")?.value,q=S.find(Y=>Y.label==="Min Color")?.value,U=S.find(Y=>Y.label==="Max Color")?.value;o.value.__veauryVueRef__.settingCache[$]={wellboreThickness:K,min:se,max:le,displayMode:f,color:X,minCalibration:J,maxCalibration:P,minColor:q,maxColor:U},console.log("重置时直接更新缓存")}}}),closable:!1}]}]}]}};onMounted(()=>{});const L=applyReactInVue(DockLayout);return(M,$)=>(openBlock(),createElementBlock("div",_hoisted_1,[createVNode(_sfc_main$7,{"new-tabs":D.value},null,8,["new-tabs"]),createBaseVNode("div",_hoisted_2,[createVNode(unref(L),mergeProps({ref_key:"dockRef",ref:t},te.value),null,16)])]))}});export{_sfc_main as default};
