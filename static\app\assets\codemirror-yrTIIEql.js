import{g as a}from"./index-B5fOQYc3.js";import{r as f}from"./codemirror-Dcz-OE1i.js";function s(o,c){for(var t=0;t<c.length;t++){const r=c[t];if(typeof r!="string"&&!Array.isArray(r)){for(const e in r)if(e!=="default"&&!(e in o)){const i=Object.getOwnPropertyDescriptor(r,e);i&&Object.defineProperty(o,e,i.get?i:{enumerable:!0,get:()=>r[e]})}}}return Object.freeze(Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}))}var n=f();const m=a(n),d=s({__proto__:null,default:m},[n]);export{d as c};
