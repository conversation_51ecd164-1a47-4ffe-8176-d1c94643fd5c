import{g as C}from"./index-B5fOQYc3.js";import{r as v}from"./codemirror-Dcz-OE1i.js";function b(p,s){for(var r=0;r<s.length;r++){const n=s[r];if(typeof n!="string"&&!Array.isArray(n)){for(const l in n)if(l!=="default"&&!(l in p)){const a=Object.getOwnPropertyDescriptor(n,l);a&&Object.defineProperty(p,l,a.get?a:{enumerable:!0,get:()=>n[l]})}}}return Object.freeze(Object.defineProperty(p,Symbol.toStringTag,{value:"Module"}))}var h={exports:{}},g;function x(){return g||(g=1,function(p,s){(function(r){r(v())})(function(r){r.defineOption("placeholder","",function(e,t,o){var u=o&&o!=r.Init;if(t&&!u)e.on("blur",f),e.on("change",i),e.on("swapDoc",i),r.on(e.getInputField(),"compositionupdate",e.state.placeholderCompose=function(){a(e)}),i(e);else if(!t&&u){e.off("blur",f),e.off("change",i),e.off("swapDoc",i),r.off(e.getInputField(),"compositionupdate",e.state.placeholderCompose),n(e);var c=e.getWrapperElement();c.className=c.className.replace(" CodeMirror-empty","")}t&&!e.hasFocus()&&f(e)});function n(e){e.state.placeholder&&(e.state.placeholder.parentNode.removeChild(e.state.placeholder),e.state.placeholder=null)}function l(e){n(e);var t=e.state.placeholder=document.createElement("pre");t.style.cssText="height: 0; overflow: visible",t.style.direction=e.getOption("direction"),t.className="CodeMirror-placeholder CodeMirror-line-like";var o=e.getOption("placeholder");typeof o=="string"&&(o=document.createTextNode(o)),t.appendChild(o),e.display.lineSpace.insertBefore(t,e.display.lineSpace.firstChild)}function a(e){setTimeout(function(){var t=!1;if(e.lineCount()==1){var o=e.getInputField();t=o.nodeName=="TEXTAREA"?!e.getLine(0).length:!/[^\u200b]/.test(o.querySelector(".CodeMirror-line").textContent)}t?l(e):n(e)},20)}function f(e){d(e)&&l(e)}function i(e){var t=e.getWrapperElement(),o=d(e);t.className=t.className.replace(" CodeMirror-empty","")+(o?" CodeMirror-empty":""),o?l(e):n(e)}function d(e){return e.lineCount()===1&&e.getLine(0)===""}})}()),h.exports}var y=x();const N=C(y),P=b({__proto__:null,default:N},[y]);export{P as p};
