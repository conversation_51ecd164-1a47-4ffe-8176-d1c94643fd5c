    /**
     * 创建弹框HTML结构
     */
    function createDialogHTML() {
        const dialogHTML = `
             <div class="geo-annotation-dialog">
             <div class="geo-annotation-dialog-body">
                <div class="header-section">
                    <div class="header-title">标注列表</div>
                    <div class="toolbar">
                        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" id="showAnnotationBtn" title="显示标注"><i class="fa fa-eye" aria-hidden="true"></i></a>
                        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" id="addAnnotationBtn"   title="新增"><i class="fa fa-plus" aria-hidden="true"></i></a>
                        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" id="deleteSelectedBtn" title="删除"><i class="fa fa-minus" aria-hidden="true"></i></a>
                    </div>
                </div>

                <div class="table-container">
                    <table class="annotation-table" id="annotationTable">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <label class="checkbox-wrapper">
                                        <input type="checkbox" class="select-all-checkbox">
                                    </label>
                                </th>
                                <th class="sequence-cell">序号</th>
                                <th class="depth-cell">
                                    <div class="depth-header">
                                        <span class="depth-title">测深值</span>
                                        <div class="sort-button" id="depthSortBtn" data-sort="none">
                                            <div class="sort-arrow sort-arrow-up">▲</div>
                                            <div class="sort-arrow sort-arrow-down">▼</div>
                                        </div>
                                    </div>
                                </th>
                                <th class="description-cell">标注说明</th>
                            </tr>
                        </thead>
                        <tbody id="annotationTableBody">
                        </tbody>
                    </table>
                </div>
            
                <div class="summary-section">
                    <div class="summary-label">总结说明</div>
                    <textarea class="summary-textarea" id="dialog_summaryText" placeholder="请输入总结说明..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <a href="#" class="easyui-linkbutton" id="saveBtn" iconCls="linkbtn-check"  title="保存">保存</a>
                <a href="#" class="easyui-linkbutton" id="cancelBtn" iconCls="linkbtn-cancel"  title="取消">取消</a>
            </div>
        </div>
        `;
        
        $('#addLabelDialog').html(dialogHTML);
        return dialogHTML; // 返回HTML字符串用于测试
    }
    // 全局变量存储回调函数
    let labelDialogCallbacks = {
        onSave: null
    };
    function openLabelDialog () {
        $('#addLabelDialog').dialog({
            title: '标注',
            width: 1200,
            height: 750,
            modal: true,
            center: true,
            maximizable: false,
            resizable: true,
            minimizable: false,
            collapsible: false,
            onOpen: () => {
                // 解析EasyUI组件
                $('#addLabelDialog').find('.easyui-linkbutton').linkbutton();
                // getReportAnnotations();

                if(labelInfo.Annotations.length > 0) {
                    console.log('初始化加载数据', labelInfo)
                    initLabelInfo(labelInfo)
                }

                // 初始化排序按钮状态
                currentSortState = 'none';
                const $sortBtn = $('#depthSortBtn');
                if ($sortBtn.length > 0) {
                    updateSortButtonState($sortBtn, 'none');
                }
            },
            onClose: () => {
                // 重置状态
                labelDialogCallbacks.onSave = null;
            },
            onBeforeClose: () => {

            },  
            onResize: (width, height) => {
                
                //重新居中显示
                $('#addLabelDialog').dialog('center');
            }
        });
    }
    
    function showLabel(options = {}) {
        console.log('options', options)
        labelDialogCallbacks = {
            onSave: options.onSave || null,
        };
        // 如果弹框不存在，则创建
        createDialogHTML();
        openLabelDialog();
    }

    // 初始化按钮状态
    let isAnnotationVisible = true; // 当前显示状态

    // 绑定点击事件
    $(document).on('click', '#showAnnotationBtn', function() {
        if (isAnnotationVisible) {
            // 当前是显示状态，切换为隐藏
            // 更新按钮样式和提示
            $(this).find('i').removeClass('fa-eye').addClass('fa-eye-slash');
            $(this).attr('title', '显示标注');
            isAnnotationVisible = false;
        } else {
            // 当前是隐藏状态，切换为显示
            // 更新按钮样式和提示
            $(this).find('i').removeClass('fa-eye-slash').addClass('fa-eye');
            $(this).attr('title', '隐藏标注');
            isAnnotationVisible = true;
        }
    });

    /**
     * 显示表格加载状态
     */
    function showTableLoading() {
        const $tableContainer = $('.table-container');
        if ($tableContainer.length === 0) return;

        // 如果已经存在加载层，先移除
        $tableContainer.find('.table-loading-overlay').remove();

        // 创建加载层HTML
        const loadingHTML = `
            <div class="table-loading-overlay">
                <div class="table-loading-content">
                    <div class="table-loading-spinner"></div>
                    <div class="table-loading-text">数据加载中...</div>
                </div>
            </div>
        `;

        // 添加加载层到table-container
        $tableContainer.append(loadingHTML);
    }

    /**
     * 隐藏表格加载状态
     */
    function hideTableLoading() {
        $('.table-loading-overlay').remove();
    }

    async function drawLabel () {
        // if (!nowProjectInfo) return;
        const url = setTokenUrl(
            vWebApiUrl +
            "/GeoSteering/GetReportAnnotations?projectId=" +
            nowProjectInfo.Id
        );
        await $.get({
            url: url,
            dataType:'json',
            success: function (data) {
                if (typeof data == "undefined" || data == "") {
                    $.messager.alert(
                        "提示信息",
                        "网络返回数据异常，请检查网络连接是否正常。",
                        "info"
                    );
                }
                labelInfo = data.Data; //将数据保存到全局
                console.log(data.Data, 'data======')
                isAnnotationVisible = data.Data.IsDisplay;
                if (!isAnnotationVisible) {
                    // 如果是隐藏就不需要绘制点
                    return;
                }
                if (labelInfo.Annotations.length > 0) {
                    //更新导向报告标记点
                    const depths = labelInfo.Annotations.map(item => item.Depth);
                    plot.updateReportMark(depths);
                }
                
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // 隐藏加载状态
                hideTableLoading();
                $.messager.alert("提示信息", "发生错误！", "info");
            },
        });
         const WellboreId = nowProjectInfo.WellboreId;
        const OilWellbore = nowProjectInfo.OilWellbore;
        const curWellbore = OilWellbore.find(item => item.Id === WellboreId);
        //获取完钻深度
        GetData(setTokenUrl(
            vWebApiUrl +
            "/GeoSteering/GetReportInfo?wellboreId=" +
            curWellbore.Id +
            "&wellId=" + curWellbore.WellId), "", (data) => {
                if (data.Success) {
                        addlabel_curWellbore = data.Data
                        console.log('addlabel_curWellbore', addlabel_curWellbore)
                    }
        });
    }

    function getReportAnnotations () {
        // 显示加载状态
        showTableLoading();

        const url = setTokenUrl(
            vWebApiUrl +
            "/GeoSteering/GetReportAnnotations?projectId=" +
            nowProjectInfo.Id
        );
        $.get({
            url: url,
            dataType:'json',
            success: function (data) {
                // 隐藏加载状态
                hideTableLoading();

                if (typeof data == "undefined" || data == "") {
                    $.messager.alert(
                        "提示信息",
                        "网络返回数据异常，请检查网络连接是否正常。",
                        "info"
                    );
                }
                labelInfo = data.Data; //将数据保存到全局
                initLabelInfo(labelInfo);

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // 隐藏加载状态
                hideTableLoading();
                $.messager.alert("提示信息", "发生错误！", "info");
            },
        });
    }

    function saveReportAnnotations(body, msg, close) {
      const url = setTokenUrl(
        vWebApiUrl +
          "/GeoSteering/SaveReportAnnotations"
      );
      $.post({
        url: url,
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        data: JSON.stringify(body),
        success: function(data) {
          console.log(data, "data---");
          if (typeof data == "undefined" || data == "") {
            $.messager.alert(
              "提示信息",
              "网络返回数据异常，请检查网络连接是否正常。",
              "info"
            );
          }
            //更新导向报告标记点
            const depths = body.annotations.map(item => item.depth);
            if (isAnnotationVisible) {
                try {
                    console.log(depths, 'depths', isAnnotationVisible)
                    //先清空
                    // plot.updateReportMark([]);
                    plot.updateReportMark(depths);
                } catch(err) {
                    $.messager.alert("提示信息", "更新导向报告标记点失败！", "info");
                    console.error('更新导向报告标记点失败:', err);
                }
            } else {
                    //隐藏标记
                    plot.updateReportMark([]);
            }
            
            
            $.messager.show({
                        title: `${msg}成功`,
                        msg: `标注内容已${msg}成功！`,
                        timeout: 3000,
                        showType: 'slide'
                    });
            // 调用更新文字，重新获取图片回调
            if (labelDialogCallbacks.onSave && labelDialogCallbacks.onSave instanceof Function) {
                labelDialogCallbacks.onSave();
            }
            //更新标记点存储的数据 
            getReportAnnotations()
            if (close) {
                $('#addLabelDialog').dialog('close');
            }
            
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
          $.messager.alert("提示信息", "发生错误！", "info");
        },
      });
    }

    function deleteReportAnnotations() {
        const annotations = [];
        
        $('#annotationTableBody tr').each(function() {
            const depth = $(this).find('.depth-input').val().trim();
            const description = $(this).find('.description-input').val().trim();
            const index = $(this).find('td:eq(1)').text();
            
            if (depth === '') {
                hasEmptyDepth = true;
                return false; // 跳出循环
            }
            if (description === '') {
                hasEmptyDescription = true;
                return false; // 跳出循环
            }
            
            if (depth !== '' || description !== '' || index !== '') {
                annotations.push({
                    depth: parseFloat(depth) || 0,
                    content: description,
                    order: index
                });
            }
        });
        const summary = $('#dialog_summaryText').val();
        const data = {
            projectId: nowProjectInfo.Id,
            annotations: annotations,
            summary: summary,
            IsDisplay: isAnnotationVisible
        };

        saveReportAnnotations(data, '删除', false)
    }

    let labelInfo = {}
    function initLabelInfo() {
        if (labelInfo) {
            const Annotations = labelInfo.Annotations;
            const Summary = labelInfo.Summary;
            const IsDisplay = labelInfo.IsDisplay ? true : false;
            
            // 清空表格内容
            $('#annotationTableBody').empty();
            
            // 添加新行
            Annotations.forEach((annotation, index) => {
                const newRow = `
                    <tr>
                        <td>
                            <label class="checkbox-wrapper">
                                <input type="checkbox" class="row-checkbox">
                            </label>
                        </td>
                        <td>${Number(annotation.Order)}</td>
                        <td class="depth-cell-content">
                            <span class="depth-text">${annotation.Depth}</span>
                            <input type="text" placeholder="请输入测深值" class="depth-input" value="${annotation.Depth}" style="display: none;">
                        </td>
                        <td class="description-cell-content">
                            <span class="description-text">${annotation.Content}</span>
                            <textarea placeholder="请输入标注说明" class="description-input" style="display: none;">${annotation.Content}</textarea>
                        </td>
                    </tr>
                `;
                $('#annotationTableBody').append(newRow);
            });

            // 设置总结说明
            $('#dialog_summaryText').val(Summary);

            // 重置选择状态
            $('.row-checkbox').prop('checked', false);
            $('.select-all-checkbox').prop('checked', false);
            $('.select-all-checkbox').prop('indeterminate', false);

            // 重置排序状态
            currentSortState = 'none';
            const $sortBtn = $('#depthSortBtn');
            if ($sortBtn.length > 0) {
                updateSortButtonState($sortBtn, 'none');
            }
            
            if (IsDisplay) {
                // 显示标记点
                // 更新按钮样式和提示
                $('#showAnnotationBtn').find('i').removeClass('fa-eye-slash').addClass('fa-eye');
                $('#showAnnotationBtn').attr('title', '隐藏标注');
            } else {
                // 更新按钮样式和提示
                $('#showAnnotationBtn').find('i').removeClass('fa-eye').addClass('fa-eye-slash');
                $('#showAnnotationBtn').attr('title', '显示标注');
            }
        }
    }
    let annotationCounter = 2; // 当前已有2行
    
    // 添加标注行
    // $('#addAnnotationBtn').click(function() {
    $(document).on('click', '#addAnnotationBtn', function() {
        annotationCounter++;
        const newRow = `
            <tr>
                <td><input type="checkbox" class="row-checkbox"></td>
                <td>${annotationCounter}</td>
                <td><input type="text" placeholder="请输入测深值" class="depth-input"></td>
                <td><textarea placeholder="请输入标注说明" class="description-input"></textarea></td>
            </tr>
        `;
        $('#annotationTableBody').append(newRow);
        updateRowNumbers();

        // 滚动到表格底部显示新添加的行
        const tableContainer = $('.table-container');
        tableContainer.scrollTop(tableContainer[0].scrollHeight);
    });
    
    // 删除选中的标注行
    // $('#deleteSelectedBtn').click(function() {
    $(document).on('click', '#deleteSelectedBtn', function() {
        const checkedRows = $('.row-checkbox:checked').closest('tr');
        if (checkedRows.length === 0) {
            $.messager.alert('提示', '请先选择要删除的标注行！', 'warning');
            return;
        }
        
        $.messager.confirm('确认', '确定要删除选中的标注行吗？', function(r) {
            if (r) {
                checkedRows.remove();
                updateRowNumbers();
                const totalCheckboxes = $('.row-checkbox').length;
                const checkedCheckboxes = $('.row-checkbox:checked').length;
                if (totalCheckboxes === 0 || checkedCheckboxes === 0) {
                    $('.select-all-checkbox').prop('checked', false);
                    $('.select-all-checkbox').prop('indeterminate', false);
                }
                deleteReportAnnotations()
            }
        });
    });
    
    // 全选功能
    $(document).on('change', 'th input[type="checkbox"]', function() {
        const isChecked = $(this).prop('checked');
        $('.row-checkbox').prop('checked', isChecked);
    });
    
    // 单行选择时更新全选状态
    $(document).on('change', '.row-checkbox', function() {
        const totalRows = $('.row-checkbox').length;
        const checkedRows = $('.row-checkbox:checked').length;
        const selectAllCheckbox = $('th input[type="checkbox"]');
        
        if (checkedRows === 0) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', false);
        } else if (checkedRows === totalRows) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    });

    // 排序状态管理
    let currentSortState = 'none'; // none, asc, desc

    // 测深值排序按钮点击事件
    $(document).on('click', '#depthSortBtn', function() {
        const $btn = $(this);

        // 状态切换逻辑：none -> asc -> desc -> none
        switch (currentSortState) {
            case 'none':
                currentSortState = 'asc';
                sortByDepth(true);
                break;
            case 'asc':
                currentSortState = 'desc';
                sortByDepth(false);
                break;
            case 'desc':
                currentSortState = 'none';
                // 恢复原始顺序（按序号排序）
                restoreOriginalOrder();
                break;
        }

        // 更新按钮状态
        updateSortButtonState($btn, currentSortState);
    });

    // 更新排序按钮状态
    function updateSortButtonState($btn, state) {
        $btn.attr('data-sort', state);
        const $upArrow = $btn.find('.sort-arrow-up');
        const $downArrow = $btn.find('.sort-arrow-down');

        // 重置所有箭头状态
        $upArrow.removeClass('arrow-active');
        $downArrow.removeClass('arrow-active');

        // 根据状态设置激活的箭头
        switch (state) {
            case 'asc':
                $upArrow.addClass('arrow-active');
                break;
            case 'desc':
                $downArrow.addClass('arrow-active');
                break;
            case 'none':
            default:
                // 默认状态，两个箭头都不激活
                break;
        }
    }

    // 恢复原始顺序（按序号排序）
    function restoreOriginalOrder() {
        const tbody = $('#annotationTableBody');
        const rows = tbody.find('tr').toArray();

        // 按序号排序（第二列）
        rows.sort(function(a, b) {
            const indexA = parseInt($(a).find('td:eq(1)').text()) || 0;
            const indexB = parseInt($(b).find('td:eq(1)').text()) || 0;
            return indexA - indexB;
        });

        tbody.empty().append(rows);
        updateRowNumbers();
    }

    // 排序功能
    function sortByDepth(ascending) {
        const tbody = $('#annotationTableBody');
        const rows = tbody.find('tr').toArray();

        rows.sort(function(a, b) {
            // 优先从显示文本获取测深值，如果为空或"点击编辑"则从输入框获取
            let depthA = $(a).find('.depth-text').text().trim();
            let depthB = $(b).find('.depth-text').text().trim();

            if (depthA === '点击编辑' || depthA === '') {
                depthA = $(a).find('.depth-input').val();
            }
            if (depthB === '点击编辑' || depthB === '') {
                depthB = $(b).find('.depth-input').val();
            }

            const numA = parseFloat(depthA) || 0;
            const numB = parseFloat(depthB) || 0;

            if (ascending) {
                return numA - numB;
            } else {
                return numB - numA;
            }
        });

        tbody.empty().append(rows);
        updateRowNumbers();

        const sortType = ascending ? '升序' : '降序';
        $.messager.show({
            title: '排序完成',
            msg: `已按测深值${sortType}排列！`,
            timeout: 2000,
            showType: 'slide'
        });
    }
    
    // 更新行号
    function updateRowNumbers() {
        $('#annotationTableBody tr').each(function(index) {
            $(this).find('td:eq(1)').text(index + 1);
        });
    }
    
    // 保存功能
    // $('#saveBtn').click(function() {
    $(document).on('click', '#saveBtn', function() {
        const annotations = [];
        let hasEmptyDepth = false;
        let hasEmptyDescription = false;
        
        $('#annotationTableBody tr').each(function() {
            const depth = $(this).find('.depth-input').val().trim();
            const description = $(this).find('.description-input').val().trim();
            const index = $(this).find('td:eq(1)').text();
            
            if (depth === '') {
                hasEmptyDepth = true;
                return false; // 跳出循环
            }
            if (description === '') {
                hasEmptyDescription = true;
                return false; // 跳出循环
            }
            
            if (depth !== '' || description !== '' || index !== '') {
                annotations.push({
                    depth: parseFloat(depth) || 0,
                    content: description,
                    order: index
                });
            }
        });
        
        if (hasEmptyDepth) {
            $.messager.alert('验证失败', '请填写所有标注点的测深值！', 'warning');
            return;
        }

        if (hasEmptyDescription) {
            $.messager.alert('验证失败', '请填写所有标注点的标注说明！', 'warning');
            return;
        }
        
        const summary = $('#dialog_summaryText').val().trim();

        // if (summary === '') {
        //     $.messager.alert('验证失败', '请填写总结说明！', 'warning');
        //     return;
        // }
        
        const data = {
            projectId: nowProjectInfo.Id,
            annotations: annotations,
            summary: summary,
            IsDisplay: isAnnotationVisible
        };
        
        // 模拟保存到工程
        console.log('保存的数据:', data);
        
        $.messager.confirm('保存确认', '确定要保存当前标注内容吗？', function(r) {
            if (r) {
                saveReportAnnotations(data, '保存', true)
            }
        });
    });
    
    // 取消功能
    // $('#cancelBtn').click(function() {
    $(document).on('click', '#cancelBtn', function() {
        $.messager.confirm('取消确认', '确定要放弃当前编辑的内容吗？', function(r) {
            if (r) {
                // 重置状态
                labelDialogCallbacks.onSave = null;
                $('#addLabelDialog').dialog('close');
            }
        });
    });
    
    // 添加全选复选框到表头
    $('th.checkbox-cell').html('<input type="checkbox" id="selectAll">');

    // 数值输入验证
    $(document).on('input', '.depth-input', function() {
        const $input = $(this);
        const value = $input.val();
        const $cell = $input.closest('.depth-cell-content');

        // 移除之前的错误提示
        $cell.find('.error-tip').remove();
        $input.removeClass('input-error');

        // 只允许数字、小数点和负号
        const numericValue = value.replace(/[^0-9.-]/g, '');

        if (value !== numericValue) {
            $input.val(numericValue);
            $input.addClass('input-error');
            
            // 添加错误提示
            const errorTip = '<div class="error-tip">只能输入数值</div>';
            $cell.append(errorTip);
            
            // 3秒后自动移除提示
            setTimeout(function() {
                $cell.find('.error-tip').fadeOut(300, function() {
                    $(this).remove();
                });
                $input.removeClass('input-error');
            }, 2000);
        }

        // 验证数值格式（多个小数点、多个负号等）
        if (numericValue && !isValidNumber(numericValue)) {
            $input.addClass('input-error');
            const errorTip = '<div class="error-tip">请输入有效的数值</div>';
            $cell.append(errorTip);
            
            setTimeout(function() {
                $cell.find('.error-tip').fadeOut(300, function() {
                    $(this).remove();
                });
                $input.removeClass('input-error');
            }, 3000);
        }

        // 只有当输入是完整有效数字时才进行范围校验
        const depthRange = getWellDepthRange();
        if (numericValue < depthRange.min || numericValue > depthRange.max) {
            $input.addClass('input-error');
            const errorTip = `<div class="error-tip">请输入测深范围内的数值 (${depthRange.min} - ${depthRange.max})</div>`;
            $cell.append(errorTip);
            
            setTimeout(function() {
                $cell.find('.error-tip').fadeOut(300, function() {
                    $(this).remove();
                });
                $input.removeClass('input-error');
            }, 3000);
        }
    });

    // 缓存井深范围，避免重复计算
    let wellDepthRange = null;
    let addlabel_curWellbore = null;

    // 获取井深范围的函数
    function getWellDepthRange() {
        if (!wellDepthRange && addlabel_curWellbore) {
            wellDepthRange = {
                min: 0,
                max: addlabel_curWellbore.Md
            };
        }
        return wellDepthRange;
    }

    // 验证数值格式的辅助函数
    function isValidNumber(value) {
        if (!value) return true;
        
        // 检查是否有多个小数点
        const dotCount = (value.match(/\./g) || []).length;
        if (dotCount > 1) return false;
        
        // 检查是否有多个负号或负号不在开头
        const minusCount = (value.match(/-/g) || []).length;
        if (minusCount > 1 || (minusCount === 1 && value.indexOf('-') !== 0)) return false;
        
        // 检查是否为有效数字格式
        return /^-?\d*\.?\d*$/.test(value);
    }
    
    // Ctrl+Enter键快速添加新行（因为textarea需要回车换行）
    // $(document).on('keydown', '.description-input', function(e) {
    //     if (e.ctrlKey && e.which === 13) { // Ctrl+Enter键
    //         $('#addAnnotationBtn').click();
    //         // 聚焦到新添加行的深度输入框
    //         setTimeout(function() {
    //             $('#annotationTableBody tr:last .depth-input').focus();
    //         }, 100);
    //     }
    // });

    // 处理点击编辑功能
    $(document).on('click', '.depth-text', function() {
        const $cell = $(this).closest('.depth-cell-content');
        $(this).hide();
        $cell.find('.depth-input').show().focus();
    });

    $(document).on('click', '.description-text', function() {
        const $cell = $(this).closest('.description-cell-content');
        $(this).hide();
        $cell.find('.description-input').show().focus();
    });

    // 处理输入完成
    $(document).on('blur', '.depth-input', function() {
        const $cell = $(this).closest('.depth-cell-content');
        const value = $(this).val().trim();
        $cell.find('.depth-text').text(value || '点击编辑').show();
        $(this).hide();
    });

    $(document).on('blur', '.description-input', function() {
        const $cell = $(this).closest('.description-cell-content');
        const value = $(this).val().trim();
        $cell.find('.description-text').text(value || '点击编辑').show();
        $(this).hide();
    });

    // 处理全选功能
    $(document).on('change', '.select-all-checkbox', function() {
        const isChecked = $(this).prop('checked');
        $('.row-checkbox').prop('checked', isChecked);
    });

    // 当单个复选框状态改变时，更新全选框状态
    $(document).on('change', '.row-checkbox', function() {
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;
        
        $('.select-all-checkbox').prop({
            checked: totalCheckboxes === checkedCheckboxes,
            indeterminate: checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes
        });
    });
    // 添加新行时的处理
    function addNewRow() {
        const rowCount = $('#annotationTableBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <label class="checkbox-wrapper">
                        <input type="checkbox" class="row-checkbox">
                    </label>
                </td>
                <td>${rowCount + 1}</td>
                <td class="depth-cell-content">
                    <span class="depth-text">点击编辑</span>
                    <input type="text" placeholder="请输入测深值" class="depth-input" style="display: none;">
                </td>
                <td class="description-cell-content">
                    <span class="description-text">点击编辑</span>
                    <textarea placeholder="请输入标注说明" class="description-input" style="display: none;"></textarea>
                </td>
            </tr>
        `;
        $('#annotationTableBody').append(newRow);
    }

    // 更新现有的添加按钮事件处理
    $(document).off('click', '#addAnnotationBtn').on('click', '#addAnnotationBtn', function() {
        addNewRow();
    });
