<template>
  <el-tabs
    v-model="activeName"
    style="height: auto;overflow-y: auto;"
    type="card"
    class="middle"
  >
    <!-- 输入曲线 -->
    <el-tab-pane :label="$t('processMethod.inputCurveTab')" name="in">
      <el-form>
        <el-form-item
          :label="$t('processMethod.dataset')"
          label-width="120px"
          style=" margin-top: 9px;"
        >
          <el-cascader
            v-model="CurvePackageName"
            :options="cascaderOptions"
            style="width: 100%;"
            :placeholder="$t('processMethod.selectDataset')"
            @change="handleChange"
            :show-all-levels="false"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
            }"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <el-table
        :data="inputList"
        border
        fit
        stripe
        highlight-current-row
        max-height="350"
      >
        <el-table-column
          :label="$t('processMethod.titleColumn')"
          prop="title"
          width="160"
          align="right"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          :label="$t('processMethod.valueColumn')"
          prop="value"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.value"
              placeholder="Please select"
              style="width: 95%;"
            >
              <el-option
                v-for="item in selectableInputCurves"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="display: inline-flex; align-items: center;">
                  <img
                    :src="item.icon"
                    alt="channel icon"
                    style="width: 12px; height: 12px; margin-right: 6px;"
                  />
                  {{ item.label }}
                </span>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>

    <!-- 全局参数 -->
    <el-tab-pane
      v-if="showTabParameter_g"
      :label="$t('processMethod.parameterTab')"
      name="parameter_g"
    >
      <el-form>
        <el-form-item
          style="margin-top: 10px; margin-left: -20px;"
          :label="$t('processMethod.searchParameters')"
          label-width="165px"
        >
          <el-input
            v-model="searchAll"
            :placeholder="$t('processMethod.search')"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-form>
      <parameterList
        :list="parameterData"
        :searchAll="searchAll"
      ></parameterList>
    </el-tab-pane>

    <!-- 输出曲线 -->
    <el-tab-pane :label="$t('processMethod.outputCurveTab')" name="out">
      <el-table
        :data="outputList"
        border
        fit
        highlight-current-row
        max-height="350"
      >
        <el-table-column
          :label="$t('processMethod.titleColumn')"
          width="160"
          prop="title"
          align="right"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          :label="$t('processMethod.valueColumn')"
          width="260"
          prop="value"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.value"
              style="width: 95%;"
              placeholder="Please input a value"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('processMethod.unitColumn')"
          prop="unit"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
      </el-table>
    </el-tab-pane>

    <!-- 设置 -->
    <el-tab-pane :label="$t('processMethod.settingsTab')" name="settings">
      <div class="settings-container">
        <el-form>
          <el-form-item
            :label="$t('processMethod.depthRange')"
            label-width="180px"
            style="margin-top: 5px;"
          >
            <el-input v-model="startIndex" style="width: 22%;" /> -
            <el-input v-model="endIndex" style="width: 22%;" />
            <el-button
              size="mini"
              title="Run"
              @click="reset"
              style="margin-left: 10px;"
              >{{ $t("processMethod.reset") }}</el-button
            >
          </el-form-item>

          <el-form-item
            :label="$t('processMethod.samplingInterval')"
            label-width="180px"
            style="width: 45%;"
          >
            <el-input v-model="samplingInterval" />
          </el-form-item>
        </el-form>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { CoreMethod } from "@/core-method";
import parameterList from "./parameterList";
import {
  List,
  FindByMethodName,
  Update,
  GetDataSet,
  GetChannel,
  CalculateChannel,
} from "@/api/processingModule/index";

export default {
  components: { parameterList },
  props: {
    parameterData: {
      type: Array,
      required: true,
      default: () => [],
    },
    inputList: {
      type: Array,
      required: true,
      default: () => [],
      validator: (value) => Array.isArray(value),
    },
    outputList: {
      type: Array,
      required: true,
      default: () => [],
    },
    settingsData: {
      type: Object,
      default: () => ({
        startIndex: "",
        endIndex: "",
        samplingInterval: ""
      })
    },
    datasetId: {
      type: String,
      default: ""
    },
    wellboreId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      activeName: "in",
      searchAll: "",
      cascaderOptions: [],
      CurvePackageName: "",
      showTabParameter_g: true,
      startIndex: this.settingsData.startIndex || "",
      endIndex: this.settingsData.endIndex || "",
      samplingInterval: this.settingsData.samplingInterval || "",
      selectableInputCurves: [],
      defaultSettings: {
        startIndex: "",
        endIndex: "",
        samplingInterval: "",
      },
    };
  },
  watch: {
    settingsData: {
      handler(newVal) {
        console.log("settingsData 变化:", newVal);
        if (newVal) {
          // 直接使用 settingsData 的值，不再进行条件判断
          this.startIndex = newVal.startIndex || "";
          this.endIndex = newVal.endIndex || "";
          this.samplingInterval = newVal.samplingInterval || "";
        }
      },
      immediate: true,
      deep: true
    },
    inputList: {
      handler(newVal) {
        console.log("methodInfo 接收到新的 inputList:", newVal);
      },
      immediate: true,
      deep: true
    },
    parameterData: {
      handler(newVal) {
        console.log("methodInfo 接收到新的 parameterData:", newVal);
      },
      immediate: true,
      deep: true
    },
    outputList: {
      handler(newVal) {
        console.log("methodInfo 接收到新的 outputList:", newVal);
      },
      immediate: true,
      deep: true
    },
    // 监听数据集ID的变化
    datasetId: {
      handler(newVal) {
        console.log("methodInfo 接收到新的 datasetId:", newVal);
        if (newVal) {
          this.loadDatasetInfo(newVal);
        }
      },
      immediate: true
    },
    // 监听井眼ID的变化
    wellboreId: {
      handler(newVal) {
        console.log("methodInfo 接收到新的 wellboreId:", newVal);
      },
      immediate: true
    },
    startIndex(newVal) {
      this.$emit('settings-changed', {
        startIndex: newVal,
        endIndex: this.endIndex,
        samplingInterval: this.samplingInterval
      });
    },
    endIndex(newVal) {
      this.$emit('settings-changed', {
        startIndex: this.startIndex,
        endIndex: newVal,
        samplingInterval: this.samplingInterval
      });
    },
    samplingInterval(newVal) {
      this.$emit('settings-changed', {
        startIndex: this.startIndex,
        endIndex: this.endIndex,
        samplingInterval: newVal
      });
    }
  },
  computed: {
    filteredInputsList() {
      if (this.CurvePackageName && this.CurvePackageName.length > 0) {
        const lastCurvePackage = this.CurvePackageName[
          this.CurvePackageName.length - 1
        ];
        return this.inputList.filter(
          (item) => item.inputValue === lastCurvePackage
        );
      }
      return this.inputList;
    },
  },
  methods: CoreMethod({
    async fetchData() {
      try {
        const response = await GetDataSet();
        if (response && response.data) {
          this.cascaderOptions = response.data;
        } else {
          this.$message.error("获取数据失败");
        }
      } catch (error) {
        console.error("请求失败:", error);
        this.$message.error("请求数据失败，请稍后再试");
      }
    },
    async handleChange(selectedDataset) {
      this.wellboreId = selectedDataset[0];
      this.datasetId = selectedDataset[selectedDataset.length - 1];

      try {
        const { data: channels } = await GetChannel(this.datasetId);

        this.selectableInputCurves = channels.map((channel) => ({
          label: channel.name,
          value: channel.id,
          icon: "/static/images/channel.png",
        }));

        for (const inputItem of this.inputList) {
          if (inputItem.value) {
            const chNames = inputItem.value.split(";");
            for (const chName of chNames) {
              if (
                this.selectableInputCurves.some((c) => c.label.includes(chName))
              ) {
                inputItem.value = chName;
                break;
              }
            }
            // 设置显示名称
            const channel = this.selectableInputCurves.find(c => c.value === inputItem.value);
            if (channel) {
              inputItem.displayName = channel.label;
            }
          }
        }

        if (channels.length > 0) {
          console.log("channels[0]数据", channels[0]);  
          console.log("channels[0].indexSpacing:", channels[0].indexSpacing, "类型:", typeof channels[0].indexSpacing);

          this.startIndex = channels[0].startIndex;
          this.endIndex = channels[0].endIndex;
          this.samplingInterval = String(channels[0].indexSpacing);
          console.log("设置后的 samplingInterval:", this.samplingInterval, "类型:", typeof this.samplingInterval);

          this.defaultSettings.startIndex = channels[0].startIndex;
          this.defaultSettings.endIndex = channels[0].endIndex;
          this.defaultSettings.samplingInterval = String(channels[0].indexSpacing);
        } else {
          this.startIndex = "";
          this.endIndex = "";
          this.samplingInterval = "";
        }
        const datasetInfo = {
          wellboreId: this.wellboreId,
          datasetId: this.datasetId,
          channels: channels,
          startIndex: this.startIndex,
          endIndex: this.endIndex,
          samplingInterval: this.samplingInterval,
          selectableInputCurves: this.selectableInputCurves,
        };
        this.$emit("dataset-changed", datasetInfo);
      } catch (err) {
        console.log(err);
      }
    },
    reset() {
      this.startIndex = this.defaultSettings.startIndex;
      this.endIndex = this.defaultSettings.endIndex;
      this.samplingInterval = String(this.defaultSettings.samplingInterval);
    },
    // 清除组件数据
    clearComponentData() {
      console.log("清除 methodInfo 组件数据");
      
      // 清除数据集选择器
      this.CurvePackageName = "";
      
      // 清除可选择的输入曲线
      this.selectableInputCurves = [];
      
      // 清除设置数据
      this.startIndex = "";
      this.endIndex = "";
      this.samplingInterval = "";
      
      // 清除默认设置
      this.defaultSettings = {
        startIndex: "",
        endIndex: "",
        samplingInterval: ""
      };
      
      console.log("methodInfo 组件数据已清除");
    },
    // 根据曲线ID获取曲线名称
    getCurveNameById(curveId) {
      if (!curveId || !this.selectableInputCurves || this.selectableInputCurves.length === 0) {
        return curveId || '';
      }
      
      const curve = this.selectableInputCurves.find(c => c.value === curveId);
      return curve ? curve.label : curveId;
    },
    // 加载数据集信息
    async loadDatasetInfo(datasetId) {
      try {
        console.log("开始加载数据集信息:", datasetId);
        const { data: channels } = await GetChannel(datasetId);
        
        this.selectableInputCurves = channels.map((channel) => ({
          label: channel.name,
          value: channel.id,
          icon: "/static/images/channel.png",
        }));
        
        console.log("加载到的曲线列表:", this.selectableInputCurves);
        
        // 根据保存的曲线ID显示曲线名称
        if (this.inputList && this.inputList.length > 0) {
          this.inputList.forEach((inputItem) => {
            if (inputItem.value) {
              // 查找对应的曲线名称
              const channel = this.selectableInputCurves.find(c => c.value === inputItem.value);
              if (channel) {
                console.log(`找到曲线 ${inputItem.value} 对应的名称: ${channel.label}`);
                // 可以在这里设置一个显示名称的属性
                inputItem.displayName = channel.label;
              } else {
                console.log(`未找到曲线 ${inputItem.value} 对应的名称`);
                inputItem.displayName = inputItem.value; // 如果找不到，就显示ID
              }
            }
          });
        }
        
        // 设置数据集选择器
        if (this.wellboreId && datasetId) {
          this.CurvePackageName = [this.wellboreId, datasetId];
          console.log("设置数据集选择器:", this.CurvePackageName);
        }
        
        // 设置默认的深度范围
        if (channels.length > 0) {
          this.startIndex = channels[0].startIndex || "";
          this.endIndex = channels[0].endIndex || "";
          this.samplingInterval = String(channels[0].indexSpacing || "");
          
          this.defaultSettings.startIndex = channels[0].startIndex || "";
          this.defaultSettings.endIndex = channels[0].endIndex || "";
          this.defaultSettings.samplingInterval = String(channels[0].indexSpacing || "");
        }
        
        // 通知父组件数据集已加载
        const datasetInfo = {
          wellboreId: this.wellboreId,
          datasetId: datasetId,
          channels: channels,
          startIndex: this.startIndex,
          endIndex: this.endIndex,
          samplingInterval: this.samplingInterval,
          selectableInputCurves: this.selectableInputCurves,
        };
        this.$emit("dataset-changed", datasetInfo);
        
      } catch (error) {
        console.error("加载数据集信息失败:", error);
      }
    },
  }),
  mounted() {
    this.fetchData();
  },
};
</script>

<style></style> 