var fi={exports:{}},ou=fi.exports,zo;function su(){return zo||(zo=1,function(Go,au){(function(re,wr){Go.exports=wr()})(ou,function(){var re=navigator.userAgent,wr=navigator.platform,We=/gecko\/\d/i.test(re),kn=/MSIE \d/.test(re),Tn=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(re),Sr=/Edge\/(\d+)/.exec(re),A=kn||Tn||Sr,E=A&&(kn?document.documentMode||6:+(Sr||Tn)[1]),ie=!Sr&&/WebKit\//.test(re),Uo=ie&&/Qt\/\d+\.\d+/.test(re),Lr=!Sr&&/Chrome\//.test(re),Ce=/Opera\//.test(re),kr=/Apple Computer/.test(navigator.vendor),Ko=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(re),_o=/PhantomJS/.test(re),zt=kr&&(/Mobile\/\w+/.test(re)||navigator.maxTouchPoints>2),Tr=/Android/.test(re),Gt=zt||Tr||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(re),ye=zt||/Mac/.test(wr),Xo=/\bCrOS\b/.test(re),Yo=/win/i.test(wr),Ve=Ce&&re.match(/Version\/(\d*\.\d*)/);Ve&&(Ve=Number(Ve[1])),Ve&&Ve>=15&&(Ce=!1,ie=!0);var Mn=ye&&(Uo||Ce&&(Ve==null||Ve<12.11)),hi=We||A&&E>=9;function gt(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var $e=function(e,t){var i=e.className,r=gt(t).exec(i);if(r){var n=i.slice(r.index+r[0].length);e.className=i.slice(0,r.index)+(n?r[1]+n:"")}};function ze(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function pe(e,t){return ze(e).appendChild(t)}function k(e,t,i,r){var n=document.createElement(e);if(i&&(n.className=i),r&&(n.style.cssText=r),typeof t=="string")n.appendChild(document.createTextNode(t));else if(t)for(var l=0;l<t.length;++l)n.appendChild(t[l]);return n}function yt(e,t,i,r){var n=k(e,t,i,r);return n.setAttribute("role","presentation"),n}var et;document.createRange?et=function(e,t,i,r){var n=document.createRange();return n.setEnd(r||e,i),n.setStart(e,t),n}:et=function(e,t,i){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch{return r}return r.collapse(!0),r.moveEnd("character",i),r.moveStart("character",t),r};function Ge(e,t){if(t.nodeType==3&&(t=t.parentNode),e.contains)return e.contains(t);do if(t.nodeType==11&&(t=t.host),t==e)return!0;while(t=t.parentNode)}function me(){var e;try{e=document.activeElement}catch{e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function tt(e,t){var i=e.className;gt(t).test(i)||(e.className+=(i?" ":"")+t)}function ci(e,t){for(var i=e.split(" "),r=0;r<i.length;r++)i[r]&&!gt(i[r]).test(t)&&(t+=" "+i[r]);return t}var Ut=function(e){e.select()};zt?Ut=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:A&&(Ut=function(e){try{e.select()}catch{}});function di(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function rt(e,t,i){t||(t={});for(var r in e)e.hasOwnProperty(r)&&(i!==!1||!t.hasOwnProperty(r))&&(t[r]=e[r]);return t}function be(e,t,i,r,n){t==null&&(t=e.search(/[^\s\u00a0]/),t==-1&&(t=e.length));for(var l=r||0,o=n||0;;){var a=e.indexOf("	",l);if(a<0||a>=t)return o+(t-l);o+=a-l,o+=i-o%i,l=a+1}}var Ue=function(){this.id=null,this.f=null,this.time=0,this.handler=di(this.onTimeout,this)};Ue.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},Ue.prototype.set=function(e,t){this.f=t;var i=+new Date+e;(!this.id||i<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=i)};function $(e,t){for(var i=0;i<e.length;++i)if(e[i]==t)return i;return-1}var Dn=50,Mr={toString:function(){return"CodeMirror.Pass"}},ke={scroll:!1},pi={origin:"*mouse"},Kt={origin:"+move"};function vi(e,t,i){for(var r=0,n=0;;){var l=e.indexOf("	",r);l==-1&&(l=e.length);var o=l-r;if(l==e.length||n+o>=t)return r+Math.min(o,t-n);if(n+=l-r,n+=i-n%i,r=l+1,n>=t)return r}}var Dr=[""];function gi(e){for(;Dr.length<=e;)Dr.push(W(Dr)+" ");return Dr[e]}function W(e){return e[e.length-1]}function Nr(e,t){for(var i=[],r=0;r<e.length;r++)i[r]=t(e[r],r);return i}function qo(e,t,i){for(var r=0,n=i(t);r<e.length&&i(e[r])<=n;)r++;e.splice(r,0,t)}function Nn(){}function An(e,t){var i;return Object.create?i=Object.create(e):(Nn.prototype=e,i=new Nn),t&&rt(t,i),i}var Zo=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function yi(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Zo.test(e))}function Ar(e,t){return t?t.source.indexOf("\\w")>-1&&yi(e)?!0:t.test(e):yi(e)}function On(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var Qo=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function mi(e){return e.charCodeAt(0)>=768&&Qo.test(e)}function Wn(e,t,i){for(;(i<0?t>0:t<e.length)&&mi(e.charAt(t));)t+=i;return t}function _t(e,t,i){for(var r=t>i?-1:1;;){if(t==i)return t;var n=(t+i)/2,l=r<0?Math.ceil(n):Math.floor(n);if(l==t)return e(l)?t:i;e(l)?i=l:t=l+r}}function Jo(e,t,i,r){if(!e)return r(t,i,"ltr",0);for(var n=!1,l=0;l<e.length;++l){var o=e[l];(o.from<i&&o.to>t||t==i&&o.to==t)&&(r(Math.max(o.from,t),Math.min(o.to,i),o.level==1?"rtl":"ltr",l),n=!0)}n||r(t,i,"ltr")}var Xt=null;function Yt(e,t,i){var r;Xt=null;for(var n=0;n<e.length;++n){var l=e[n];if(l.from<t&&l.to>t)return n;l.to==t&&(l.from!=l.to&&i=="before"?r=n:Xt=n),l.from==t&&(l.from!=l.to&&i!="before"?r=n:Xt=n)}return r??Xt}var jo=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function i(u){return u<=247?e.charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?t.charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":u==8204?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,n=/[stwN]/,l=/[LRr]/,o=/[Lb1n]/,a=/[1n]/;function s(u,f,h){this.level=u,this.from=f,this.to=h}return function(u,f){var h=f=="ltr"?"L":"R";if(u.length==0||f=="ltr"&&!r.test(u))return!1;for(var d=u.length,c=[],p=0;p<d;++p)c.push(i(u.charCodeAt(p)));for(var v=0,y=h;v<d;++v){var m=c[v];m=="m"?c[v]=y:y=m}for(var x=0,b=h;x<d;++x){var C=c[x];C=="1"&&b=="r"?c[x]="n":l.test(C)&&(b=C,C=="r"&&(c[x]="R"))}for(var L=1,S=c[0];L<d-1;++L){var N=c[L];N=="+"&&S=="1"&&c[L+1]=="1"?c[L]="1":N==","&&S==c[L+1]&&(S=="1"||S=="n")&&(c[L]=S),S=N}for(var P=0;P<d;++P){var J=c[P];if(J==",")c[P]="N";else if(J=="%"){var R=void 0;for(R=P+1;R<d&&c[R]=="%";++R);for(var de=P&&c[P-1]=="!"||R<d&&c[R]=="1"?"1":"N",ue=P;ue<R;++ue)c[ue]=de;P=R-1}}for(var K=0,fe=h;K<d;++K){var V=c[K];fe=="L"&&V=="1"?c[K]="L":l.test(V)&&(fe=V)}for(var X=0;X<d;++X)if(n.test(c[X])){var _=void 0;for(_=X+1;_<d&&n.test(c[_]);++_);for(var B=(X?c[X-1]:h)=="L",he=(_<d?c[_]:h)=="L",Rt=B==he?B?"L":"R":h,je=X;je<_;++je)c[je]=Rt;X=_-1}for(var te=[],Oe,j=0;j<d;)if(o.test(c[j])){var Sn=j;for(++j;j<d&&o.test(c[j]);++j);te.push(new s(0,Sn,j))}else{var Be=j,pt=te.length,vt=f=="rtl"?1:0;for(++j;j<d&&c[j]!="L";++j);for(var le=Be;le<j;)if(a.test(c[le])){Be<le&&(te.splice(pt,0,new s(1,Be,le)),pt+=vt);var Bt=le;for(++le;le<j&&a.test(c[le]);++le);te.splice(pt,0,new s(2,Bt,le)),pt+=vt,Be=le}else++le;Be<j&&te.splice(pt,0,new s(1,Be,j))}return f=="ltr"&&(te[0].level==1&&(Oe=u.match(/^\s+/))&&(te[0].from=Oe[0].length,te.unshift(new s(0,0,Oe[0].length))),W(te).level==1&&(Oe=u.match(/\s+$/))&&(W(te).to-=Oe[0].length,te.push(new s(0,d-Oe[0].length,d)))),f=="rtl"?te.reverse():te}}();function He(e,t){var i=e.order;return i==null&&(i=e.order=jo(e.text,t)),i}var Hn=[],T=function(e,t,i){if(e.addEventListener)e.addEventListener(t,i,!1);else if(e.attachEvent)e.attachEvent("on"+t,i);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||Hn).concat(i)}};function bi(e,t){return e._handlers&&e._handlers[t]||Hn}function ve(e,t,i){if(e.removeEventListener)e.removeEventListener(t,i,!1);else if(e.detachEvent)e.detachEvent("on"+t,i);else{var r=e._handlers,n=r&&r[t];if(n){var l=$(n,i);l>-1&&(r[t]=n.slice(0,l).concat(n.slice(l+1)))}}}function G(e,t){var i=bi(e,t);if(i.length)for(var r=Array.prototype.slice.call(arguments,2),n=0;n<i.length;++n)i[n].apply(null,r)}function Y(e,t,i){return typeof t=="string"&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),G(e,i||t.type,e,t),xi(t)||t.codemirrorIgnore}function Fn(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var i=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)$(i,t[r])==-1&&i.push(t[r])}function xe(e,t){return bi(e,t).length>0}function mt(e){e.prototype.on=function(t,i){T(this,t,i)},e.prototype.off=function(t,i){ve(this,t,i)}}function oe(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Pn(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function xi(e){return e.defaultPrevented!=null?e.defaultPrevented:e.returnValue==!1}function qt(e){oe(e),Pn(e)}function Ci(e){return e.target||e.srcElement}function En(e){var t=e.which;return t==null&&(e.button&1?t=1:e.button&2?t=3:e.button&4&&(t=2)),ye&&e.ctrlKey&&t==1&&(t=3),t}var Vo=function(){if(A&&E<9)return!1;var e=k("div");return"draggable"in e||"dragDrop"in e}(),wi;function $o(e){if(wi==null){var t=k("span","​");pe(e,k("span",[t,document.createTextNode("x")])),e.firstChild.offsetHeight!=0&&(wi=t.offsetWidth<=1&&t.offsetHeight>2&&!(A&&E<8))}var i=wi?k("span","​"):k("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return i.setAttribute("cm-text",""),i}var Si;function ea(e){if(Si!=null)return Si;var t=pe(e,document.createTextNode("AخA")),i=et(t,0,1).getBoundingClientRect(),r=et(t,1,2).getBoundingClientRect();return ze(e),!i||i.left==i.right?!1:Si=r.right-i.right<3}var Li=`

b`.split(/\n/).length!=3?function(e){for(var t=0,i=[],r=e.length;t<=r;){var n=e.indexOf(`
`,t);n==-1&&(n=e.length);var l=e.slice(t,e.charAt(n-1)=="\r"?n-1:n),o=l.indexOf("\r");o!=-1?(i.push(l.slice(0,o)),t+=o+1):(i.push(l),t=n+1)}return i}:function(e){return e.split(/\r\n?|\n/)},ta=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch{return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch{}return!t||t.parentElement()!=e?!1:t.compareEndPoints("StartToEnd",t)!=0},ra=function(){var e=k("div");return"oncopy"in e?!0:(e.setAttribute("oncopy","return;"),typeof e.oncopy=="function")}(),ki=null;function ia(e){if(ki!=null)return ki;var t=pe(e,k("span","x")),i=t.getBoundingClientRect(),r=et(t,0,1).getBoundingClientRect();return ki=Math.abs(i.left-r.left)>1}var Ti={},bt={};function na(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ti[e]=t}function la(e,t){bt[e]=t}function Or(e){if(typeof e=="string"&&bt.hasOwnProperty(e))e=bt[e];else if(e&&typeof e.name=="string"&&bt.hasOwnProperty(e.name)){var t=bt[e.name];typeof t=="string"&&(t={name:t}),e=An(t,e),e.name=t.name}else{if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Or("application/xml");if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Or("application/json")}return typeof e=="string"?{name:e}:e||{name:"null"}}function Mi(e,t){t=Or(t);var i=Ti[t.name];if(!i)return Mi(e,"text/plain");var r=i(e,t);if(xt.hasOwnProperty(t.name)){var n=xt[t.name];for(var l in n)n.hasOwnProperty(l)&&(r.hasOwnProperty(l)&&(r["_"+l]=r[l]),r[l]=n[l])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var o in t.modeProps)r[o]=t.modeProps[o];return r}var xt={};function oa(e,t){var i=xt.hasOwnProperty(e)?xt[e]:xt[e]={};rt(t,i)}function it(e,t){if(t===!0)return t;if(e.copyState)return e.copyState(t);var i={};for(var r in t){var n=t[r];n instanceof Array&&(n=n.concat([])),i[r]=n}return i}function Di(e,t){for(var i;e.innerMode&&(i=e.innerMode(t),!(!i||i.mode==e));)t=i.state,e=i.mode;return i||{mode:e,state:t}}function In(e,t,i){return e.startState?e.startState(t,i):!0}var U=function(e,t,i){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=i};U.prototype.eol=function(){return this.pos>=this.string.length},U.prototype.sol=function(){return this.pos==this.lineStart},U.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},U.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},U.prototype.eat=function(e){var t=this.string.charAt(this.pos),i;if(typeof e=="string"?i=t==e:i=t&&(e.test?e.test(t):e(t)),i)return++this.pos,t},U.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},U.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},U.prototype.skipToEnd=function(){this.pos=this.string.length},U.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},U.prototype.backUp=function(e){this.pos-=e},U.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=be(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?be(this.string,this.lineStart,this.tabSize):0)},U.prototype.indentation=function(){return be(this.string,null,this.tabSize)-(this.lineStart?be(this.string,this.lineStart,this.tabSize):0)},U.prototype.match=function(e,t,i){if(typeof e=="string"){var r=function(o){return i?o.toLowerCase():o},n=this.string.substr(this.pos,e.length);if(r(n)==r(e))return t!==!1&&(this.pos+=e.length),!0}else{var l=this.string.slice(this.pos).match(e);return l&&l.index>0?null:(l&&t!==!1&&(this.pos+=l[0].length),l)}},U.prototype.current=function(){return this.string.slice(this.start,this.pos)},U.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},U.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},U.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};function w(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var i=e;!i.lines;)for(var r=0;;++r){var n=i.children[r],l=n.chunkSize();if(t<l){i=n;break}t-=l}return i.lines[t]}function nt(e,t,i){var r=[],n=t.line;return e.iter(t.line,i.line+1,function(l){var o=l.text;n==i.line&&(o=o.slice(0,i.ch)),n==t.line&&(o=o.slice(t.ch)),r.push(o),++n}),r}function Ni(e,t,i){var r=[];return e.iter(t,i,function(n){r.push(n.text)}),r}function Te(e,t){var i=t-e.height;if(i)for(var r=e;r;r=r.parent)r.height+=i}function H(e){if(e.parent==null)return null;for(var t=e.parent,i=$(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var n=0;r.children[n]!=t;++n)i+=r.children[n].chunkSize();return i+t.first}function lt(e,t){var i=e.first;e:do{for(var r=0;r<e.children.length;++r){var n=e.children[r],l=n.height;if(t<l){e=n;continue e}t-=l,i+=n.chunkSize()}return i}while(!e.lines);for(var o=0;o<e.lines.length;++o){var a=e.lines[o],s=a.height;if(t<s)break;t-=s}return i+o}function Zt(e,t){return t>=e.first&&t<e.first+e.size}function Ai(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function g(e,t,i){if(i===void 0&&(i=null),!(this instanceof g))return new g(e,t,i);this.line=e,this.ch=t,this.sticky=i}function M(e,t){return e.line-t.line||e.ch-t.ch}function Oi(e,t){return e.sticky==t.sticky&&M(e,t)==0}function Wi(e){return g(e.line,e.ch)}function Wr(e,t){return M(e,t)<0?t:e}function Hr(e,t){return M(e,t)<0?e:t}function Rn(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function D(e,t){if(t.line<e.first)return g(e.first,0);var i=e.first+e.size-1;return t.line>i?g(i,w(e,i).text.length):aa(t,w(e,t.line).text.length)}function aa(e,t){var i=e.ch;return i==null||i>t?g(e.line,t):i<0?g(e.line,0):e}function Bn(e,t){for(var i=[],r=0;r<t.length;r++)i[r]=D(e,t[r]);return i}var Fr=function(e,t){this.state=e,this.lookAhead=t},Me=function(e,t,i,r){this.state=t,this.doc=e,this.line=i,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};Me.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return t!=null&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},Me.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Me.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Me.fromSaved=function(e,t,i){return t instanceof Fr?new Me(e,it(e.mode,t.state),i,t.lookAhead):new Me(e,it(e.mode,t),i)},Me.prototype.save=function(e){var t=e!==!1?it(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new Fr(t,this.maxLookAhead):t};function zn(e,t,i,r){var n=[e.state.modeGen],l={};Yn(e,t.text,e.doc.mode,i,function(u,f){return n.push(u,f)},l,r);for(var o=i.state,a=function(u){i.baseTokens=n;var f=e.state.overlays[u],h=1,d=0;i.state=!0,Yn(e,t.text,f.mode,i,function(c,p){for(var v=h;d<c;){var y=n[h];y>c&&n.splice(h,1,c,n[h+1],y),h+=2,d=Math.min(c,y)}if(p)if(f.opaque)n.splice(v,h-v,c,"overlay "+p),h=v+2;else for(;v<h;v+=2){var m=n[v+1];n[v+1]=(m?m+" ":"")+"overlay "+p}},l),i.state=o,i.baseTokens=null,i.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:n,classes:l.bgClass||l.textClass?l:null}}function Gn(e,t,i){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=Qt(e,H(t)),n=t.text.length>e.options.maxHighlightLength&&it(e.doc.mode,r.state),l=zn(e,t,r);n&&(r.state=n),t.stateAfter=r.save(!n),t.styles=l.styles,l.classes?t.styleClasses=l.classes:t.styleClasses&&(t.styleClasses=null),i===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Qt(e,t,i){var r=e.doc,n=e.display;if(!r.mode.startState)return new Me(r,!0,t);var l=sa(e,t,i),o=l>r.first&&w(r,l-1).stateAfter,a=o?Me.fromSaved(r,o,l):new Me(r,In(r.mode),l);return r.iter(l,t,function(s){Hi(e,s.text,a);var u=a.line;s.stateAfter=u==t-1||u%5==0||u>=n.viewFrom&&u<n.viewTo?a.save():null,a.nextLine()}),i&&(r.modeFrontier=a.line),a}function Hi(e,t,i,r){var n=e.doc.mode,l=new U(t,e.options.tabSize,i);for(l.start=l.pos=r||0,t==""&&Un(n,i.state);!l.eol();)Fi(n,l,i.state),l.start=l.pos}function Un(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var i=Di(e,t);if(i.mode.blankLine)return i.mode.blankLine(i.state)}}function Fi(e,t,i,r){for(var n=0;n<10;n++){r&&(r[0]=Di(e,i).mode);var l=e.token(t,i);if(t.pos>t.start)return l}throw new Error("Mode "+e.name+" failed to advance stream.")}var Kn=function(e,t,i){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=i};function _n(e,t,i,r){var n=e.doc,l=n.mode,o;t=D(n,t);var a=w(n,t.line),s=Qt(e,t.line,i),u=new U(a.text,e.options.tabSize,s),f;for(r&&(f=[]);(r||u.pos<t.ch)&&!u.eol();)u.start=u.pos,o=Fi(l,u,s.state),r&&f.push(new Kn(u,o,it(n.mode,s.state)));return r?f:new Kn(u,o,s.state)}function Xn(e,t){if(e)for(;;){var i=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!i)break;e=e.slice(0,i.index)+e.slice(i.index+i[0].length);var r=i[1]?"bgClass":"textClass";t[r]==null?t[r]=i[2]:new RegExp("(?:^|\\s)"+i[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+i[2])}return e}function Yn(e,t,i,r,n,l,o){var a=i.flattenSpans;a==null&&(a=e.options.flattenSpans);var s=0,u=null,f=new U(t,e.options.tabSize,r),h,d=e.options.addModeClass&&[null];for(t==""&&Xn(Un(i,r.state),l);!f.eol();){if(f.pos>e.options.maxHighlightLength?(a=!1,o&&Hi(e,t,r,f.pos),f.pos=t.length,h=null):h=Xn(Fi(i,f,r.state,d),l),d){var c=d[0].name;c&&(h="m-"+(h?c+" "+h:c))}if(!a||u!=h){for(;s<f.start;)s=Math.min(f.start,s+5e3),n(s,u);u=h}f.start=f.pos}for(;s<f.pos;){var p=Math.min(f.pos,s+5e3);n(p,u),s=p}}function sa(e,t,i){for(var r,n,l=e.doc,o=i?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>o;--a){if(a<=l.first)return l.first;var s=w(l,a-1),u=s.stateAfter;if(u&&(!i||a+(u instanceof Fr?u.lookAhead:0)<=l.modeFrontier))return a;var f=be(s.text,null,e.options.tabSize);(n==null||r>f)&&(n=a-1,r=f)}return n}function ua(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var i=e.first,r=t-1;r>i;r--){var n=w(e,r).stateAfter;if(n&&(!(n instanceof Fr)||r+n.lookAhead<t)){i=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,i)}}var qn=!1,Fe=!1;function fa(){qn=!0}function ha(){Fe=!0}function Pr(e,t,i){this.marker=e,this.from=t,this.to=i}function Jt(e,t){if(e)for(var i=0;i<e.length;++i){var r=e[i];if(r.marker==t)return r}}function ca(e,t){for(var i,r=0;r<e.length;++r)e[r]!=t&&(i||(i=[])).push(e[r]);return i}function da(e,t,i){var r=i&&window.WeakSet&&(i.markedSpans||(i.markedSpans=new WeakSet));r&&r.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],r&&r.add(e.markedSpans)),t.marker.attachLine(e)}function pa(e,t,i){var r;if(e)for(var n=0;n<e.length;++n){var l=e[n],o=l.marker,a=l.from==null||(o.inclusiveLeft?l.from<=t:l.from<t);if(a||l.from==t&&o.type=="bookmark"&&(!i||!l.marker.insertLeft)){var s=l.to==null||(o.inclusiveRight?l.to>=t:l.to>t);(r||(r=[])).push(new Pr(o,l.from,s?null:l.to))}}return r}function va(e,t,i){var r;if(e)for(var n=0;n<e.length;++n){var l=e[n],o=l.marker,a=l.to==null||(o.inclusiveRight?l.to>=t:l.to>t);if(a||l.from==t&&o.type=="bookmark"&&(!i||l.marker.insertLeft)){var s=l.from==null||(o.inclusiveLeft?l.from<=t:l.from<t);(r||(r=[])).push(new Pr(o,s?null:l.from-t,l.to==null?null:l.to-t))}}return r}function Pi(e,t){if(t.full)return null;var i=Zt(e,t.from.line)&&w(e,t.from.line).markedSpans,r=Zt(e,t.to.line)&&w(e,t.to.line).markedSpans;if(!i&&!r)return null;var n=t.from.ch,l=t.to.ch,o=M(t.from,t.to)==0,a=pa(i,n,o),s=va(r,l,o),u=t.text.length==1,f=W(t.text).length+(u?n:0);if(a)for(var h=0;h<a.length;++h){var d=a[h];if(d.to==null){var c=Jt(s,d.marker);c?u&&(d.to=c.to==null?null:c.to+f):d.to=n}}if(s)for(var p=0;p<s.length;++p){var v=s[p];if(v.to!=null&&(v.to+=f),v.from==null){var y=Jt(a,v.marker);y||(v.from=f,u&&(a||(a=[])).push(v))}else v.from+=f,u&&(a||(a=[])).push(v)}a&&(a=Zn(a)),s&&s!=a&&(s=Zn(s));var m=[a];if(!u){var x=t.text.length-2,b;if(x>0&&a)for(var C=0;C<a.length;++C)a[C].to==null&&(b||(b=[])).push(new Pr(a[C].marker,null,null));for(var L=0;L<x;++L)m.push(b);m.push(s)}return m}function Zn(e){for(var t=0;t<e.length;++t){var i=e[t];i.from!=null&&i.from==i.to&&i.marker.clearWhenEmpty!==!1&&e.splice(t--,1)}return e.length?e:null}function ga(e,t,i){var r=null;if(e.iter(t.line,i.line+1,function(c){if(c.markedSpans)for(var p=0;p<c.markedSpans.length;++p){var v=c.markedSpans[p].marker;v.readOnly&&(!r||$(r,v)==-1)&&(r||(r=[])).push(v)}}),!r)return null;for(var n=[{from:t,to:i}],l=0;l<r.length;++l)for(var o=r[l],a=o.find(0),s=0;s<n.length;++s){var u=n[s];if(!(M(u.to,a.from)<0||M(u.from,a.to)>0)){var f=[s,1],h=M(u.from,a.from),d=M(u.to,a.to);(h<0||!o.inclusiveLeft&&!h)&&f.push({from:u.from,to:a.from}),(d>0||!o.inclusiveRight&&!d)&&f.push({from:a.to,to:u.to}),n.splice.apply(n,f),s+=f.length-3}}return n}function Qn(e){var t=e.markedSpans;if(t){for(var i=0;i<t.length;++i)t[i].marker.detachLine(e);e.markedSpans=null}}function Jn(e,t){if(t){for(var i=0;i<t.length;++i)t[i].marker.attachLine(e);e.markedSpans=t}}function Er(e){return e.inclusiveLeft?-1:0}function Ir(e){return e.inclusiveRight?1:0}function Ei(e,t){var i=e.lines.length-t.lines.length;if(i!=0)return i;var r=e.find(),n=t.find(),l=M(r.from,n.from)||Er(e)-Er(t);if(l)return-l;var o=M(r.to,n.to)||Ir(e)-Ir(t);return o||t.id-e.id}function jn(e,t){var i=Fe&&e.markedSpans,r;if(i)for(var n=void 0,l=0;l<i.length;++l)n=i[l],n.marker.collapsed&&(t?n.from:n.to)==null&&(!r||Ei(r,n.marker)<0)&&(r=n.marker);return r}function Vn(e){return jn(e,!0)}function Rr(e){return jn(e,!1)}function ya(e,t){var i=Fe&&e.markedSpans,r;if(i)for(var n=0;n<i.length;++n){var l=i[n];l.marker.collapsed&&(l.from==null||l.from<t)&&(l.to==null||l.to>t)&&(!r||Ei(r,l.marker)<0)&&(r=l.marker)}return r}function $n(e,t,i,r,n){var l=w(e,t),o=Fe&&l.markedSpans;if(o)for(var a=0;a<o.length;++a){var s=o[a];if(s.marker.collapsed){var u=s.marker.find(0),f=M(u.from,i)||Er(s.marker)-Er(n),h=M(u.to,r)||Ir(s.marker)-Ir(n);if(!(f>=0&&h<=0||f<=0&&h>=0)&&(f<=0&&(s.marker.inclusiveRight&&n.inclusiveLeft?M(u.to,i)>=0:M(u.to,i)>0)||f>=0&&(s.marker.inclusiveRight&&n.inclusiveLeft?M(u.from,r)<=0:M(u.from,r)<0)))return!0}}}function De(e){for(var t;t=Vn(e);)e=t.find(-1,!0).line;return e}function ma(e){for(var t;t=Rr(e);)e=t.find(1,!0).line;return e}function ba(e){for(var t,i;t=Rr(e);)e=t.find(1,!0).line,(i||(i=[])).push(e);return i}function Ii(e,t){var i=w(e,t),r=De(i);return i==r?t:H(r)}function el(e,t){if(t>e.lastLine())return t;var i=w(e,t),r;if(!Ke(e,i))return t;for(;r=Rr(i);)i=r.find(1,!0).line;return H(i)+1}function Ke(e,t){var i=Fe&&t.markedSpans;if(i){for(var r=void 0,n=0;n<i.length;++n)if(r=i[n],!!r.marker.collapsed){if(r.from==null)return!0;if(!r.marker.widgetNode&&r.from==0&&r.marker.inclusiveLeft&&Ri(e,t,r))return!0}}}function Ri(e,t,i){if(i.to==null){var r=i.marker.find(1,!0);return Ri(e,r.line,Jt(r.line.markedSpans,i.marker))}if(i.marker.inclusiveRight&&i.to==t.text.length)return!0;for(var n=void 0,l=0;l<t.markedSpans.length;++l)if(n=t.markedSpans[l],n.marker.collapsed&&!n.marker.widgetNode&&n.from==i.to&&(n.to==null||n.to!=i.from)&&(n.marker.inclusiveLeft||i.marker.inclusiveRight)&&Ri(e,t,n))return!0}function Pe(e){e=De(e);for(var t=0,i=e.parent,r=0;r<i.lines.length;++r){var n=i.lines[r];if(n==e)break;t+=n.height}for(var l=i.parent;l;i=l,l=i.parent)for(var o=0;o<l.children.length;++o){var a=l.children[o];if(a==i)break;t+=a.height}return t}function Br(e){if(e.height==0)return 0;for(var t=e.text.length,i,r=e;i=Vn(r);){var n=i.find(0,!0);r=n.from.line,t+=n.from.ch-n.to.ch}for(r=e;i=Rr(r);){var l=i.find(0,!0);t-=r.text.length-l.from.ch,r=l.to.line,t+=r.text.length-l.to.ch}return t}function Bi(e){var t=e.display,i=e.doc;t.maxLine=w(i,i.first),t.maxLineLength=Br(t.maxLine),t.maxLineChanged=!0,i.iter(function(r){var n=Br(r);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=r)})}var Ct=function(e,t,i){this.text=e,Jn(this,t),this.height=i?i(this):1};Ct.prototype.lineNo=function(){return H(this)},mt(Ct);function xa(e,t,i,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),e.order!=null&&(e.order=null),Qn(e),Jn(e,i);var n=r?r(e):1;n!=e.height&&Te(e,n)}function Ca(e){e.parent=null,Qn(e)}var wa={},Sa={};function tl(e,t){if(!e||/^\s*$/.test(e))return null;var i=t.addModeClass?Sa:wa;return i[e]||(i[e]=e.replace(/\S+/g,"cm-$&"))}function rl(e,t){var i=yt("span",null,null,ie?"padding-right: .1px":null),r={pre:yt("pre",[i],"CodeMirror-line"),content:i,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var n=0;n<=(t.rest?t.rest.length:0);n++){var l=n?t.rest[n-1]:t.line,o=void 0;r.pos=0,r.addToken=ka,ea(e.display.measure)&&(o=He(l,e.doc.direction))&&(r.addToken=Ma(r.addToken,o)),r.map=[];var a=t!=e.display.externalMeasured&&H(l);Da(l,r,Gn(e,l,a)),l.styleClasses&&(l.styleClasses.bgClass&&(r.bgClass=ci(l.styleClasses.bgClass,r.bgClass||"")),l.styleClasses.textClass&&(r.textClass=ci(l.styleClasses.textClass,r.textClass||""))),r.map.length==0&&r.map.push(0,0,r.content.appendChild($o(e.display.measure))),n==0?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(ie){var s=r.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return G(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=ci(r.pre.className,r.textClass||"")),r}function La(e){var t=k("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function ka(e,t,i,r,n,l,o){if(t){var a=e.splitSpaces?Ta(t,e.trailingSpace):t,s=e.cm.state.specialChars,u=!1,f;if(!s.test(t))e.col+=t.length,f=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,f),A&&E<9&&(u=!0),e.pos+=t.length;else{f=document.createDocumentFragment();for(var h=0;;){s.lastIndex=h;var d=s.exec(t),c=d?d.index-h:t.length-h;if(c){var p=document.createTextNode(a.slice(h,h+c));A&&E<9?f.appendChild(k("span",[p])):f.appendChild(p),e.map.push(e.pos,e.pos+c,p),e.col+=c,e.pos+=c}if(!d)break;h+=c+1;var v=void 0;if(d[0]=="	"){var y=e.cm.options.tabSize,m=y-e.col%y;v=f.appendChild(k("span",gi(m),"cm-tab")),v.setAttribute("role","presentation"),v.setAttribute("cm-text","	"),e.col+=m}else d[0]=="\r"||d[0]==`
`?(v=f.appendChild(k("span",d[0]=="\r"?"␍":"␤","cm-invalidchar")),v.setAttribute("cm-text",d[0]),e.col+=1):(v=e.cm.options.specialCharPlaceholder(d[0]),v.setAttribute("cm-text",d[0]),A&&E<9?f.appendChild(k("span",[v])):f.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}if(e.trailingSpace=a.charCodeAt(t.length-1)==32,i||r||n||u||l||o){var x=i||"";r&&(x+=r),n&&(x+=n);var b=k("span",[f],x,l);if(o)for(var C in o)o.hasOwnProperty(C)&&C!="style"&&C!="class"&&b.setAttribute(C,o[C]);return e.content.appendChild(b)}e.content.appendChild(f)}}function Ta(e,t){if(e.length>1&&!/  /.test(e))return e;for(var i=t,r="",n=0;n<e.length;n++){var l=e.charAt(n);l==" "&&i&&(n==e.length-1||e.charCodeAt(n+1)==32)&&(l=" "),r+=l,i=l==" "}return r}function Ma(e,t){return function(i,r,n,l,o,a,s){n=n?n+" cm-force-border":"cm-force-border";for(var u=i.pos,f=u+r.length;;){for(var h=void 0,d=0;d<t.length&&(h=t[d],!(h.to>u&&h.from<=u));d++);if(h.to>=f)return e(i,r,n,l,o,a,s);e(i,r.slice(0,h.to-u),n,l,null,a,s),l=null,r=r.slice(h.to-u),u=h.to}}}function il(e,t,i,r){var n=!r&&i.widgetNode;n&&e.map.push(e.pos,e.pos+t,n),!r&&e.cm.display.input.needsContentAttribute&&(n||(n=e.content.appendChild(document.createElement("span"))),n.setAttribute("cm-marker",i.id)),n&&(e.cm.display.input.setUneditable(n),e.content.appendChild(n)),e.pos+=t,e.trailingSpace=!1}function Da(e,t,i){var r=e.markedSpans,n=e.text,l=0;if(!r){for(var o=1;o<i.length;o+=2)t.addToken(t,n.slice(l,l=i[o]),tl(i[o+1],t.cm.options));return}for(var a=n.length,s=0,u=1,f="",h,d,c=0,p,v,y,m,x;;){if(c==s){p=v=y=d="",x=null,m=null,c=1/0;for(var b=[],C=void 0,L=0;L<r.length;++L){var S=r[L],N=S.marker;if(N.type=="bookmark"&&S.from==s&&N.widgetNode)b.push(N);else if(S.from<=s&&(S.to==null||S.to>s||N.collapsed&&S.to==s&&S.from==s)){if(S.to!=null&&S.to!=s&&c>S.to&&(c=S.to,v=""),N.className&&(p+=" "+N.className),N.css&&(d=(d?d+";":"")+N.css),N.startStyle&&S.from==s&&(y+=" "+N.startStyle),N.endStyle&&S.to==c&&(C||(C=[])).push(N.endStyle,S.to),N.title&&((x||(x={})).title=N.title),N.attributes)for(var P in N.attributes)(x||(x={}))[P]=N.attributes[P];N.collapsed&&(!m||Ei(m.marker,N)<0)&&(m=S)}else S.from>s&&c>S.from&&(c=S.from)}if(C)for(var J=0;J<C.length;J+=2)C[J+1]==c&&(v+=" "+C[J]);if(!m||m.from==s)for(var R=0;R<b.length;++R)il(t,0,b[R]);if(m&&(m.from||0)==s){if(il(t,(m.to==null?a+1:m.to)-s,m.marker,m.from==null),m.to==null)return;m.to==s&&(m=!1)}}if(s>=a)break;for(var de=Math.min(a,c);;){if(f){var ue=s+f.length;if(!m){var K=ue>de?f.slice(0,de-s):f;t.addToken(t,K,h?h+p:p,y,s+K.length==c?v:"",d,x)}if(ue>=de){f=f.slice(de-s),s=de;break}s=ue,y=""}f=n.slice(l,l=i[u++]),h=tl(i[u++],t.cm.options)}}}function nl(e,t,i){this.line=t,this.rest=ba(t),this.size=this.rest?H(W(this.rest))-i+1:1,this.node=this.text=null,this.hidden=Ke(e,t)}function zr(e,t,i){for(var r=[],n,l=t;l<i;l=n){var o=new nl(e.doc,w(e.doc,l),l);n=l+o.size,r.push(o)}return r}var wt=null;function Na(e){wt?wt.ops.push(e):e.ownsGroup=wt={ops:[e],delayedCallbacks:[]}}function Aa(e){var t=e.delayedCallbacks,i=0;do{for(;i<t.length;i++)t[i].call(null);for(var r=0;r<e.ops.length;r++){var n=e.ops[r];if(n.cursorActivityHandlers)for(;n.cursorActivityCalled<n.cursorActivityHandlers.length;)n.cursorActivityHandlers[n.cursorActivityCalled++].call(null,n.cm)}}while(i<t.length)}function Oa(e,t){var i=e.ownsGroup;if(i)try{Aa(i)}finally{wt=null,t(i)}}var jt=null;function q(e,t){var i=bi(e,t);if(i.length){var r=Array.prototype.slice.call(arguments,2),n;wt?n=wt.delayedCallbacks:jt?n=jt:(n=jt=[],setTimeout(Wa,0));for(var l=function(a){n.push(function(){return i[a].apply(null,r)})},o=0;o<i.length;++o)l(o)}}function Wa(){var e=jt;jt=null;for(var t=0;t<e.length;++t)e[t]()}function ll(e,t,i,r){for(var n=0;n<t.changes.length;n++){var l=t.changes[n];l=="text"?Fa(e,t):l=="gutter"?al(e,t,i,r):l=="class"?zi(e,t):l=="widget"&&Pa(e,t,r)}t.changes=null}function Vt(e){return e.node==e.text&&(e.node=k("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),A&&E<8&&(e.node.style.zIndex=2)),e.node}function Ha(e,t){var i=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(i&&(i+=" CodeMirror-linebackground"),t.background)i?t.background.className=i:(t.background.parentNode.removeChild(t.background),t.background=null);else if(i){var r=Vt(t);t.background=r.insertBefore(k("div",null,i),r.firstChild),e.display.input.setUneditable(t.background)}}function ol(e,t){var i=e.display.externalMeasured;return i&&i.line==t.line?(e.display.externalMeasured=null,t.measure=i.measure,i.built):rl(e,t)}function Fa(e,t){var i=t.text.className,r=ol(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,zi(e,t)):i&&(t.text.className=i)}function zi(e,t){Ha(e,t),t.line.wrapClass?Vt(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var i=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=i||""}function al(e,t,i,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var n=Vt(t);t.gutterBackground=k("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),n.insertBefore(t.gutterBackground,t.text)}var l=t.line.gutterMarkers;if(e.options.lineNumbers||l){var o=Vt(t),a=t.gutter=k("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(a.setAttribute("aria-hidden","true"),e.display.input.setUneditable(a),o.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),e.options.lineNumbers&&(!l||!l["CodeMirror-linenumbers"])&&(t.lineNumber=a.appendChild(k("div",Ai(e.options,i),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),l)for(var s=0;s<e.display.gutterSpecs.length;++s){var u=e.display.gutterSpecs[s].className,f=l.hasOwnProperty(u)&&l[u];f&&a.appendChild(k("div",[f],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function Pa(e,t,i){t.alignable&&(t.alignable=null);for(var r=gt("CodeMirror-linewidget"),n=t.node.firstChild,l=void 0;n;n=l)l=n.nextSibling,r.test(n.className)&&t.node.removeChild(n);sl(e,t,i)}function Ea(e,t,i,r){var n=ol(e,t);return t.text=t.node=n.pre,n.bgClass&&(t.bgClass=n.bgClass),n.textClass&&(t.textClass=n.textClass),zi(e,t),al(e,t,i,r),sl(e,t,r),t.node}function sl(e,t,i){if(ul(e,t.line,t,i,!0),t.rest)for(var r=0;r<t.rest.length;r++)ul(e,t.rest[r],t,i,!1)}function ul(e,t,i,r,n){if(t.widgets)for(var l=Vt(i),o=0,a=t.widgets;o<a.length;++o){var s=a[o],u=k("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Ia(s,u,i,r),e.display.input.setUneditable(u),n&&s.above?l.insertBefore(u,i.gutter||i.text):l.appendChild(u),q(s,"redraw")}}function Ia(e,t,i,r){if(e.noHScroll){(i.alignable||(i.alignable=[])).push(t);var n=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(n-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=n+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function $t(e){if(e.height!=null)return e.height;var t=e.doc.cm;if(!t)return 0;if(!Ge(document.body,e.node)){var i="position: relative;";e.coverGutter&&(i+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(i+="width: "+t.display.wrapper.clientWidth+"px;"),pe(t.display.measure,k("div",[e.node],null,i))}return e.height=e.node.parentNode.offsetHeight}function Ee(e,t){for(var i=Ci(t);i!=e.wrapper;i=i.parentNode)if(!i||i.nodeType==1&&i.getAttribute("cm-ignore-events")=="true"||i.parentNode==e.sizer&&i!=e.mover)return!0}function Gr(e){return e.lineSpace.offsetTop}function Gi(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function fl(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=pe(e.measure,k("pre","x","CodeMirror-line-like")),i=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(i.paddingLeft),right:parseInt(i.paddingRight)};return!isNaN(r.left)&&!isNaN(r.right)&&(e.cachedPaddingH=r),r}function Ne(e){return Dn-e.display.nativeBarWidth}function ot(e){return e.display.scroller.clientWidth-Ne(e)-e.display.barWidth}function Ui(e){return e.display.scroller.clientHeight-Ne(e)-e.display.barHeight}function Ra(e,t,i){var r=e.options.lineWrapping,n=r&&ot(e);if(!t.measure.heights||r&&t.measure.width!=n){var l=t.measure.heights=[];if(r){t.measure.width=n;for(var o=t.text.firstChild.getClientRects(),a=0;a<o.length-1;a++){var s=o[a],u=o[a+1];Math.abs(s.bottom-u.bottom)>2&&l.push((s.bottom+u.top)/2-i.top)}}l.push(i.bottom-i.top)}}function hl(e,t,i){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var n=0;n<e.rest.length;n++)if(H(e.rest[n])>i)return{map:e.measure.maps[n],cache:e.measure.caches[n],before:!0}}function Ba(e,t){t=De(t);var i=H(t),r=e.display.externalMeasured=new nl(e.doc,t,i);r.lineN=i;var n=r.built=rl(e,r);return r.text=n.pre,pe(e.display.lineMeasure,n.pre),r}function cl(e,t,i,r){return Ae(e,St(e,t),i,r)}function Ki(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[ut(e,t)];var i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size)return i}function St(e,t){var i=H(t),r=Ki(e,i);r&&!r.text?r=null:r&&r.changes&&(ll(e,r,i,Zi(e)),e.curOp.forceUpdate=!0),r||(r=Ba(e,t));var n=hl(r,t,i);return{line:t,view:r,rect:null,map:n.map,cache:n.cache,before:n.before,hasHeights:!1}}function Ae(e,t,i,r,n){t.before&&(i=-1);var l=i+(r||""),o;return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Ra(e,t.view,t.rect),t.hasHeights=!0),o=Ga(e,t,i,r),o.bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:n?o.rtop:o.top,bottom:n?o.rbottom:o.bottom}}var dl={left:0,right:0,top:0,bottom:0};function pl(e,t,i){for(var r,n,l,o,a,s,u=0;u<e.length;u+=3)if(a=e[u],s=e[u+1],t<a?(n=0,l=1,o="left"):t<s?(n=t-a,l=n+1):(u==e.length-3||t==s&&e[u+3]>t)&&(l=s-a,n=l-1,t>=s&&(o="right")),n!=null){if(r=e[u+2],a==s&&i==(r.insertLeft?"left":"right")&&(o=i),i=="left"&&n==0)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[(u-=3)+2],o="left";if(i=="right"&&n==s-a)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],o="right";break}return{node:r,start:n,end:l,collapse:o,coverStart:a,coverEnd:s}}function za(e,t){var i=dl;if(t=="left")for(var r=0;r<e.length&&(i=e[r]).left==i.right;r++);else for(var n=e.length-1;n>=0&&(i=e[n]).left==i.right;n--);return i}function Ga(e,t,i,r){var n=pl(t.map,i,r),l=n.node,o=n.start,a=n.end,s=n.collapse,u;if(l.nodeType==3){for(var f=0;f<4;f++){for(;o&&mi(t.line.text.charAt(n.coverStart+o));)--o;for(;n.coverStart+a<n.coverEnd&&mi(t.line.text.charAt(n.coverStart+a));)++a;if(A&&E<9&&o==0&&a==n.coverEnd-n.coverStart?u=l.parentNode.getBoundingClientRect():u=za(et(l,o,a).getClientRects(),r),u.left||u.right||o==0)break;a=o,o=o-1,s="right"}A&&E<11&&(u=Ua(e.display.measure,u))}else{o>0&&(s=r="right");var h;e.options.lineWrapping&&(h=l.getClientRects()).length>1?u=h[r=="right"?h.length-1:0]:u=l.getBoundingClientRect()}if(A&&E<9&&!o&&(!u||!u.left&&!u.right)){var d=l.parentNode.getClientRects()[0];d?u={left:d.left,right:d.left+kt(e.display),top:d.top,bottom:d.bottom}:u=dl}for(var c=u.top-t.rect.top,p=u.bottom-t.rect.top,v=(c+p)/2,y=t.view.measure.heights,m=0;m<y.length-1&&!(v<y[m]);m++);var x=m?y[m-1]:0,b=y[m],C={left:(s=="right"?u.right:u.left)-t.rect.left,right:(s=="left"?u.left:u.right)-t.rect.left,top:x,bottom:b};return!u.left&&!u.right&&(C.bogus=!0),e.options.singleCursorHeightPerLine||(C.rtop=c,C.rbottom=p),C}function Ua(e,t){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!ia(e))return t;var i=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*i,right:t.right*i,top:t.top*r,bottom:t.bottom*r}}function vl(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function gl(e){e.display.externalMeasure=null,ze(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)vl(e.display.view[t])}function er(e){gl(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function yl(){return Lr&&Tr?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function ml(){return Lr&&Tr?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function _i(e){var t=0;if(e.widgets)for(var i=0;i<e.widgets.length;++i)e.widgets[i].above&&(t+=$t(e.widgets[i]));return t}function Ur(e,t,i,r,n){if(!n){var l=_i(t);i.top+=l,i.bottom+=l}if(r=="line")return i;r||(r="local");var o=Pe(t);if(r=="local"?o+=Gr(e.display):o-=e.display.viewOffset,r=="page"||r=="window"){var a=e.display.lineSpace.getBoundingClientRect();o+=a.top+(r=="window"?0:ml());var s=a.left+(r=="window"?0:yl());i.left+=s,i.right+=s}return i.top+=o,i.bottom+=o,i}function bl(e,t,i){if(i=="div")return t;var r=t.left,n=t.top;if(i=="page")r-=yl(),n-=ml();else if(i=="local"||!i){var l=e.display.sizer.getBoundingClientRect();r+=l.left,n+=l.top}var o=e.display.lineSpace.getBoundingClientRect();return{left:r-o.left,top:n-o.top}}function Kr(e,t,i,r,n){return r||(r=w(e.doc,t.line)),Ur(e,r,cl(e,r,t.ch,n),i)}function we(e,t,i,r,n,l){r=r||w(e.doc,t.line),n||(n=St(e,r));function o(p,v){var y=Ae(e,n,p,v?"right":"left",l);return v?y.left=y.right:y.right=y.left,Ur(e,r,y,i)}var a=He(r,e.doc.direction),s=t.ch,u=t.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return o(u=="before"?s-1:s,u=="before");function f(p,v,y){var m=a[v],x=m.level==1;return o(y?p-1:p,x!=y)}var h=Yt(a,s,u),d=Xt,c=f(s,h,u=="before");return d!=null&&(c.other=f(s,d,u!="before")),c}function xl(e,t){var i=0;t=D(e.doc,t),e.options.lineWrapping||(i=kt(e.display)*t.ch);var r=w(e.doc,t.line),n=Pe(r)+Gr(e.display);return{left:i,right:i,top:n,bottom:n+r.height}}function Xi(e,t,i,r,n){var l=g(e,t,i);return l.xRel=n,r&&(l.outside=r),l}function Yi(e,t,i){var r=e.doc;if(i+=e.display.viewOffset,i<0)return Xi(r.first,0,null,-1,-1);var n=lt(r,i),l=r.first+r.size-1;if(n>l)return Xi(r.first+r.size-1,w(r,l).text.length,null,1,1);t<0&&(t=0);for(var o=w(r,n);;){var a=Ka(e,o,n,t,i),s=ya(o,a.ch+(a.xRel>0||a.outside>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==n)return u;o=w(r,n=u.line)}}function Cl(e,t,i,r){r-=_i(t);var n=t.text.length,l=_t(function(o){return Ae(e,i,o-1).bottom<=r},n,0);return n=_t(function(o){return Ae(e,i,o).top>r},l,n),{begin:l,end:n}}function wl(e,t,i,r){i||(i=St(e,t));var n=Ur(e,t,Ae(e,i,r),"line").top;return Cl(e,t,i,n)}function qi(e,t,i,r){return e.bottom<=i?!1:e.top>i?!0:(r?e.left:e.right)>t}function Ka(e,t,i,r,n){n-=Pe(t);var l=St(e,t),o=_i(t),a=0,s=t.text.length,u=!0,f=He(t,e.doc.direction);if(f){var h=(e.options.lineWrapping?Xa:_a)(e,t,i,l,f,r,n);u=h.level!=1,a=u?h.from:h.to-1,s=u?h.to:h.from-1}var d=null,c=null,p=_t(function(L){var S=Ae(e,l,L);return S.top+=o,S.bottom+=o,qi(S,r,n,!1)?(S.top<=n&&S.left<=r&&(d=L,c=S),!0):!1},a,s),v,y,m=!1;if(c){var x=r-c.left<c.right-r,b=x==u;p=d+(b?0:1),y=b?"after":"before",v=x?c.left:c.right}else{!u&&(p==s||p==a)&&p++,y=p==0?"after":p==t.text.length?"before":Ae(e,l,p-(u?1:0)).bottom+o<=n==u?"after":"before";var C=we(e,g(i,p,y),"line",t,l);v=C.left,m=n<C.top?-1:n>=C.bottom?1:0}return p=Wn(t.text,p,1),Xi(i,p,y,m,r-v)}function _a(e,t,i,r,n,l,o){var a=_t(function(h){var d=n[h],c=d.level!=1;return qi(we(e,g(i,c?d.to:d.from,c?"before":"after"),"line",t,r),l,o,!0)},0,n.length-1),s=n[a];if(a>0){var u=s.level!=1,f=we(e,g(i,u?s.from:s.to,u?"after":"before"),"line",t,r);qi(f,l,o,!0)&&f.top>o&&(s=n[a-1])}return s}function Xa(e,t,i,r,n,l,o){var a=Cl(e,t,r,o),s=a.begin,u=a.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var f=null,h=null,d=0;d<n.length;d++){var c=n[d];if(!(c.from>=u||c.to<=s)){var p=c.level!=1,v=Ae(e,r,p?Math.min(u,c.to)-1:Math.max(s,c.from)).right,y=v<l?l-v+1e9:v-l;(!f||h>y)&&(f=c,h=y)}}return f||(f=n[n.length-1]),f.from<s&&(f={from:s,to:f.to,level:f.level}),f.to>u&&(f={from:f.from,to:u,level:f.level}),f}var at;function Lt(e){if(e.cachedTextHeight!=null)return e.cachedTextHeight;if(at==null){at=k("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)at.appendChild(document.createTextNode("x")),at.appendChild(k("br"));at.appendChild(document.createTextNode("x"))}pe(e.measure,at);var i=at.offsetHeight/50;return i>3&&(e.cachedTextHeight=i),ze(e.measure),i||1}function kt(e){if(e.cachedCharWidth!=null)return e.cachedCharWidth;var t=k("span","xxxxxxxxxx"),i=k("pre",[t],"CodeMirror-line-like");pe(e.measure,i);var r=t.getBoundingClientRect(),n=(r.right-r.left)/10;return n>2&&(e.cachedCharWidth=n),n||10}function Zi(e){for(var t=e.display,i={},r={},n=t.gutters.clientLeft,l=t.gutters.firstChild,o=0;l;l=l.nextSibling,++o){var a=e.display.gutterSpecs[o].className;i[a]=l.offsetLeft+l.clientLeft+n,r[a]=l.clientWidth}return{fixedPos:Qi(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:i,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function Qi(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Sl(e){var t=Lt(e.display),i=e.options.lineWrapping,r=i&&Math.max(5,e.display.scroller.clientWidth/kt(e.display)-3);return function(n){if(Ke(e.doc,n))return 0;var l=0;if(n.widgets)for(var o=0;o<n.widgets.length;o++)n.widgets[o].height&&(l+=n.widgets[o].height);return i?l+(Math.ceil(n.text.length/r)||1)*t:l+t}}function Ji(e){var t=e.doc,i=Sl(e);t.iter(function(r){var n=i(r);n!=r.height&&Te(r,n)})}function st(e,t,i,r){var n=e.display;if(!i&&Ci(t).getAttribute("cm-not-content")=="true")return null;var l,o,a=n.lineSpace.getBoundingClientRect();try{l=t.clientX-a.left,o=t.clientY-a.top}catch{return null}var s=Yi(e,l,o),u;if(r&&s.xRel>0&&(u=w(e.doc,s.line).text).length==s.ch){var f=be(u,u.length,e.options.tabSize)-u.length;s=g(s.line,Math.max(0,Math.round((l-fl(e.display).left)/kt(e.display))-f))}return s}function ut(e,t){if(t>=e.display.viewTo||(t-=e.display.viewFrom,t<0))return null;for(var i=e.display.view,r=0;r<i.length;r++)if(t-=i[r].size,t<0)return r}function ae(e,t,i,r){t==null&&(t=e.doc.first),i==null&&(i=e.doc.first+e.doc.size),r||(r=0);var n=e.display;if(r&&i<n.viewTo&&(n.updateLineNumbers==null||n.updateLineNumbers>t)&&(n.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=n.viewTo)Fe&&Ii(e.doc,t)<n.viewTo&&Xe(e);else if(i<=n.viewFrom)Fe&&el(e.doc,i+r)>n.viewFrom?Xe(e):(n.viewFrom+=r,n.viewTo+=r);else if(t<=n.viewFrom&&i>=n.viewTo)Xe(e);else if(t<=n.viewFrom){var l=_r(e,i,i+r,1);l?(n.view=n.view.slice(l.index),n.viewFrom=l.lineN,n.viewTo+=r):Xe(e)}else if(i>=n.viewTo){var o=_r(e,t,t,-1);o?(n.view=n.view.slice(0,o.index),n.viewTo=o.lineN):Xe(e)}else{var a=_r(e,t,t,-1),s=_r(e,i,i+r,1);a&&s?(n.view=n.view.slice(0,a.index).concat(zr(e,a.lineN,s.lineN)).concat(n.view.slice(s.index)),n.viewTo+=r):Xe(e)}var u=n.externalMeasured;u&&(i<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(n.externalMeasured=null))}function _e(e,t,i){e.curOp.viewChanged=!0;var r=e.display,n=e.display.externalMeasured;if(n&&t>=n.lineN&&t<n.lineN+n.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var l=r.view[ut(e,t)];if(l.node!=null){var o=l.changes||(l.changes=[]);$(o,i)==-1&&o.push(i)}}}function Xe(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function _r(e,t,i,r){var n=ut(e,t),l,o=e.display.view;if(!Fe||i==e.doc.first+e.doc.size)return{index:n,lineN:i};for(var a=e.display.viewFrom,s=0;s<n;s++)a+=o[s].size;if(a!=t){if(r>0){if(n==o.length-1)return null;l=a+o[n].size-t,n++}else l=a-t;t+=l,i+=l}for(;Ii(e.doc,i)!=i;){if(n==(r<0?0:o.length-1))return null;i+=r*o[n-(r<0?1:0)].size,n+=r}return{index:n,lineN:i}}function Ya(e,t,i){var r=e.display,n=r.view;n.length==0||t>=r.viewTo||i<=r.viewFrom?(r.view=zr(e,t,i),r.viewFrom=t):(r.viewFrom>t?r.view=zr(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(ut(e,t))),r.viewFrom=t,r.viewTo<i?r.view=r.view.concat(zr(e,r.viewTo,i)):r.viewTo>i&&(r.view=r.view.slice(0,ut(e,i)))),r.viewTo=i}function Ll(e){for(var t=e.display.view,i=0,r=0;r<t.length;r++){var n=t[r];!n.hidden&&(!n.node||n.changes)&&++i}return i}function tr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function kl(e,t){t===void 0&&(t=!0);for(var i=e.doc,r={},n=r.cursors=document.createDocumentFragment(),l=r.selection=document.createDocumentFragment(),o=0;o<i.sel.ranges.length;o++)if(!(!t&&o==i.sel.primIndex)){var a=i.sel.ranges[o];if(!(a.from().line>=e.display.viewTo||a.to().line<e.display.viewFrom)){var s=a.empty();(s||e.options.showCursorWhenSelecting)&&Tl(e,a.head,n),s||qa(e,a,l)}}return r}function Tl(e,t,i){var r=we(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),n=i.appendChild(k("div"," ","CodeMirror-cursor"));if(n.style.left=r.left+"px",n.style.top=r.top+"px",n.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var l=Kr(e,t,"div",null,null);l.right-l.left>0&&(n.style.width=l.right-l.left+"px")}if(r.other){var o=i.appendChild(k("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=r.other.left+"px",o.style.top=r.other.top+"px",o.style.height=(r.other.bottom-r.other.top)*.85+"px"}}function Xr(e,t){return e.top-t.top||e.left-t.left}function qa(e,t,i){var r=e.display,n=e.doc,l=document.createDocumentFragment(),o=fl(e.display),a=o.left,s=Math.max(r.sizerWidth,ot(e)-r.sizer.offsetLeft)-o.right,u=n.direction=="ltr";function f(b,C,L,S){C<0&&(C=0),C=Math.round(C),S=Math.round(S),l.appendChild(k("div",null,"CodeMirror-selected","position: absolute; left: "+b+`px;
                             top: `+C+"px; width: "+(L??s-b)+`px;
                             height: `+(S-C)+"px"))}function h(b,C,L){var S=w(n,b),N=S.text.length,P,J;function R(K,fe){return Kr(e,g(b,K),"div",S,fe)}function de(K,fe,V){var X=wl(e,S,null,K),_=fe=="ltr"==(V=="after")?"left":"right",B=V=="after"?X.begin:X.end-(/\s/.test(S.text.charAt(X.end-1))?2:1);return R(B,_)[_]}var ue=He(S,n.direction);return Jo(ue,C||0,L??N,function(K,fe,V,X){var _=V=="ltr",B=R(K,_?"left":"right"),he=R(fe-1,_?"right":"left"),Rt=C==null&&K==0,je=L==null&&fe==N,te=X==0,Oe=!ue||X==ue.length-1;if(he.top-B.top<=3){var j=(u?Rt:je)&&te,Sn=(u?je:Rt)&&Oe,Be=j?a:(_?B:he).left,pt=Sn?s:(_?he:B).right;f(Be,B.top,pt-Be,B.bottom)}else{var vt,le,Bt,Ln;_?(vt=u&&Rt&&te?a:B.left,le=u?s:de(K,V,"before"),Bt=u?a:de(fe,V,"after"),Ln=u&&je&&Oe?s:he.right):(vt=u?de(K,V,"before"):a,le=!u&&Rt&&te?s:B.right,Bt=!u&&je&&Oe?a:he.left,Ln=u?de(fe,V,"after"):s),f(vt,B.top,le-vt,B.bottom),B.bottom<he.top&&f(a,B.bottom,null,he.top),f(Bt,he.top,Ln-Bt,he.bottom)}(!P||Xr(B,P)<0)&&(P=B),Xr(he,P)<0&&(P=he),(!J||Xr(B,J)<0)&&(J=B),Xr(he,J)<0&&(J=he)}),{start:P,end:J}}var d=t.from(),c=t.to();if(d.line==c.line)h(d.line,d.ch,c.ch);else{var p=w(n,d.line),v=w(n,c.line),y=De(p)==De(v),m=h(d.line,d.ch,y?p.text.length+1:null).end,x=h(c.line,y?0:null,c.ch).start;y&&(m.top<x.top-2?(f(m.right,m.top,null,m.bottom),f(a,x.top,x.left,x.bottom)):f(m.right,m.top,x.left-m.right,m.bottom)),m.bottom<x.top&&f(a,m.bottom,null,x.top)}i.appendChild(l)}function ji(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var i=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){e.hasFocus()||Tt(e),t.cursorDiv.style.visibility=(i=!i)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Ml(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||$i(e))}function Vi(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Tt(e))},100)}function $i(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),e.options.readOnly!="nocursor"&&(e.state.focused||(G(e,"focus",e,t),e.state.focused=!0,tt(e.display.wrapper,"CodeMirror-focused"),!e.curOp&&e.display.selForContextMenu!=e.doc.sel&&(e.display.input.reset(),ie&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),ji(e))}function Tt(e,t){e.state.delayingBlurEvent||(e.state.focused&&(G(e,"blur",e,t),e.state.focused=!1,$e(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Yr(e){for(var t=e.display,i=t.lineDiv.offsetTop,r=Math.max(0,t.scroller.getBoundingClientRect().top),n=t.lineDiv.getBoundingClientRect().top,l=0,o=0;o<t.view.length;o++){var a=t.view[o],s=e.options.lineWrapping,u=void 0,f=0;if(!a.hidden){if(n+=a.line.height,A&&E<8){var h=a.node.offsetTop+a.node.offsetHeight;u=h-i,i=h}else{var d=a.node.getBoundingClientRect();u=d.bottom-d.top,!s&&a.text.firstChild&&(f=a.text.firstChild.getBoundingClientRect().right-d.left-1)}var c=a.line.height-u;if((c>.005||c<-.005)&&(n<r&&(l-=c),Te(a.line,u),Dl(a.line),a.rest))for(var p=0;p<a.rest.length;p++)Dl(a.rest[p]);if(f>e.display.sizerWidth){var v=Math.ceil(f/kt(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=a.line,e.display.maxLineChanged=!0)}}}Math.abs(l)>2&&(t.scroller.scrollTop+=l)}function Dl(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var i=e.widgets[t],r=i.node.parentNode;r&&(i.height=r.offsetHeight)}}function qr(e,t,i){var r=i&&i.top!=null?Math.max(0,i.top):e.scroller.scrollTop;r=Math.floor(r-Gr(e));var n=i&&i.bottom!=null?i.bottom:r+e.wrapper.clientHeight,l=lt(t,r),o=lt(t,n);if(i&&i.ensure){var a=i.ensure.from.line,s=i.ensure.to.line;a<l?(l=a,o=lt(t,Pe(w(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=o&&(l=lt(t,Pe(w(t,s))-e.wrapper.clientHeight),o=s)}return{from:l,to:Math.max(o,l+1)}}function Za(e,t){if(!Y(e,"scrollCursorIntoView")){var i=e.display,r=i.sizer.getBoundingClientRect(),n=null;if(t.top+r.top<0?n=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(n=!1),n!=null&&!_o){var l=k("div","​",null,`position: absolute;
                         top: `+(t.top-i.viewOffset-Gr(e.display))+`px;
                         height: `+(t.bottom-t.top+Ne(e)+i.barHeight)+`px;
                         left: `+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(l),l.scrollIntoView(n),e.display.lineSpace.removeChild(l)}}}function Qa(e,t,i,r){r==null&&(r=0);var n;!e.options.lineWrapping&&t==i&&(i=t.sticky=="before"?g(t.line,t.ch+1,"before"):t,t=t.ch?g(t.line,t.sticky=="before"?t.ch-1:t.ch,"after"):t);for(var l=0;l<5;l++){var o=!1,a=we(e,t),s=!i||i==t?a:we(e,i);n={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r};var u=en(e,n),f=e.doc.scrollTop,h=e.doc.scrollLeft;if(u.scrollTop!=null&&(ir(e,u.scrollTop),Math.abs(e.doc.scrollTop-f)>1&&(o=!0)),u.scrollLeft!=null&&(ft(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(o=!0)),!o)break}return n}function Ja(e,t){var i=en(e,t);i.scrollTop!=null&&ir(e,i.scrollTop),i.scrollLeft!=null&&ft(e,i.scrollLeft)}function en(e,t){var i=e.display,r=Lt(e.display);t.top<0&&(t.top=0);var n=e.curOp&&e.curOp.scrollTop!=null?e.curOp.scrollTop:i.scroller.scrollTop,l=Ui(e),o={};t.bottom-t.top>l&&(t.bottom=t.top+l);var a=e.doc.height+Gi(i),s=t.top<r,u=t.bottom>a-r;if(t.top<n)o.scrollTop=s?0:t.top;else if(t.bottom>n+l){var f=Math.min(t.top,(u?a:t.bottom)-l);f!=n&&(o.scrollTop=f)}var h=e.options.fixedGutter?0:i.gutters.offsetWidth,d=e.curOp&&e.curOp.scrollLeft!=null?e.curOp.scrollLeft:i.scroller.scrollLeft-h,c=ot(e)-i.gutters.offsetWidth,p=t.right-t.left>c;return p&&(t.right=t.left+c),t.left<10?o.scrollLeft=0:t.left<d?o.scrollLeft=Math.max(0,t.left+h-(p?0:10)):t.right>c+d-3&&(o.scrollLeft=t.right+(p?0:10)-c),o}function tn(e,t){t!=null&&(Zr(e),e.curOp.scrollTop=(e.curOp.scrollTop==null?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Mt(e){Zr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function rr(e,t,i){(t!=null||i!=null)&&Zr(e),t!=null&&(e.curOp.scrollLeft=t),i!=null&&(e.curOp.scrollTop=i)}function ja(e,t){Zr(e),e.curOp.scrollToPos=t}function Zr(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var i=xl(e,t.from),r=xl(e,t.to);Nl(e,i,r,t.margin)}}function Nl(e,t,i,r){var n=en(e,{left:Math.min(t.left,i.left),top:Math.min(t.top,i.top)-r,right:Math.max(t.right,i.right),bottom:Math.max(t.bottom,i.bottom)+r});rr(e,n.scrollLeft,n.scrollTop)}function ir(e,t){Math.abs(e.doc.scrollTop-t)<2||(We||nn(e,{top:t}),Al(e,t,!0),We&&nn(e),or(e,100))}function Al(e,t,i){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),!(e.display.scroller.scrollTop==t&&!i)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function ft(e,t,i,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),!((i?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r)&&(e.doc.scrollLeft=t,Pl(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function nr(e){var t=e.display,i=t.gutters.offsetWidth,r=Math.round(e.doc.height+Gi(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?i:0,docHeight:r,scrollHeight:r+Ne(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:i}}var ht=function(e,t,i){this.cm=i;var r=this.vert=k("div",[k("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),n=this.horiz=k("div",[k("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=n.tabIndex=-1,e(r),e(n),T(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),T(n,"scroll",function(){n.clientWidth&&t(n.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,A&&E<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};ht.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,i=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(i){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var n=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+n)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=i?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var l=e.viewWidth-e.barLeft-(i?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+l)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(r==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:i?r:0,bottom:t?r:0}},ht.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},ht.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},ht.prototype.zeroWidthHack=function(){var e=ye&&!Ko?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new Ue,this.disableVert=new Ue},ht.prototype.enableZeroWidthBar=function(e,t,i){e.style.pointerEvents="auto";function r(){var n=e.getBoundingClientRect(),l=i=="vert"?document.elementFromPoint(n.right-1,(n.top+n.bottom)/2):document.elementFromPoint((n.right+n.left)/2,n.bottom-1);l!=e?e.style.pointerEvents="none":t.set(1e3,r)}t.set(1e3,r)},ht.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var lr=function(){};lr.prototype.update=function(){return{bottom:0,right:0}},lr.prototype.setScrollLeft=function(){},lr.prototype.setScrollTop=function(){},lr.prototype.clear=function(){};function Dt(e,t){t||(t=nr(e));var i=e.display.barWidth,r=e.display.barHeight;Ol(e,t);for(var n=0;n<4&&i!=e.display.barWidth||r!=e.display.barHeight;n++)i!=e.display.barWidth&&e.options.lineWrapping&&Yr(e),Ol(e,nr(e)),i=e.display.barWidth,r=e.display.barHeight}function Ol(e,t){var i=e.display,r=i.scrollbars.update(t);i.sizer.style.paddingRight=(i.barWidth=r.right)+"px",i.sizer.style.paddingBottom=(i.barHeight=r.bottom)+"px",i.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(i.scrollbarFiller.style.display="block",i.scrollbarFiller.style.height=r.bottom+"px",i.scrollbarFiller.style.width=r.right+"px"):i.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(i.gutterFiller.style.display="block",i.gutterFiller.style.height=r.bottom+"px",i.gutterFiller.style.width=t.gutterWidth+"px"):i.gutterFiller.style.display=""}var Wl={native:ht,null:lr};function Hl(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&$e(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Wl[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),T(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,i){i=="horizontal"?ft(e,t):ir(e,t)},e),e.display.scrollbars.addClass&&tt(e.display.wrapper,e.display.scrollbars.addClass)}var Va=0;function ct(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Va,markArrays:null},Na(e.curOp)}function dt(e){var t=e.curOp;t&&Oa(t,function(i){for(var r=0;r<i.ops.length;r++)i.ops[r].cm.curOp=null;$a(i)})}function $a(e){for(var t=e.ops,i=0;i<t.length;i++)es(t[i]);for(var r=0;r<t.length;r++)ts(t[r]);for(var n=0;n<t.length;n++)rs(t[n]);for(var l=0;l<t.length;l++)is(t[l]);for(var o=0;o<t.length;o++)ns(t[o])}function es(e){var t=e.cm,i=t.display;os(t),e.updateMaxLine&&Bi(t),e.mustUpdate=e.viewChanged||e.forceUpdate||e.scrollTop!=null||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Qr(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function ts(e){e.updatedDisplay=e.mustUpdate&&rn(e.cm,e.update)}function rs(e){var t=e.cm,i=t.display;e.updatedDisplay&&Yr(t),e.barMeasure=nr(t),i.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=cl(t,i.maxLine,i.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(i.scroller.clientWidth,i.sizer.offsetLeft+e.adjustWidthTo+Ne(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,i.sizer.offsetLeft+e.adjustWidthTo-ot(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=i.input.prepareSelection())}function is(e){var t=e.cm;e.adjustWidthTo!=null&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&ft(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var i=e.focus&&e.focus==me();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,i),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Dt(t,e.barMeasure),e.updatedDisplay&&on(t,e.barMeasure),e.selectionChanged&&ji(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),i&&Ml(e.cm)}function ns(e){var t=e.cm,i=t.display,r=t.doc;if(e.updatedDisplay&&Fl(t,e.update),i.wheelStartX!=null&&(e.scrollTop!=null||e.scrollLeft!=null||e.scrollToPos)&&(i.wheelStartX=i.wheelStartY=null),e.scrollTop!=null&&Al(t,e.scrollTop,e.forceScroll),e.scrollLeft!=null&&ft(t,e.scrollLeft,!0,!0),e.scrollToPos){var n=Qa(t,D(r,e.scrollToPos.from),D(r,e.scrollToPos.to),e.scrollToPos.margin);Za(t,n)}var l=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(l)for(var a=0;a<l.length;++a)l[a].lines.length||G(l[a],"hide");if(o)for(var s=0;s<o.length;++s)o[s].lines.length&&G(o[s],"unhide");i.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&G(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function ce(e,t){if(e.curOp)return t();ct(e);try{return t()}finally{dt(e)}}function Z(e,t){return function(){if(e.curOp)return t.apply(e,arguments);ct(e);try{return t.apply(e,arguments)}finally{dt(e)}}}function ne(e){return function(){if(this.curOp)return e.apply(this,arguments);ct(this);try{return e.apply(this,arguments)}finally{dt(this)}}}function Q(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);ct(t);try{return e.apply(this,arguments)}finally{dt(t)}}}function or(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,di(ls,e))}function ls(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var i=+new Date+e.options.workTime,r=Qt(e,t.highlightFrontier),n=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(l){if(r.line>=e.display.viewFrom){var o=l.styles,a=l.text.length>e.options.maxHighlightLength?it(t.mode,r.state):null,s=zn(e,l,r,!0);a&&(r.state=a),l.styles=s.styles;var u=l.styleClasses,f=s.classes;f?l.styleClasses=f:u&&(l.styleClasses=null);for(var h=!o||o.length!=l.styles.length||u!=f&&(!u||!f||u.bgClass!=f.bgClass||u.textClass!=f.textClass),d=0;!h&&d<o.length;++d)h=o[d]!=l.styles[d];h&&n.push(r.line),l.stateAfter=r.save(),r.nextLine()}else l.text.length<=e.options.maxHighlightLength&&Hi(e,l.text,r),l.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>i)return or(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),n.length&&ce(e,function(){for(var l=0;l<n.length;l++)_e(e,n[l],"text")})}}var Qr=function(e,t,i){var r=e.display;this.viewport=t,this.visible=qr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=ot(e),this.force=i,this.dims=Zi(e),this.events=[]};Qr.prototype.signal=function(e,t){xe(e,t)&&this.events.push(arguments)},Qr.prototype.finish=function(){for(var e=0;e<this.events.length;e++)G.apply(null,this.events[e])};function os(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Ne(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Ne(e)+"px",t.scrollbarsClipped=!0)}function as(e){if(e.hasFocus())return null;var t=me();if(!t||!Ge(e.display.lineDiv,t))return null;var i={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&Ge(e.display.lineDiv,r.anchorNode)&&(i.anchorNode=r.anchorNode,i.anchorOffset=r.anchorOffset,i.focusNode=r.focusNode,i.focusOffset=r.focusOffset)}return i}function ss(e){if(!(!e||!e.activeElt||e.activeElt==me())&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&Ge(document.body,e.anchorNode)&&Ge(document.body,e.focusNode))){var t=window.getSelection(),i=document.createRange();i.setEnd(e.anchorNode,e.anchorOffset),i.collapse(!1),t.removeAllRanges(),t.addRange(i),t.extend(e.focusNode,e.focusOffset)}}function rn(e,t){var i=e.display,r=e.doc;if(t.editorIsHidden)return Xe(e),!1;if(!t.force&&t.visible.from>=i.viewFrom&&t.visible.to<=i.viewTo&&(i.updateLineNumbers==null||i.updateLineNumbers>=i.viewTo)&&i.renderedView==i.view&&Ll(e)==0)return!1;El(e)&&(Xe(e),t.dims=Zi(e));var n=r.first+r.size,l=Math.max(t.visible.from-e.options.viewportMargin,r.first),o=Math.min(n,t.visible.to+e.options.viewportMargin);i.viewFrom<l&&l-i.viewFrom<20&&(l=Math.max(r.first,i.viewFrom)),i.viewTo>o&&i.viewTo-o<20&&(o=Math.min(n,i.viewTo)),Fe&&(l=Ii(e.doc,l),o=el(e.doc,o));var a=l!=i.viewFrom||o!=i.viewTo||i.lastWrapHeight!=t.wrapperHeight||i.lastWrapWidth!=t.wrapperWidth;Ya(e,l,o),i.viewOffset=Pe(w(e.doc,i.viewFrom)),e.display.mover.style.top=i.viewOffset+"px";var s=Ll(e);if(!a&&s==0&&!t.force&&i.renderedView==i.view&&(i.updateLineNumbers==null||i.updateLineNumbers>=i.viewTo))return!1;var u=as(e);return s>4&&(i.lineDiv.style.display="none"),us(e,i.updateLineNumbers,t.dims),s>4&&(i.lineDiv.style.display=""),i.renderedView=i.view,ss(u),ze(i.cursorDiv),ze(i.selectionDiv),i.gutters.style.height=i.sizer.style.minHeight=0,a&&(i.lastWrapHeight=t.wrapperHeight,i.lastWrapWidth=t.wrapperWidth,or(e,400)),i.updateLineNumbers=null,!0}function Fl(e,t){for(var i=t.viewport,r=!0;;r=!1){if(!r||!e.options.lineWrapping||t.oldDisplayWidth==ot(e)){if(i&&i.top!=null&&(i={top:Math.min(e.doc.height+Gi(e.display)-Ui(e),i.top)}),t.visible=qr(e.display,e.doc,i),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break}else r&&(t.visible=qr(e.display,e.doc,i));if(!rn(e,t))break;Yr(e);var n=nr(e);tr(e),Dt(e,n),on(e,n),t.force=!1}t.signal(e,"update",e),(e.display.viewFrom!=e.display.reportedViewFrom||e.display.viewTo!=e.display.reportedViewTo)&&(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function nn(e,t){var i=new Qr(e,t);if(rn(e,i)){Yr(e),Fl(e,i);var r=nr(e);tr(e),Dt(e,r),on(e,r),i.finish()}}function us(e,t,i){var r=e.display,n=e.options.lineNumbers,l=r.lineDiv,o=l.firstChild;function a(p){var v=p.nextSibling;return ie&&ye&&e.display.currentWheelTarget==p?p.style.display="none":p.parentNode.removeChild(p),v}for(var s=r.view,u=r.viewFrom,f=0;f<s.length;f++){var h=s[f];if(!h.hidden)if(!h.node||h.node.parentNode!=l){var d=Ea(e,h,u,i);l.insertBefore(d,o)}else{for(;o!=h.node;)o=a(o);var c=n&&t!=null&&t<=u&&h.lineNumber;h.changes&&($(h.changes,"gutter")>-1&&(c=!1),ll(e,h,u,i)),c&&(ze(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(Ai(e.options,u)))),o=h.node.nextSibling}u+=h.size}for(;o;)o=a(o)}function ln(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",q(e,"gutterChanged",e)}function on(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Ne(e)+"px"}function Pl(e){var t=e.display,i=t.view;if(!(!t.alignWidgets&&(!t.gutters.firstChild||!e.options.fixedGutter))){for(var r=Qi(t)-t.scroller.scrollLeft+e.doc.scrollLeft,n=t.gutters.offsetWidth,l=r+"px",o=0;o<i.length;o++)if(!i[o].hidden){e.options.fixedGutter&&(i[o].gutter&&(i[o].gutter.style.left=l),i[o].gutterBackground&&(i[o].gutterBackground.style.left=l));var a=i[o].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=l}e.options.fixedGutter&&(t.gutters.style.left=r+n+"px")}}function El(e){if(!e.options.lineNumbers)return!1;var t=e.doc,i=Ai(e.options,t.first+t.size-1),r=e.display;if(i.length!=r.lineNumChars){var n=r.measure.appendChild(k("div",[k("div",i)],"CodeMirror-linenumber CodeMirror-gutter-elt")),l=n.firstChild.offsetWidth,o=n.offsetWidth-l;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(l,r.lineGutter.offsetWidth-o)+1,r.lineNumWidth=r.lineNumInnerWidth+o,r.lineNumChars=r.lineNumInnerWidth?i.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",ln(e.display),!0}return!1}function an(e,t){for(var i=[],r=!1,n=0;n<e.length;n++){var l=e[n],o=null;if(typeof l!="string"&&(o=l.style,l=l.className),l=="CodeMirror-linenumbers")if(t)r=!0;else continue;i.push({className:l,style:o})}return t&&!r&&i.push({className:"CodeMirror-linenumbers",style:null}),i}function Il(e){var t=e.gutters,i=e.gutterSpecs;ze(t),e.lineGutter=null;for(var r=0;r<i.length;++r){var n=i[r],l=n.className,o=n.style,a=t.appendChild(k("div",null,"CodeMirror-gutter "+l));o&&(a.style.cssText=o),l=="CodeMirror-linenumbers"&&(e.lineGutter=a,a.style.width=(e.lineNumWidth||1)+"px")}t.style.display=i.length?"":"none",ln(e)}function ar(e){Il(e.display),ae(e),Pl(e)}function fs(e,t,i,r){var n=this;this.input=i,n.scrollbarFiller=k("div",null,"CodeMirror-scrollbar-filler"),n.scrollbarFiller.setAttribute("cm-not-content","true"),n.gutterFiller=k("div",null,"CodeMirror-gutter-filler"),n.gutterFiller.setAttribute("cm-not-content","true"),n.lineDiv=yt("div",null,"CodeMirror-code"),n.selectionDiv=k("div",null,null,"position: relative; z-index: 1"),n.cursorDiv=k("div",null,"CodeMirror-cursors"),n.measure=k("div",null,"CodeMirror-measure"),n.lineMeasure=k("div",null,"CodeMirror-measure"),n.lineSpace=yt("div",[n.measure,n.lineMeasure,n.selectionDiv,n.cursorDiv,n.lineDiv],null,"position: relative; outline: none");var l=yt("div",[n.lineSpace],"CodeMirror-lines");n.mover=k("div",[l],null,"position: relative"),n.sizer=k("div",[n.mover],"CodeMirror-sizer"),n.sizerWidth=null,n.heightForcer=k("div",null,null,"position: absolute; height: "+Dn+"px; width: 1px;"),n.gutters=k("div",null,"CodeMirror-gutters"),n.lineGutter=null,n.scroller=k("div",[n.sizer,n.heightForcer,n.gutters],"CodeMirror-scroll"),n.scroller.setAttribute("tabIndex","-1"),n.wrapper=k("div",[n.scrollbarFiller,n.gutterFiller,n.scroller],"CodeMirror"),n.wrapper.setAttribute("translate","no"),A&&E<8&&(n.gutters.style.zIndex=-1,n.scroller.style.paddingRight=0),!ie&&!(We&&Gt)&&(n.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(n.wrapper):e(n.wrapper)),n.viewFrom=n.viewTo=t.first,n.reportedViewFrom=n.reportedViewTo=t.first,n.view=[],n.renderedView=null,n.externalMeasured=null,n.viewOffset=0,n.lastWrapHeight=n.lastWrapWidth=0,n.updateLineNumbers=null,n.nativeBarWidth=n.barHeight=n.barWidth=0,n.scrollbarsClipped=!1,n.lineNumWidth=n.lineNumInnerWidth=n.lineNumChars=null,n.alignWidgets=!1,n.cachedCharWidth=n.cachedTextHeight=n.cachedPaddingH=null,n.maxLine=null,n.maxLineLength=0,n.maxLineChanged=!1,n.wheelDX=n.wheelDY=n.wheelStartX=n.wheelStartY=null,n.shift=!1,n.selForContextMenu=null,n.activeTouch=null,n.gutterSpecs=an(r.gutters,r.lineNumbers),Il(n),i.init(n)}var Jr=0,Ie=null;A?Ie=-.53:We?Ie=15:Lr?Ie=-.7:kr&&(Ie=-1/3);function Rl(e){var t=e.wheelDeltaX,i=e.wheelDeltaY;return t==null&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),i==null&&e.detail&&e.axis==e.VERTICAL_AXIS?i=e.detail:i==null&&(i=e.wheelDelta),{x:t,y:i}}function hs(e){var t=Rl(e);return t.x*=Ie,t.y*=Ie,t}function Bl(e,t){var i=Rl(t),r=i.x,n=i.y,l=Ie;t.deltaMode===0&&(r=t.deltaX,n=t.deltaY,l=1);var o=e.display,a=o.scroller,s=a.scrollWidth>a.clientWidth,u=a.scrollHeight>a.clientHeight;if(r&&s||n&&u){if(n&&ye&&ie){e:for(var f=t.target,h=o.view;f!=a;f=f.parentNode)for(var d=0;d<h.length;d++)if(h[d].node==f){e.display.currentWheelTarget=f;break e}}if(r&&!We&&!Ce&&l!=null){n&&u&&ir(e,Math.max(0,a.scrollTop+n*l)),ft(e,Math.max(0,a.scrollLeft+r*l)),(!n||n&&u)&&oe(t),o.wheelStartX=null;return}if(n&&l!=null){var c=n*l,p=e.doc.scrollTop,v=p+o.wrapper.clientHeight;c<0?p=Math.max(0,p+c-50):v=Math.min(e.doc.height,v+c+50),nn(e,{top:p,bottom:v})}Jr<20&&t.deltaMode!==0&&(o.wheelStartX==null?(o.wheelStartX=a.scrollLeft,o.wheelStartY=a.scrollTop,o.wheelDX=r,o.wheelDY=n,setTimeout(function(){if(o.wheelStartX!=null){var y=a.scrollLeft-o.wheelStartX,m=a.scrollTop-o.wheelStartY,x=m&&o.wheelDY&&m/o.wheelDY||y&&o.wheelDX&&y/o.wheelDX;o.wheelStartX=o.wheelStartY=null,x&&(Ie=(Ie*Jr+x)/(Jr+1),++Jr)}},200)):(o.wheelDX+=r,o.wheelDY+=n))}}var ge=function(e,t){this.ranges=e,this.primIndex=t};ge.prototype.primary=function(){return this.ranges[this.primIndex]},ge.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var i=this.ranges[t],r=e.ranges[t];if(!Oi(i.anchor,r.anchor)||!Oi(i.head,r.head))return!1}return!0},ge.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new O(Wi(this.ranges[t].anchor),Wi(this.ranges[t].head));return new ge(e,this.primIndex)},ge.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},ge.prototype.contains=function(e,t){t||(t=e);for(var i=0;i<this.ranges.length;i++){var r=this.ranges[i];if(M(t,r.from())>=0&&M(e,r.to())<=0)return i}return-1};var O=function(e,t){this.anchor=e,this.head=t};O.prototype.from=function(){return Hr(this.anchor,this.head)},O.prototype.to=function(){return Wr(this.anchor,this.head)},O.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};function Se(e,t,i){var r=e&&e.options.selectionsMayTouch,n=t[i];t.sort(function(d,c){return M(d.from(),c.from())}),i=$(t,n);for(var l=1;l<t.length;l++){var o=t[l],a=t[l-1],s=M(a.to(),o.from());if(r&&!o.empty()?s>0:s>=0){var u=Hr(a.from(),o.from()),f=Wr(a.to(),o.to()),h=a.empty()?o.from()==o.head:a.from()==a.head;l<=i&&--i,t.splice(--l,2,new O(h?f:u,h?u:f))}}return new ge(t,i)}function Ye(e,t){return new ge([new O(e,t||e)],0)}function qe(e){return e.text?g(e.from.line+e.text.length-1,W(e.text).length+(e.text.length==1?e.from.ch:0)):e.to}function zl(e,t){if(M(e,t.from)<0)return e;if(M(e,t.to)<=0)return qe(t);var i=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=qe(t).ch-t.to.ch),g(i,r)}function sn(e,t){for(var i=[],r=0;r<e.sel.ranges.length;r++){var n=e.sel.ranges[r];i.push(new O(zl(n.anchor,t),zl(n.head,t)))}return Se(e.cm,i,e.sel.primIndex)}function Gl(e,t,i){return e.line==t.line?g(i.line,e.ch-t.ch+i.ch):g(i.line+(e.line-t.line),e.ch)}function cs(e,t,i){for(var r=[],n=g(e.first,0),l=n,o=0;o<t.length;o++){var a=t[o],s=Gl(a.from,n,l),u=Gl(qe(a),n,l);if(n=a.to,l=u,i=="around"){var f=e.sel.ranges[o],h=M(f.head,f.anchor)<0;r[o]=new O(h?u:s,h?s:u)}else r[o]=new O(s,s)}return new ge(r,e.sel.primIndex)}function un(e){e.doc.mode=Mi(e.options,e.doc.modeOption),sr(e)}function sr(e){e.doc.iter(function(t){t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,or(e,100),e.state.modeGen++,e.curOp&&ae(e)}function Ul(e,t){return t.from.ch==0&&t.to.ch==0&&W(t.text)==""&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function fn(e,t,i,r){function n(x){return i?i[x]:null}function l(x,b,C){xa(x,b,C,r),q(x,"change",x,t)}function o(x,b){for(var C=[],L=x;L<b;++L)C.push(new Ct(u[L],n(L),r));return C}var a=t.from,s=t.to,u=t.text,f=w(e,a.line),h=w(e,s.line),d=W(u),c=n(u.length-1),p=s.line-a.line;if(t.full)e.insert(0,o(0,u.length)),e.remove(u.length,e.size-u.length);else if(Ul(e,t)){var v=o(0,u.length-1);l(h,h.text,c),p&&e.remove(a.line,p),v.length&&e.insert(a.line,v)}else if(f==h)if(u.length==1)l(f,f.text.slice(0,a.ch)+d+f.text.slice(s.ch),c);else{var y=o(1,u.length-1);y.push(new Ct(d+f.text.slice(s.ch),c,r)),l(f,f.text.slice(0,a.ch)+u[0],n(0)),e.insert(a.line+1,y)}else if(u.length==1)l(f,f.text.slice(0,a.ch)+u[0]+h.text.slice(s.ch),n(0)),e.remove(a.line+1,p);else{l(f,f.text.slice(0,a.ch)+u[0],n(0)),l(h,d+h.text.slice(s.ch),c);var m=o(1,u.length-1);p>1&&e.remove(a.line+1,p-1),e.insert(a.line+1,m)}q(e,"change",e,t)}function Ze(e,t,i){function r(n,l,o){if(n.linked)for(var a=0;a<n.linked.length;++a){var s=n.linked[a];if(s.doc!=l){var u=o&&s.sharedHist;i&&!u||(t(s.doc,u),r(s.doc,n,u))}}}r(e,null,!0)}function Kl(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Ji(e),un(e),_l(e),e.options.direction=t.direction,e.options.lineWrapping||Bi(e),e.options.mode=t.modeOption,ae(e)}function _l(e){(e.doc.direction=="rtl"?tt:$e)(e.display.lineDiv,"CodeMirror-rtl")}function ds(e){ce(e,function(){_l(e),ae(e)})}function jr(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function hn(e,t){var i={from:Wi(t.from),to:qe(t),text:nt(e,t.from,t.to)};return ql(e,i,t.from.line,t.to.line+1),Ze(e,function(r){return ql(r,i,t.from.line,t.to.line+1)},!0),i}function Xl(e){for(;e.length;){var t=W(e);if(t.ranges)e.pop();else break}}function ps(e,t){if(t)return Xl(e.done),W(e.done);if(e.done.length&&!W(e.done).ranges)return W(e.done);if(e.done.length>1&&!e.done[e.done.length-2].ranges)return e.done.pop(),W(e.done)}function Yl(e,t,i,r){var n=e.history;n.undone.length=0;var l=+new Date,o,a;if((n.lastOp==r||n.lastOrigin==t.origin&&t.origin&&(t.origin.charAt(0)=="+"&&n.lastModTime>l-(e.cm?e.cm.options.historyEventDelay:500)||t.origin.charAt(0)=="*"))&&(o=ps(n,n.lastOp==r)))a=W(o.changes),M(t.from,t.to)==0&&M(t.from,a.to)==0?a.to=qe(t):o.changes.push(hn(e,t));else{var s=W(n.done);for((!s||!s.ranges)&&Vr(e.sel,n.done),o={changes:[hn(e,t)],generation:n.generation},n.done.push(o);n.done.length>n.undoDepth;)n.done.shift(),n.done[0].ranges||n.done.shift()}n.done.push(i),n.generation=++n.maxGeneration,n.lastModTime=n.lastSelTime=l,n.lastOp=n.lastSelOp=r,n.lastOrigin=n.lastSelOrigin=t.origin,a||G(e,"historyAdded")}function vs(e,t,i,r){var n=t.charAt(0);return n=="*"||n=="+"&&i.ranges.length==r.ranges.length&&i.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function gs(e,t,i,r){var n=e.history,l=r&&r.origin;i==n.lastSelOp||l&&n.lastSelOrigin==l&&(n.lastModTime==n.lastSelTime&&n.lastOrigin==l||vs(e,l,W(n.done),t))?n.done[n.done.length-1]=t:Vr(t,n.done),n.lastSelTime=+new Date,n.lastSelOrigin=l,n.lastSelOp=i,r&&r.clearRedo!==!1&&Xl(n.undone)}function Vr(e,t){var i=W(t);i&&i.ranges&&i.equals(e)||t.push(e)}function ql(e,t,i,r){var n=t["spans_"+e.id],l=0;e.iter(Math.max(e.first,i),Math.min(e.first+e.size,r),function(o){o.markedSpans&&((n||(n=t["spans_"+e.id]={}))[l]=o.markedSpans),++l})}function ys(e){if(!e)return null;for(var t,i=0;i<e.length;++i)e[i].marker.explicitlyCleared?t||(t=e.slice(0,i)):t&&t.push(e[i]);return t?t.length?t:null:e}function ms(e,t){var i=t["spans_"+e.id];if(!i)return null;for(var r=[],n=0;n<t.text.length;++n)r.push(ys(i[n]));return r}function Zl(e,t){var i=ms(e,t),r=Pi(e,t);if(!i)return r;if(!r)return i;for(var n=0;n<i.length;++n){var l=i[n],o=r[n];if(l&&o)e:for(var a=0;a<o.length;++a){for(var s=o[a],u=0;u<l.length;++u)if(l[u].marker==s.marker)continue e;l.push(s)}else o&&(i[n]=o)}return i}function Nt(e,t,i){for(var r=[],n=0;n<e.length;++n){var l=e[n];if(l.ranges){r.push(i?ge.prototype.deepCopy.call(l):l);continue}var o=l.changes,a=[];r.push({changes:a});for(var s=0;s<o.length;++s){var u=o[s],f=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(f=h.match(/^spans_(\d+)$/))&&$(t,Number(f[1]))>-1&&(W(a)[h]=u[h],delete u[h])}}return r}function cn(e,t,i,r){if(r){var n=e.anchor;if(i){var l=M(t,n)<0;l!=M(i,n)<0?(n=t,t=i):l!=M(t,i)<0&&(t=i)}return new O(n,t)}else return new O(i||t,t)}function $r(e,t,i,r,n){n==null&&(n=e.cm&&(e.cm.display.shift||e.extend)),ee(e,new ge([cn(e.sel.primary(),t,i,n)],0),r)}function Ql(e,t,i){for(var r=[],n=e.cm&&(e.cm.display.shift||e.extend),l=0;l<e.sel.ranges.length;l++)r[l]=cn(e.sel.ranges[l],t[l],null,n);var o=Se(e.cm,r,e.sel.primIndex);ee(e,o,i)}function dn(e,t,i,r){var n=e.sel.ranges.slice(0);n[t]=i,ee(e,Se(e.cm,n,e.sel.primIndex),r)}function Jl(e,t,i,r){ee(e,Ye(t,i),r)}function bs(e,t,i){var r={ranges:t.ranges,update:function(n){this.ranges=[];for(var l=0;l<n.length;l++)this.ranges[l]=new O(D(e,n[l].anchor),D(e,n[l].head))},origin:i&&i.origin};return G(e,"beforeSelectionChange",e,r),e.cm&&G(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?Se(e.cm,r.ranges,r.ranges.length-1):t}function jl(e,t,i){var r=e.history.done,n=W(r);n&&n.ranges?(r[r.length-1]=t,ei(e,t,i)):ee(e,t,i)}function ee(e,t,i){ei(e,t,i),gs(e,e.sel,e.cm?e.cm.curOp.id:NaN,i)}function ei(e,t,i){(xe(e,"beforeSelectionChange")||e.cm&&xe(e.cm,"beforeSelectionChange"))&&(t=bs(e,t,i));var r=i&&i.bias||(M(t.primary().head,e.sel.primary().head)<0?-1:1);Vl(e,eo(e,t,r,!0)),!(i&&i.scroll===!1)&&e.cm&&e.cm.getOption("readOnly")!="nocursor"&&Mt(e.cm)}function Vl(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Fn(e.cm)),q(e,"cursorActivity",e))}function $l(e){Vl(e,eo(e,e.sel,null,!1))}function eo(e,t,i,r){for(var n,l=0;l<t.ranges.length;l++){var o=t.ranges[l],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[l],s=ti(e,o.anchor,a&&a.anchor,i,r),u=ti(e,o.head,a&&a.head,i,r);(n||s!=o.anchor||u!=o.head)&&(n||(n=t.ranges.slice(0,l)),n[l]=new O(s,u))}return n?Se(e.cm,n,t.primIndex):t}function At(e,t,i,r,n){var l=w(e,t.line);if(l.markedSpans)for(var o=0;o<l.markedSpans.length;++o){var a=l.markedSpans[o],s=a.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,f="selectRight"in s?!s.selectRight:s.inclusiveRight;if((a.from==null||(u?a.from<=t.ch:a.from<t.ch))&&(a.to==null||(f?a.to>=t.ch:a.to>t.ch))){if(n&&(G(s,"beforeCursorEnter"),s.explicitlyCleared))if(l.markedSpans){--o;continue}else break;if(!s.atomic)continue;if(i){var h=s.find(r<0?1:-1),d=void 0;if((r<0?f:u)&&(h=to(e,h,-r,h&&h.line==t.line?l:null)),h&&h.line==t.line&&(d=M(h,i))&&(r<0?d<0:d>0))return At(e,h,t,r,n)}var c=s.find(r<0?-1:1);return(r<0?u:f)&&(c=to(e,c,r,c.line==t.line?l:null)),c?At(e,c,t,r,n):null}}return t}function ti(e,t,i,r,n){var l=r||1,o=At(e,t,i,l,n)||!n&&At(e,t,i,l,!0)||At(e,t,i,-l,n)||!n&&At(e,t,i,-l,!0);return o||(e.cantEdit=!0,g(e.first,0))}function to(e,t,i,r){return i<0&&t.ch==0?t.line>e.first?D(e,g(t.line-1)):null:i>0&&t.ch==(r||w(e,t.line)).text.length?t.line<e.first+e.size-1?g(t.line+1,0):null:new g(t.line,t.ch+i)}function ro(e){e.setSelection(g(e.firstLine(),0),g(e.lastLine()),ke)}function io(e,t,i){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return i&&(r.update=function(n,l,o,a){n&&(r.from=D(e,n)),l&&(r.to=D(e,l)),o&&(r.text=o),a!==void 0&&(r.origin=a)}),G(e,"beforeChange",e,r),e.cm&&G(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function Ot(e,t,i){if(e.cm){if(!e.cm.curOp)return Z(e.cm,Ot)(e,t,i);if(e.cm.state.suppressEdits)return}if(!((xe(e,"beforeChange")||e.cm&&xe(e.cm,"beforeChange"))&&(t=io(e,t,!0),!t))){var r=qn&&!i&&ga(e,t.from,t.to);if(r)for(var n=r.length-1;n>=0;--n)no(e,{from:r[n].from,to:r[n].to,text:n?[""]:t.text,origin:t.origin});else no(e,t)}}function no(e,t){if(!(t.text.length==1&&t.text[0]==""&&M(t.from,t.to)==0)){var i=sn(e,t);Yl(e,t,i,e.cm?e.cm.curOp.id:NaN),ur(e,t,i,Pi(e,t));var r=[];Ze(e,function(n,l){!l&&$(r,n.history)==-1&&(so(n.history,t),r.push(n.history)),ur(n,t,null,Pi(n,t))})}}function ri(e,t,i){var r=e.cm&&e.cm.state.suppressEdits;if(!(r&&!i)){for(var n=e.history,l,o=e.sel,a=t=="undo"?n.done:n.undone,s=t=="undo"?n.undone:n.done,u=0;u<a.length&&(l=a[u],!(i?l.ranges&&!l.equals(e.sel):!l.ranges));u++);if(u!=a.length){for(n.lastOrigin=n.lastSelOrigin=null;;)if(l=a.pop(),l.ranges){if(Vr(l,s),i&&!l.equals(e.sel)){ee(e,l,{clearRedo:!1});return}o=l}else if(r){a.push(l);return}else break;var f=[];Vr(o,s),s.push({changes:f,generation:n.generation}),n.generation=l.generation||++n.maxGeneration;for(var h=xe(e,"beforeChange")||e.cm&&xe(e.cm,"beforeChange"),d=function(v){var y=l.changes[v];if(y.origin=t,h&&!io(e,y,!1))return a.length=0,{};f.push(hn(e,y));var m=v?sn(e,y):W(a);ur(e,y,m,Zl(e,y)),!v&&e.cm&&e.cm.scrollIntoView({from:y.from,to:qe(y)});var x=[];Ze(e,function(b,C){!C&&$(x,b.history)==-1&&(so(b.history,y),x.push(b.history)),ur(b,y,null,Zl(b,y))})},c=l.changes.length-1;c>=0;--c){var p=d(c);if(p)return p.v}}}}function lo(e,t){if(t!=0&&(e.first+=t,e.sel=new ge(Nr(e.sel.ranges,function(n){return new O(g(n.anchor.line+t,n.anchor.ch),g(n.head.line+t,n.head.ch))}),e.sel.primIndex),e.cm)){ae(e.cm,e.first,e.first-t,t);for(var i=e.cm.display,r=i.viewFrom;r<i.viewTo;r++)_e(e.cm,r,"gutter")}}function ur(e,t,i,r){if(e.cm&&!e.cm.curOp)return Z(e.cm,ur)(e,t,i,r);if(t.to.line<e.first){lo(e,t.text.length-1-(t.to.line-t.from.line));return}if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var n=t.text.length-1-(e.first-t.from.line);lo(e,n),t={from:g(e.first,0),to:g(t.to.line+n,t.to.ch),text:[W(t.text)],origin:t.origin}}var l=e.lastLine();t.to.line>l&&(t={from:t.from,to:g(l,w(e,l).text.length),text:[t.text[0]],origin:t.origin}),t.removed=nt(e,t.from,t.to),i||(i=sn(e,t)),e.cm?xs(e.cm,t,r):fn(e,t,r),ei(e,i,ke),e.cantEdit&&ti(e,g(e.firstLine(),0))&&(e.cantEdit=!1)}}function xs(e,t,i){var r=e.doc,n=e.display,l=t.from,o=t.to,a=!1,s=l.line;e.options.lineWrapping||(s=H(De(w(r,l.line))),r.iter(s,o.line+1,function(c){if(c==n.maxLine)return a=!0,!0})),r.sel.contains(t.from,t.to)>-1&&Fn(e),fn(r,t,i,Sl(e)),e.options.lineWrapping||(r.iter(s,l.line+t.text.length,function(c){var p=Br(c);p>n.maxLineLength&&(n.maxLine=c,n.maxLineLength=p,n.maxLineChanged=!0,a=!1)}),a&&(e.curOp.updateMaxLine=!0)),ua(r,l.line),or(e,400);var u=t.text.length-(o.line-l.line)-1;t.full?ae(e):l.line==o.line&&t.text.length==1&&!Ul(e.doc,t)?_e(e,l.line,"text"):ae(e,l.line,o.line+1,u);var f=xe(e,"changes"),h=xe(e,"change");if(h||f){var d={from:l,to:o,text:t.text,removed:t.removed,origin:t.origin};h&&q(e,"change",e,d),f&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(d)}e.display.selForContextMenu=null}function Wt(e,t,i,r,n){var l;r||(r=i),M(r,i)<0&&(l=[r,i],i=l[0],r=l[1]),typeof t=="string"&&(t=e.splitLines(t)),Ot(e,{from:i,to:r,text:t,origin:n})}function oo(e,t,i,r){i<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function ao(e,t,i,r){for(var n=0;n<e.length;++n){var l=e[n],o=!0;if(l.ranges){l.copied||(l=e[n]=l.deepCopy(),l.copied=!0);for(var a=0;a<l.ranges.length;a++)oo(l.ranges[a].anchor,t,i,r),oo(l.ranges[a].head,t,i,r);continue}for(var s=0;s<l.changes.length;++s){var u=l.changes[s];if(i<u.from.line)u.from=g(u.from.line+r,u.from.ch),u.to=g(u.to.line+r,u.to.ch);else if(t<=u.to.line){o=!1;break}}o||(e.splice(0,n+1),n=0)}}function so(e,t){var i=t.from.line,r=t.to.line,n=t.text.length-(r-i)-1;ao(e.done,i,r,n),ao(e.undone,i,r,n)}function fr(e,t,i,r){var n=t,l=t;return typeof t=="number"?l=w(e,Rn(e,t)):n=H(t),n==null?null:(r(l,n)&&e.cm&&_e(e.cm,n,i),l)}function hr(e){this.lines=e,this.parent=null;for(var t=0,i=0;i<e.length;++i)e[i].parent=this,t+=e[i].height;this.height=t}hr.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var i=e,r=e+t;i<r;++i){var n=this.lines[i];this.height-=n.height,Ca(n),q(n,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,i){this.height+=i,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,i){for(var r=e+t;e<r;++e)if(i(this.lines[e]))return!0}};function cr(e){this.children=e;for(var t=0,i=0,r=0;r<e.length;++r){var n=e[r];t+=n.chunkSize(),i+=n.height,n.parent=this}this.size=t,this.height=i,this.parent=null}cr.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var i=0;i<this.children.length;++i){var r=this.children[i],n=r.chunkSize();if(e<n){var l=Math.min(t,n-e),o=r.height;if(r.removeInner(e,l),this.height-=o-r.height,n==l&&(this.children.splice(i--,1),r.parent=null),(t-=l)==0)break;e=0}else e-=n}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof hr))){var a=[];this.collapse(a),this.children=[new hr(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,i){this.size+=t.length,this.height+=i;for(var r=0;r<this.children.length;++r){var n=this.children[r],l=n.chunkSize();if(e<=l){if(n.insertInner(e,t,i),n.lines&&n.lines.length>50){for(var o=n.lines.length%25+25,a=o;a<n.lines.length;){var s=new hr(n.lines.slice(a,a+=25));n.height-=s.height,this.children.splice(++r,0,s),s.parent=this}n.lines=n.lines.slice(0,o),this.maybeSpill()}break}e-=l}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),i=new cr(t);if(e.parent){e.size-=i.size,e.height-=i.height;var n=$(e.parent.children,e);e.parent.children.splice(n+1,0,i)}else{var r=new cr(e.children);r.parent=e,e.children=[r,i],e=r}i.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,i){for(var r=0;r<this.children.length;++r){var n=this.children[r],l=n.chunkSize();if(e<l){var o=Math.min(t,l-e);if(n.iterN(e,o,i))return!0;if((t-=o)==0)break;e=0}else e-=l}}};var dr=function(e,t,i){if(i)for(var r in i)i.hasOwnProperty(r)&&(this[r]=i[r]);this.doc=e,this.node=t};dr.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,i=this.line,r=H(i);if(!(r==null||!t)){for(var n=0;n<t.length;++n)t[n]==this&&t.splice(n--,1);t.length||(i.widgets=null);var l=$t(this);Te(i,Math.max(0,i.height-l)),e&&(ce(e,function(){uo(e,i,-l),_e(e,r,"widget")}),q(e,"lineWidgetCleared",e,this,r))}},dr.prototype.changed=function(){var e=this,t=this.height,i=this.doc.cm,r=this.line;this.height=null;var n=$t(this)-t;n&&(Ke(this.doc,r)||Te(r,r.height+n),i&&ce(i,function(){i.curOp.forceUpdate=!0,uo(i,r,n),q(i,"lineWidgetChanged",i,e,H(r))}))},mt(dr);function uo(e,t,i){Pe(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&tn(e,i)}function Cs(e,t,i,r){var n=new dr(e,i,r),l=e.cm;return l&&n.noHScroll&&(l.display.alignWidgets=!0),fr(e,t,"widget",function(o){var a=o.widgets||(o.widgets=[]);if(n.insertAt==null?a.push(n):a.splice(Math.min(a.length,Math.max(0,n.insertAt)),0,n),n.line=o,l&&!Ke(e,o)){var s=Pe(o)<e.scrollTop;Te(o,o.height+$t(n)),s&&tn(l,n.height),l.curOp.forceUpdate=!0}return!0}),l&&q(l,"lineWidgetAdded",l,n,typeof t=="number"?t:H(t)),n}var fo=0,Qe=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++fo};Qe.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&ct(e),xe(this,"clear")){var i=this.find();i&&q(this,"clear",i.from,i.to)}for(var r=null,n=null,l=0;l<this.lines.length;++l){var o=this.lines[l],a=Jt(o.markedSpans,this);e&&!this.collapsed?_e(e,H(o),"text"):e&&(a.to!=null&&(n=H(o)),a.from!=null&&(r=H(o))),o.markedSpans=ca(o.markedSpans,a),a.from==null&&this.collapsed&&!Ke(this.doc,o)&&e&&Te(o,Lt(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=De(this.lines[s]),f=Br(u);f>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=f,e.display.maxLineChanged=!0)}r!=null&&e&&this.collapsed&&ae(e,r,n+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&$l(e.doc)),e&&q(e,"markerCleared",e,this,r,n),t&&dt(e),this.parent&&this.parent.clear()}},Qe.prototype.find=function(e,t){e==null&&this.type=="bookmark"&&(e=1);for(var i,r,n=0;n<this.lines.length;++n){var l=this.lines[n],o=Jt(l.markedSpans,this);if(o.from!=null&&(i=g(t?l:H(l),o.from),e==-1))return i;if(o.to!=null&&(r=g(t?l:H(l),o.to),e==1))return r}return i&&{from:i,to:r}},Qe.prototype.changed=function(){var e=this,t=this.find(-1,!0),i=this,r=this.doc.cm;!t||!r||ce(r,function(){var n=t.line,l=H(t.line),o=Ki(r,l);if(o&&(vl(o),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Ke(i.doc,n)&&i.height!=null){var a=i.height;i.height=null;var s=$t(i)-a;s&&Te(n,n.height+s)}q(r,"markerChanged",r,e)})},Qe.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(!t.maybeHiddenMarkers||$(t.maybeHiddenMarkers,this)==-1)&&(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},Qe.prototype.detachLine=function(e){if(this.lines.splice($(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},mt(Qe);function Ht(e,t,i,r,n){if(r&&r.shared)return ws(e,t,i,r,n);if(e.cm&&!e.cm.curOp)return Z(e.cm,Ht)(e,t,i,r,n);var l=new Qe(e,n),o=M(t,i);if(r&&rt(r,l,!1),o>0||o==0&&l.clearWhenEmpty!==!1)return l;if(l.replacedWith&&(l.collapsed=!0,l.widgetNode=yt("span",[l.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||l.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(l.widgetNode.insertLeft=!0)),l.collapsed){if($n(e,t.line,t,i,l)||t.line!=i.line&&$n(e,i.line,t,i,l))throw new Error("Inserting collapsed marker partially overlapping an existing one");ha()}l.addToHistory&&Yl(e,{from:t,to:i,origin:"markText"},e.sel,NaN);var a=t.line,s=e.cm,u;if(e.iter(a,i.line+1,function(h){s&&l.collapsed&&!s.options.lineWrapping&&De(h)==s.display.maxLine&&(u=!0),l.collapsed&&a!=t.line&&Te(h,0),da(h,new Pr(l,a==t.line?t.ch:null,a==i.line?i.ch:null),e.cm&&e.cm.curOp),++a}),l.collapsed&&e.iter(t.line,i.line+1,function(h){Ke(e,h)&&Te(h,0)}),l.clearOnEnter&&T(l,"beforeCursorEnter",function(){return l.clear()}),l.readOnly&&(fa(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),l.collapsed&&(l.id=++fo,l.atomic=!0),s){if(u&&(s.curOp.updateMaxLine=!0),l.collapsed)ae(s,t.line,i.line+1);else if(l.className||l.startStyle||l.endStyle||l.css||l.attributes||l.title)for(var f=t.line;f<=i.line;f++)_e(s,f,"text");l.atomic&&$l(s.doc),q(s,"markerAdded",s,l)}return l}var pr=function(e,t){this.markers=e,this.primary=t;for(var i=0;i<e.length;++i)e[i].parent=this};pr.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();q(this,"clear")}},pr.prototype.find=function(e,t){return this.primary.find(e,t)},mt(pr);function ws(e,t,i,r,n){r=rt(r),r.shared=!1;var l=[Ht(e,t,i,r,n)],o=l[0],a=r.widgetNode;return Ze(e,function(s){a&&(r.widgetNode=a.cloneNode(!0)),l.push(Ht(s,D(s,t),D(s,i),r,n));for(var u=0;u<s.linked.length;++u)if(s.linked[u].isParent)return;o=W(l)}),new pr(l,o)}function ho(e){return e.findMarks(g(e.first,0),e.clipPos(g(e.lastLine())),function(t){return t.parent})}function Ss(e,t){for(var i=0;i<t.length;i++){var r=t[i],n=r.find(),l=e.clipPos(n.from),o=e.clipPos(n.to);if(M(l,o)){var a=Ht(e,l,o,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}function Ls(e){for(var t=function(r){var n=e[r],l=[n.primary.doc];Ze(n.primary.doc,function(s){return l.push(s)});for(var o=0;o<n.markers.length;o++){var a=n.markers[o];$(l,a.doc)==-1&&(a.parent=null,n.markers.splice(o--,1))}},i=0;i<e.length;i++)t(i)}var ks=0,se=function(e,t,i,r,n){if(!(this instanceof se))return new se(e,t,i,r,n);i==null&&(i=0),cr.call(this,[new hr([new Ct("",null)])]),this.first=i,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=i;var l=g(i,0);this.sel=Ye(l),this.history=new jr(null),this.id=++ks,this.modeOption=t,this.lineSep=r,this.direction=n=="rtl"?"rtl":"ltr",this.extend=!1,typeof e=="string"&&(e=this.splitLines(e)),fn(this,{from:l,to:l,text:e}),ee(this,Ye(l),ke)};se.prototype=An(cr.prototype,{constructor:se,iter:function(e,t,i){i?this.iterN(e-this.first,t-e,i):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var i=0,r=0;r<t.length;++r)i+=t[r].height;this.insertInner(e-this.first,t,i)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Ni(this,this.first,this.first+this.size);return e===!1?t:t.join(e||this.lineSeparator())},setValue:Q(function(e){var t=g(this.first,0),i=this.first+this.size-1;Ot(this,{from:t,to:g(i,w(this,i).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&rr(this.cm,0,0),ee(this,Ye(t),ke)}),replaceRange:function(e,t,i,r){t=D(this,t),i=i?D(this,i):t,Wt(this,e,t,i,r)},getRange:function(e,t,i){var r=nt(this,D(this,e),D(this,t));return i===!1?r:i===""?r.join(""):r.join(i||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(Zt(this,e))return w(this,e)},getLineNumber:function(e){return H(e)},getLineHandleVisualStart:function(e){return typeof e=="number"&&(e=w(this,e)),De(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return D(this,e)},getCursor:function(e){var t=this.sel.primary(),i;return e==null||e=="head"?i=t.head:e=="anchor"?i=t.anchor:e=="end"||e=="to"||e===!1?i=t.to():i=t.from(),i},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Q(function(e,t,i){Jl(this,D(this,typeof e=="number"?g(e,t||0):e),null,i)}),setSelection:Q(function(e,t,i){Jl(this,D(this,e),D(this,t||e),i)}),extendSelection:Q(function(e,t,i){$r(this,D(this,e),t&&D(this,t),i)}),extendSelections:Q(function(e,t){Ql(this,Bn(this,e),t)}),extendSelectionsBy:Q(function(e,t){var i=Nr(this.sel.ranges,e);Ql(this,Bn(this,i),t)}),setSelections:Q(function(e,t,i){if(e.length){for(var r=[],n=0;n<e.length;n++)r[n]=new O(D(this,e[n].anchor),D(this,e[n].head||e[n].anchor));t==null&&(t=Math.min(e.length-1,this.sel.primIndex)),ee(this,Se(this.cm,r,t),i)}}),addSelection:Q(function(e,t,i){var r=this.sel.ranges.slice(0);r.push(new O(D(this,e),D(this,t||e))),ee(this,Se(this.cm,r,r.length-1),i)}),getSelection:function(e){for(var t=this.sel.ranges,i,r=0;r<t.length;r++){var n=nt(this,t[r].from(),t[r].to());i=i?i.concat(n):n}return e===!1?i:i.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],i=this.sel.ranges,r=0;r<i.length;r++){var n=nt(this,i[r].from(),i[r].to());e!==!1&&(n=n.join(e||this.lineSeparator())),t[r]=n}return t},replaceSelection:function(e,t,i){for(var r=[],n=0;n<this.sel.ranges.length;n++)r[n]=e;this.replaceSelections(r,t,i||"+input")},replaceSelections:Q(function(e,t,i){for(var r=[],n=this.sel,l=0;l<n.ranges.length;l++){var o=n.ranges[l];r[l]={from:o.from(),to:o.to(),text:this.splitLines(e[l]),origin:i}}for(var a=t&&t!="end"&&cs(this,r,t),s=r.length-1;s>=0;s--)Ot(this,r[s]);a?jl(this,a):this.cm&&Mt(this.cm)}),undo:Q(function(){ri(this,"undo")}),redo:Q(function(){ri(this,"redo")}),undoSelection:Q(function(){ri(this,"undo",!0)}),redoSelection:Q(function(){ri(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,i=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var n=0;n<e.undone.length;n++)e.undone[n].ranges||++i;return{undo:t,redo:i}},clearHistory:function(){var e=this;this.history=new jr(this.history),Ze(this,function(t){return t.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Nt(this.history.done),undone:Nt(this.history.undone)}},setHistory:function(e){var t=this.history=new jr(this.history);t.done=Nt(e.done.slice(0),null,!0),t.undone=Nt(e.undone.slice(0),null,!0)},setGutterMarker:Q(function(e,t,i){return fr(this,e,"gutter",function(r){var n=r.gutterMarkers||(r.gutterMarkers={});return n[t]=i,!i&&On(n)&&(r.gutterMarkers=null),!0})}),clearGutter:Q(function(e){var t=this;this.iter(function(i){i.gutterMarkers&&i.gutterMarkers[e]&&fr(t,i,"gutter",function(){return i.gutterMarkers[e]=null,On(i.gutterMarkers)&&(i.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if(typeof e=="number"){if(!Zt(this,e)||(t=e,e=w(this,e),!e))return null}else if(t=H(e),t==null)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Q(function(e,t,i){return fr(this,e,t=="gutter"?"gutter":"class",function(r){var n=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass";if(!r[n])r[n]=i;else{if(gt(i).test(r[n]))return!1;r[n]+=" "+i}return!0})}),removeLineClass:Q(function(e,t,i){return fr(this,e,t=="gutter"?"gutter":"class",function(r){var n=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass",l=r[n];if(l)if(i==null)r[n]=null;else{var o=l.match(gt(i));if(!o)return!1;var a=o.index+o[0].length;r[n]=l.slice(0,o.index)+(!o.index||a==l.length?"":" ")+l.slice(a)||null}else return!1;return!0})}),addLineWidget:Q(function(e,t,i){return Cs(this,e,t,i)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,i){return Ht(this,D(this,e),D(this,t),i,i&&i.type||"range")},setBookmark:function(e,t){var i={replacedWith:t&&(t.nodeType==null?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=D(this,e),Ht(this,e,e,i,"bookmark")},findMarksAt:function(e){e=D(this,e);var t=[],i=w(this,e.line).markedSpans;if(i)for(var r=0;r<i.length;++r){var n=i[r];(n.from==null||n.from<=e.ch)&&(n.to==null||n.to>=e.ch)&&t.push(n.marker.parent||n.marker)}return t},findMarks:function(e,t,i){e=D(this,e),t=D(this,t);var r=[],n=e.line;return this.iter(e.line,t.line+1,function(l){var o=l.markedSpans;if(o)for(var a=0;a<o.length;a++){var s=o[a];!(s.to!=null&&n==e.line&&e.ch>=s.to||s.from==null&&n!=e.line||s.from!=null&&n==t.line&&s.from>=t.ch)&&(!i||i(s.marker))&&r.push(s.marker.parent||s.marker)}++n}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var i=t.markedSpans;if(i)for(var r=0;r<i.length;++r)i[r].from!=null&&e.push(i[r].marker)}),e},posFromIndex:function(e){var t,i=this.first,r=this.lineSeparator().length;return this.iter(function(n){var l=n.text.length+r;if(l>e)return t=e,!0;e-=l,++i}),D(this,g(i,t))},indexFromPos:function(e){e=D(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var i=this.lineSeparator().length;return this.iter(this.first,e.line,function(r){t+=r.text.length+i}),t},copy:function(e){var t=new se(Ni(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,i=this.first+this.size;e.from!=null&&e.from>t&&(t=e.from),e.to!=null&&e.to<i&&(i=e.to);var r=new se(Ni(this,t,i),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Ss(r,ho(this)),r},unlinkDoc:function(e){if(e instanceof I&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var i=this.linked[t];if(i.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ls(ho(this));break}}if(e.history==this.history){var r=[e.id];Ze(e,function(n){return r.push(n.id)},!0),e.history=new jr(null),e.history.done=Nt(this.history.done,r),e.history.undone=Nt(this.history.undone,r)}},iterLinkedDocs:function(e){Ze(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Li(e)},lineSeparator:function(){return this.lineSep||`
`},setDirection:Q(function(e){e!="rtl"&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(t){return t.order=null}),this.cm&&ds(this.cm))})}),se.prototype.eachLine=se.prototype.iter;var co=0;function Ts(e){var t=this;if(po(t),!(Y(t,e)||Ee(t.display,e))){oe(e),A&&(co=+new Date);var i=st(t,e,!0),r=e.dataTransfer.files;if(!(!i||t.isReadOnly()))if(r&&r.length&&window.FileReader&&window.File)for(var n=r.length,l=Array(n),o=0,a=function(){++o==n&&Z(t,function(){i=D(t.doc,i);var c={from:i,to:i,text:t.doc.splitLines(l.filter(function(p){return p!=null}).join(t.doc.lineSeparator())),origin:"paste"};Ot(t.doc,c),jl(t.doc,Ye(D(t.doc,i),D(t.doc,qe(c))))})()},s=function(c,p){if(t.options.allowDropFileTypes&&$(t.options.allowDropFileTypes,c.type)==-1){a();return}var v=new FileReader;v.onerror=function(){return a()},v.onload=function(){var y=v.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(y)){a();return}l[p]=y,a()},v.readAsText(c)},u=0;u<r.length;u++)s(r[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(i)>-1){t.state.draggingText(e),setTimeout(function(){return t.display.input.focus()},20);return}try{var f=e.dataTransfer.getData("Text");if(f){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),ei(t.doc,Ye(i,i)),h)for(var d=0;d<h.length;++d)Wt(t.doc,"",h[d].anchor,h[d].head,"drag");t.replaceSelection(f,"around","paste"),t.display.input.focus()}}catch{}}}}function Ms(e,t){if(A&&(!e.state.draggingText||+new Date-co<100)){qt(t);return}if(!(Y(e,t)||Ee(e.display,t))&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!kr)){var i=k("img",null,null,"position: fixed; left: 0; top: 0;");i.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",Ce&&(i.width=i.height=1,e.display.wrapper.appendChild(i),i._top=i.offsetTop),t.dataTransfer.setDragImage(i,0,0),Ce&&i.parentNode.removeChild(i)}}function Ds(e,t){var i=st(e,t);if(i){var r=document.createDocumentFragment();Tl(e,i,r),e.display.dragCursor||(e.display.dragCursor=k("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),pe(e.display.dragCursor,r)}}function po(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function vo(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),i=[],r=0;r<t.length;r++){var n=t[r].CodeMirror;n&&i.push(n)}i.length&&i[0].operation(function(){for(var l=0;l<i.length;l++)e(i[l])})}}var go=!1;function Ns(){go||(As(),go=!0)}function As(){var e;T(window,"resize",function(){e==null&&(e=setTimeout(function(){e=null,vo(Os)},100))}),T(window,"blur",function(){return vo(Tt)})}function Os(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Je={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},vr=0;vr<10;vr++)Je[vr+48]=Je[vr+96]=String(vr);for(var ii=65;ii<=90;ii++)Je[ii]=String.fromCharCode(ii);for(var gr=1;gr<=12;gr++)Je[gr+111]=Je[gr+63235]="F"+gr;var Re={};Re.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Re.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Re.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Re.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Re.default=ye?Re.macDefault:Re.pcDefault;function Ws(e){var t=e.split(/-(?!$)/);e=t[t.length-1];for(var i,r,n,l,o=0;o<t.length-1;o++){var a=t[o];if(/^(cmd|meta|m)$/i.test(a))l=!0;else if(/^a(lt)?$/i.test(a))i=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else if(/^s(hift)?$/i.test(a))n=!0;else throw new Error("Unrecognized modifier name: "+a)}return i&&(e="Alt-"+e),r&&(e="Ctrl-"+e),l&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Hs(e){var t={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(/^(name|fallthrough|(de|at)tach)$/.test(i))continue;if(r=="..."){delete e[i];continue}for(var n=Nr(i.split(" "),Ws),l=0;l<n.length;l++){var o=void 0,a=void 0;l==n.length-1?(a=n.join(" "),o=r):(a=n.slice(0,l+1).join(" "),o="...");var s=t[a];if(!s)t[a]=o;else if(s!=o)throw new Error("Inconsistent bindings for "+a)}delete e[i]}for(var u in t)e[u]=t[u];return e}function Ft(e,t,i,r){t=ni(t);var n=t.call?t.call(e,r):t[e];if(n===!1)return"nothing";if(n==="...")return"multi";if(n!=null&&i(n))return"handled";if(t.fallthrough){if(Object.prototype.toString.call(t.fallthrough)!="[object Array]")return Ft(e,t.fallthrough,i,r);for(var l=0;l<t.fallthrough.length;l++){var o=Ft(e,t.fallthrough[l],i,r);if(o)return o}}}function yo(e){var t=typeof e=="string"?e:Je[e.keyCode];return t=="Ctrl"||t=="Alt"||t=="Shift"||t=="Mod"}function mo(e,t,i){var r=e;return t.altKey&&r!="Alt"&&(e="Alt-"+e),(Mn?t.metaKey:t.ctrlKey)&&r!="Ctrl"&&(e="Ctrl-"+e),(Mn?t.ctrlKey:t.metaKey)&&r!="Mod"&&(e="Cmd-"+e),!i&&t.shiftKey&&r!="Shift"&&(e="Shift-"+e),e}function bo(e,t){if(Ce&&e.keyCode==34&&e.char)return!1;var i=Je[e.keyCode];return i==null||e.altGraphKey?!1:(e.keyCode==3&&e.code&&(i=e.code),mo(i,e,t))}function ni(e){return typeof e=="string"?Re[e]:e}function Pt(e,t){for(var i=e.doc.sel.ranges,r=[],n=0;n<i.length;n++){for(var l=t(i[n]);r.length&&M(l.from,W(r).to)<=0;){var o=r.pop();if(M(o.from,l.from)<0){l.from=o.from;break}}r.push(l)}ce(e,function(){for(var a=r.length-1;a>=0;a--)Wt(e.doc,"",r[a].from,r[a].to,"+delete");Mt(e)})}function pn(e,t,i){var r=Wn(e.text,t+i,i);return r<0||r>e.text.length?null:r}function vn(e,t,i){var r=pn(e,t.ch,i);return r==null?null:new g(t.line,r,i<0?"after":"before")}function gn(e,t,i,r,n){if(e){t.doc.direction=="rtl"&&(n=-n);var l=He(i,t.doc.direction);if(l){var o=n<0?W(l):l[0],a=n<0==(o.level==1),s=a?"after":"before",u;if(o.level>0||t.doc.direction=="rtl"){var f=St(t,i);u=n<0?i.text.length-1:0;var h=Ae(t,f,u).top;u=_t(function(d){return Ae(t,f,d).top==h},n<0==(o.level==1)?o.from:o.to-1,u),s=="before"&&(u=pn(i,u,1))}else u=n<0?o.to:o.from;return new g(r,u,s)}}return new g(r,n<0?i.text.length:0,n<0?"before":"after")}function Fs(e,t,i,r){var n=He(t,e.doc.direction);if(!n)return vn(t,i,r);i.ch>=t.text.length?(i.ch=t.text.length,i.sticky="before"):i.ch<=0&&(i.ch=0,i.sticky="after");var l=Yt(n,i.ch,i.sticky),o=n[l];if(e.doc.direction=="ltr"&&o.level%2==0&&(r>0?o.to>i.ch:o.from<i.ch))return vn(t,i,r);var a=function(m,x){return pn(t,m instanceof g?m.ch:m,x)},s,u=function(m){return e.options.lineWrapping?(s=s||St(e,t),wl(e,t,s,m)):{begin:0,end:t.text.length}},f=u(i.sticky=="before"?a(i,-1):i.ch);if(e.doc.direction=="rtl"||o.level==1){var h=o.level==1==r<0,d=a(i,h?1:-1);if(d!=null&&(h?d<=o.to&&d<=f.end:d>=o.from&&d>=f.begin)){var c=h?"before":"after";return new g(i.line,d,c)}}var p=function(m,x,b){for(var C=function(P,J){return J?new g(i.line,a(P,1),"before"):new g(i.line,P,"after")};m>=0&&m<n.length;m+=x){var L=n[m],S=x>0==(L.level!=1),N=S?b.begin:a(b.end,-1);if(L.from<=N&&N<L.to||(N=S?L.from:a(L.to,-1),b.begin<=N&&N<b.end))return C(N,S)}},v=p(l+r,r,f);if(v)return v;var y=r>0?f.end:a(f.begin,-1);return y!=null&&!(r>0&&y==t.text.length)&&(v=p(r>0?0:n.length-1,r,u(y)),v)?v:null}var yr={selectAll:ro,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),ke)},killLine:function(e){return Pt(e,function(t){if(t.empty()){var i=w(e.doc,t.head.line).text.length;return t.head.ch==i&&t.head.line<e.lastLine()?{from:t.head,to:g(t.head.line+1,0)}:{from:t.head,to:g(t.head.line,i)}}else return{from:t.from(),to:t.to()}})},deleteLine:function(e){return Pt(e,function(t){return{from:g(t.from().line,0),to:D(e.doc,g(t.to().line+1,0))}})},delLineLeft:function(e){return Pt(e,function(t){return{from:g(t.from().line,0),to:t.from()}})},delWrappedLineLeft:function(e){return Pt(e,function(t){var i=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:i},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return Pt(e,function(t){var i=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:i},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(g(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(g(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return xo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Co(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return Ps(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:i},"div")},Kt)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:i},"div")},Kt)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:i},"div");return r.ch<e.getLine(r.line).search(/\S/)?Co(e,t.head):r},Kt)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("	")},insertSoftTab:function(e){for(var t=[],i=e.listSelections(),r=e.options.tabSize,n=0;n<i.length;n++){var l=i[n].from(),o=be(e.getLine(l.line),l.ch,r);t.push(gi(r-o%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return ce(e,function(){for(var t=e.listSelections(),i=[],r=0;r<t.length;r++)if(t[r].empty()){var n=t[r].head,l=w(e.doc,n.line).text;if(l){if(n.ch==l.length&&(n=new g(n.line,n.ch-1)),n.ch>0)n=new g(n.line,n.ch+1),e.replaceRange(l.charAt(n.ch-1)+l.charAt(n.ch-2),g(n.line,n.ch-2),n,"+transpose");else if(n.line>e.doc.first){var o=w(e.doc,n.line-1).text;o&&(n=new g(n.line,1),e.replaceRange(l.charAt(0)+e.doc.lineSeparator()+o.charAt(o.length-1),g(n.line-1,o.length-1),n,"+transpose"))}}i.push(new O(n,n))}e.setSelections(i)})},newlineAndIndent:function(e){return ce(e,function(){for(var t=e.listSelections(),i=t.length-1;i>=0;i--)e.replaceRange(e.doc.lineSeparator(),t[i].anchor,t[i].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Mt(e)})},openLine:function(e){return e.replaceSelection(`
`,"start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function xo(e,t){var i=w(e.doc,t),r=De(i);return r!=i&&(t=H(r)),gn(!0,e,r,t,1)}function Ps(e,t){var i=w(e.doc,t),r=ma(i);return r!=i&&(t=H(r)),gn(!0,e,i,t,-1)}function Co(e,t){var i=xo(e,t.line),r=w(e.doc,i.line),n=He(r,e.doc.direction);if(!n||n[0].level==0){var l=Math.max(i.ch,r.text.search(/\S/)),o=t.line==i.line&&t.ch<=l&&t.ch;return g(i.line,o?0:l,i.sticky)}return i}function li(e,t,i){if(typeof t=="string"&&(t=yr[t],!t))return!1;e.display.input.ensurePolled();var r=e.display.shift,n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),i&&(e.display.shift=!1),n=t(e)!=Mr}finally{e.display.shift=r,e.state.suppressEdits=!1}return n}function Es(e,t,i){for(var r=0;r<e.state.keyMaps.length;r++){var n=Ft(t,e.state.keyMaps[r],i,e);if(n)return n}return e.options.extraKeys&&Ft(t,e.options.extraKeys,i,e)||Ft(t,e.options.keyMap,i,e)}var Is=new Ue;function mr(e,t,i,r){var n=e.state.keySeq;if(n){if(yo(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:Is.set(50,function(){e.state.keySeq==n&&(e.state.keySeq=null,e.display.input.reset())}),wo(e,n+" "+t,i,r))return!0}return wo(e,t,i,r)}function wo(e,t,i,r){var n=Es(e,t,r);return n=="multi"&&(e.state.keySeq=t),n=="handled"&&q(e,"keyHandled",e,t,i),(n=="handled"||n=="multi")&&(oe(i),ji(e)),!!n}function So(e,t){var i=bo(t,!0);return i?t.shiftKey&&!e.state.keySeq?mr(e,"Shift-"+i,t,function(r){return li(e,r,!0)})||mr(e,i,t,function(r){if(typeof r=="string"?/^go[A-Z]/.test(r):r.motion)return li(e,r)}):mr(e,i,t,function(r){return li(e,r)}):!1}function Rs(e,t,i){return mr(e,"'"+i+"'",t,function(r){return li(e,r,!0)})}var yn=null;function Lo(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&(t.curOp.focus=me(),!Y(t,e))){A&&E<11&&e.keyCode==27&&(e.returnValue=!1);var i=e.keyCode;t.display.shift=i==16||e.shiftKey;var r=So(t,e);Ce&&(yn=r?i:null,!r&&i==88&&!ra&&(ye?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),We&&!ye&&!r&&i==46&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),i==18&&!/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)&&Bs(t)}}function Bs(e){var t=e.display.lineDiv;tt(t,"CodeMirror-crosshair");function i(r){(r.keyCode==18||!r.altKey)&&($e(t,"CodeMirror-crosshair"),ve(document,"keyup",i),ve(document,"mouseover",i))}T(document,"keyup",i),T(document,"mouseover",i)}function ko(e){e.keyCode==16&&(this.doc.sel.shift=!1),Y(this,e)}function To(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&!(Ee(t.display,e)||Y(t,e)||e.ctrlKey&&!e.altKey||ye&&e.metaKey)){var i=e.keyCode,r=e.charCode;if(Ce&&i==yn){yn=null,oe(e);return}if(!(Ce&&(!e.which||e.which<10)&&So(t,e))){var n=String.fromCharCode(r??i);n!="\b"&&(Rs(t,e,n)||t.display.input.onKeyPress(e))}}}var zs=400,mn=function(e,t,i){this.time=e,this.pos=t,this.button=i};mn.prototype.compare=function(e,t,i){return this.time+zs>e&&M(t,this.pos)==0&&i==this.button};var br,xr;function Gs(e,t){var i=+new Date;return xr&&xr.compare(i,e,t)?(br=xr=null,"triple"):br&&br.compare(i,e,t)?(xr=new mn(i,e,t),br=null,"double"):(br=new mn(i,e,t),xr=null,"single")}function Mo(e){var t=this,i=t.display;if(!(Y(t,e)||i.activeTouch&&i.input.supportsTouch())){if(i.input.ensurePolled(),i.shift=e.shiftKey,Ee(i,e)){ie||(i.scroller.draggable=!1,setTimeout(function(){return i.scroller.draggable=!0},100));return}if(!bn(t,e)){var r=st(t,e),n=En(e),l=r?Gs(r,n):"single";window.focus(),n==1&&t.state.selectingText&&t.state.selectingText(e),!(r&&Us(t,n,r,l,e))&&(n==1?r?_s(t,r,l,e):Ci(e)==i.scroller&&oe(e):n==2?(r&&$r(t.doc,r),setTimeout(function(){return i.input.focus()},20)):n==3&&(hi?t.display.input.onContextMenu(e):Vi(t)))}}}function Us(e,t,i,r,n){var l="Click";return r=="double"?l="Double"+l:r=="triple"&&(l="Triple"+l),l=(t==1?"Left":t==2?"Middle":"Right")+l,mr(e,mo(l,n),n,function(o){if(typeof o=="string"&&(o=yr[o]),!o)return!1;var a=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),a=o(e,i)!=Mr}finally{e.state.suppressEdits=!1}return a})}function Ks(e,t,i){var r=e.getOption("configureMouse"),n=r?r(e,t,i):{};if(n.unit==null){var l=Xo?i.shiftKey&&i.metaKey:i.altKey;n.unit=l?"rectangle":t=="single"?"char":t=="double"?"word":"line"}return(n.extend==null||e.doc.extend)&&(n.extend=e.doc.extend||i.shiftKey),n.addNew==null&&(n.addNew=ye?i.metaKey:i.ctrlKey),n.moveOnDrag==null&&(n.moveOnDrag=!(ye?i.altKey:i.ctrlKey)),n}function _s(e,t,i,r){A?setTimeout(di(Ml,e),0):e.curOp.focus=me();var n=Ks(e,i,r),l=e.doc.sel,o;e.options.dragDrop&&Vo&&!e.isReadOnly()&&i=="single"&&(o=l.contains(t))>-1&&(M((o=l.ranges[o]).from(),t)<0||t.xRel>0)&&(M(o.to(),t)>0||t.xRel<0)?Xs(e,r,t,n):Ys(e,r,t,n)}function Xs(e,t,i,r){var n=e.display,l=!1,o=Z(e,function(u){ie&&(n.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Vi(e)),ve(n.wrapper.ownerDocument,"mouseup",o),ve(n.wrapper.ownerDocument,"mousemove",a),ve(n.scroller,"dragstart",s),ve(n.scroller,"drop",o),l||(oe(u),r.addNew||$r(e.doc,i,null,null,r.extend),ie&&!kr||A&&E==9?setTimeout(function(){n.wrapper.ownerDocument.body.focus({preventScroll:!0}),n.input.focus()},20):n.input.focus())}),a=function(u){l=l||Math.abs(t.clientX-u.clientX)+Math.abs(t.clientY-u.clientY)>=10},s=function(){return l=!0};ie&&(n.scroller.draggable=!0),e.state.draggingText=o,o.copy=!r.moveOnDrag,T(n.wrapper.ownerDocument,"mouseup",o),T(n.wrapper.ownerDocument,"mousemove",a),T(n.scroller,"dragstart",s),T(n.scroller,"drop",o),e.state.delayingBlurEvent=!0,setTimeout(function(){return n.input.focus()},20),n.scroller.dragDrop&&n.scroller.dragDrop()}function Do(e,t,i){if(i=="char")return new O(t,t);if(i=="word")return e.findWordAt(t);if(i=="line")return new O(g(t.line,0),D(e.doc,g(t.line+1,0)));var r=i(e,t);return new O(r.from,r.to)}function Ys(e,t,i,r){A&&Vi(e);var n=e.display,l=e.doc;oe(t);var o,a,s=l.sel,u=s.ranges;if(r.addNew&&!r.extend?(a=l.sel.contains(i),a>-1?o=u[a]:o=new O(i,i)):(o=l.sel.primary(),a=l.sel.primIndex),r.unit=="rectangle")r.addNew||(o=new O(i,i)),i=st(e,t,!0,!0),a=-1;else{var f=Do(e,i,r.unit);r.extend?o=cn(o,f.anchor,f.head,r.extend):o=f}r.addNew?a==-1?(a=u.length,ee(l,Se(e,u.concat([o]),a),{scroll:!1,origin:"*mouse"})):u.length>1&&u[a].empty()&&r.unit=="char"&&!r.extend?(ee(l,Se(e,u.slice(0,a).concat(u.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=l.sel):dn(l,a,o,pi):(a=0,ee(l,new ge([o],0),pi),s=l.sel);var h=i;function d(b){if(M(h,b)!=0)if(h=b,r.unit=="rectangle"){for(var C=[],L=e.options.tabSize,S=be(w(l,i.line).text,i.ch,L),N=be(w(l,b.line).text,b.ch,L),P=Math.min(S,N),J=Math.max(S,N),R=Math.min(i.line,b.line),de=Math.min(e.lastLine(),Math.max(i.line,b.line));R<=de;R++){var ue=w(l,R).text,K=vi(ue,P,L);P==J?C.push(new O(g(R,K),g(R,K))):ue.length>K&&C.push(new O(g(R,K),g(R,vi(ue,J,L))))}C.length||C.push(new O(i,i)),ee(l,Se(e,s.ranges.slice(0,a).concat(C),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(b)}else{var fe=o,V=Do(e,b,r.unit),X=fe.anchor,_;M(V.anchor,X)>0?(_=V.head,X=Hr(fe.from(),V.anchor)):(_=V.anchor,X=Wr(fe.to(),V.head));var B=s.ranges.slice(0);B[a]=qs(e,new O(D(l,X),_)),ee(l,Se(e,B,a),pi)}}var c=n.wrapper.getBoundingClientRect(),p=0;function v(b){var C=++p,L=st(e,b,!0,r.unit=="rectangle");if(L)if(M(L,h)!=0){e.curOp.focus=me(),d(L);var S=qr(n,l);(L.line>=S.to||L.line<S.from)&&setTimeout(Z(e,function(){p==C&&v(b)}),150)}else{var N=b.clientY<c.top?-20:b.clientY>c.bottom?20:0;N&&setTimeout(Z(e,function(){p==C&&(n.scroller.scrollTop+=N,v(b))}),50)}}function y(b){e.state.selectingText=!1,p=1/0,b&&(oe(b),n.input.focus()),ve(n.wrapper.ownerDocument,"mousemove",m),ve(n.wrapper.ownerDocument,"mouseup",x),l.history.lastSelOrigin=null}var m=Z(e,function(b){b.buttons===0||!En(b)?y(b):v(b)}),x=Z(e,y);e.state.selectingText=x,T(n.wrapper.ownerDocument,"mousemove",m),T(n.wrapper.ownerDocument,"mouseup",x)}function qs(e,t){var i=t.anchor,r=t.head,n=w(e.doc,i.line);if(M(i,r)==0&&i.sticky==r.sticky)return t;var l=He(n);if(!l)return t;var o=Yt(l,i.ch,i.sticky),a=l[o];if(a.from!=i.ch&&a.to!=i.ch)return t;var s=o+(a.from==i.ch==(a.level!=1)?0:1);if(s==0||s==l.length)return t;var u;if(r.line!=i.line)u=(r.line-i.line)*(e.doc.direction=="ltr"?1:-1)>0;else{var f=Yt(l,r.ch,r.sticky),h=f-o||(r.ch-i.ch)*(a.level==1?-1:1);f==s-1||f==s?u=h<0:u=h>0}var d=l[s+(u?-1:0)],c=u==(d.level==1),p=c?d.from:d.to,v=c?"after":"before";return i.ch==p&&i.sticky==v?t:new O(new g(i.line,p,v),r)}function No(e,t,i,r){var n,l;if(t.touches)n=t.touches[0].clientX,l=t.touches[0].clientY;else try{n=t.clientX,l=t.clientY}catch{return!1}if(n>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&oe(t);var o=e.display,a=o.lineDiv.getBoundingClientRect();if(l>a.bottom||!xe(e,i))return xi(t);l-=a.top-o.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var u=o.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=n){var f=lt(e.doc,l),h=e.display.gutterSpecs[s];return G(e,i,e,f,h.className,t),xi(t)}}}function bn(e,t){return No(e,t,"gutterClick",!0)}function Ao(e,t){Ee(e.display,t)||Zs(e,t)||Y(e,t,"contextmenu")||hi||e.display.input.onContextMenu(t)}function Zs(e,t){return xe(e,"gutterContextMenu")?No(e,t,"gutterContextMenu",!1):!1}function Oo(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),er(e)}var Et={toString:function(){return"CodeMirror.Init"}},Wo={},oi={};function Qs(e){var t=e.optionHandlers;function i(r,n,l,o){e.defaults[r]=n,l&&(t[r]=o?function(a,s,u){u!=Et&&l(a,s,u)}:l)}e.defineOption=i,e.Init=Et,i("value","",function(r,n){return r.setValue(n)},!0),i("mode",null,function(r,n){r.doc.modeOption=n,un(r)},!0),i("indentUnit",2,un,!0),i("indentWithTabs",!1),i("smartIndent",!0),i("tabSize",4,function(r){sr(r),er(r),ae(r)},!0),i("lineSeparator",null,function(r,n){if(r.doc.lineSep=n,!!n){var l=[],o=r.doc.first;r.doc.iter(function(s){for(var u=0;;){var f=s.text.indexOf(n,u);if(f==-1)break;u=f+n.length,l.push(g(o,f))}o++});for(var a=l.length-1;a>=0;a--)Wt(r.doc,n,l[a],g(l[a].line,l[a].ch+n.length))}}),i("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(r,n,l){r.state.specialChars=new RegExp(n.source+(n.test("	")?"":"|	"),"g"),l!=Et&&r.refresh()}),i("specialCharPlaceholder",La,function(r){return r.refresh()},!0),i("electricChars",!0),i("inputStyle",Gt?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),i("spellcheck",!1,function(r,n){return r.getInputField().spellcheck=n},!0),i("autocorrect",!1,function(r,n){return r.getInputField().autocorrect=n},!0),i("autocapitalize",!1,function(r,n){return r.getInputField().autocapitalize=n},!0),i("rtlMoveVisually",!Yo),i("wholeLineUpdateBefore",!0),i("theme","default",function(r){Oo(r),ar(r)},!0),i("keyMap","default",function(r,n,l){var o=ni(n),a=l!=Et&&ni(l);a&&a.detach&&a.detach(r,o),o.attach&&o.attach(r,a||null)}),i("extraKeys",null),i("configureMouse",null),i("lineWrapping",!1,js,!0),i("gutters",[],function(r,n){r.display.gutterSpecs=an(n,r.options.lineNumbers),ar(r)},!0),i("fixedGutter",!0,function(r,n){r.display.gutters.style.left=n?Qi(r.display)+"px":"0",r.refresh()},!0),i("coverGutterNextToScrollbar",!1,function(r){return Dt(r)},!0),i("scrollbarStyle","native",function(r){Hl(r),Dt(r),r.display.scrollbars.setScrollTop(r.doc.scrollTop),r.display.scrollbars.setScrollLeft(r.doc.scrollLeft)},!0),i("lineNumbers",!1,function(r,n){r.display.gutterSpecs=an(r.options.gutters,n),ar(r)},!0),i("firstLineNumber",1,ar,!0),i("lineNumberFormatter",function(r){return r},ar,!0),i("showCursorWhenSelecting",!1,tr,!0),i("resetSelectionOnContextMenu",!0),i("lineWiseCopyCut",!0),i("pasteLinesPerSelection",!0),i("selectionsMayTouch",!1),i("readOnly",!1,function(r,n){n=="nocursor"&&(Tt(r),r.display.input.blur()),r.display.input.readOnlyChanged(n)}),i("screenReaderLabel",null,function(r,n){n=n===""?null:n,r.display.input.screenReaderLabelChanged(n)}),i("disableInput",!1,function(r,n){n||r.display.input.reset()},!0),i("dragDrop",!0,Js),i("allowDropFileTypes",null),i("cursorBlinkRate",530),i("cursorScrollMargin",0),i("cursorHeight",1,tr,!0),i("singleCursorHeightPerLine",!0,tr,!0),i("workTime",100),i("workDelay",100),i("flattenSpans",!0,sr,!0),i("addModeClass",!1,sr,!0),i("pollInterval",100),i("undoDepth",200,function(r,n){return r.doc.history.undoDepth=n}),i("historyEventDelay",1250),i("viewportMargin",10,function(r){return r.refresh()},!0),i("maxHighlightLength",1e4,sr,!0),i("moveInputWithCursor",!0,function(r,n){n||r.display.input.resetPosition()}),i("tabindex",null,function(r,n){return r.display.input.getField().tabIndex=n||""}),i("autofocus",null),i("direction","ltr",function(r,n){return r.doc.setDirection(n)},!0),i("phrases",null)}function Js(e,t,i){var r=i&&i!=Et;if(!t!=!r){var n=e.display.dragFunctions,l=t?T:ve;l(e.display.scroller,"dragstart",n.start),l(e.display.scroller,"dragenter",n.enter),l(e.display.scroller,"dragover",n.over),l(e.display.scroller,"dragleave",n.leave),l(e.display.scroller,"drop",n.drop)}}function js(e){e.options.lineWrapping?(tt(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):($e(e.display.wrapper,"CodeMirror-wrap"),Bi(e)),Ji(e),ae(e),er(e),setTimeout(function(){return Dt(e)},100)}function I(e,t){var i=this;if(!(this instanceof I))return new I(e,t);this.options=t=t?rt(t):{},rt(Wo,t,!1);var r=t.value;typeof r=="string"?r=new se(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var n=new I.inputStyles[t.inputStyle](this),l=this.display=new fs(e,r,n,t);l.wrapper.CodeMirror=this,Oo(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Hl(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new Ue,keySeq:null,specialChars:null},t.autofocus&&!Gt&&l.input.focus(),A&&E<11&&setTimeout(function(){return i.display.input.reset(!0)},20),Vs(this),Ns(),ct(this),this.curOp.forceUpdate=!0,Kl(this,r),t.autofocus&&!Gt||this.hasFocus()?setTimeout(function(){i.hasFocus()&&!i.state.focused&&$i(i)},20):Tt(this);for(var o in oi)oi.hasOwnProperty(o)&&oi[o](this,t[o],Et);El(this),t.finishInit&&t.finishInit(this);for(var a=0;a<xn.length;++a)xn[a](this);dt(this),ie&&t.lineWrapping&&getComputedStyle(l.lineDiv).textRendering=="optimizelegibility"&&(l.lineDiv.style.textRendering="auto")}I.defaults=Wo,I.optionHandlers=oi;function Vs(e){var t=e.display;T(t.scroller,"mousedown",Z(e,Mo)),A&&E<11?T(t.scroller,"dblclick",Z(e,function(s){if(!Y(e,s)){var u=st(e,s);if(!(!u||bn(e,s)||Ee(e.display,s))){oe(s);var f=e.findWordAt(u);$r(e.doc,f.anchor,f.head)}}})):T(t.scroller,"dblclick",function(s){return Y(e,s)||oe(s)}),T(t.scroller,"contextmenu",function(s){return Ao(e,s)}),T(t.input.getField(),"contextmenu",function(s){t.scroller.contains(s.target)||Ao(e,s)});var i,r={end:0};function n(){t.activeTouch&&(i=setTimeout(function(){return t.activeTouch=null},1e3),r=t.activeTouch,r.end=+new Date)}function l(s){if(s.touches.length!=1)return!1;var u=s.touches[0];return u.radiusX<=1&&u.radiusY<=1}function o(s,u){if(u.left==null)return!0;var f=u.left-s.left,h=u.top-s.top;return f*f+h*h>20*20}T(t.scroller,"touchstart",function(s){if(!Y(e,s)&&!l(s)&&!bn(e,s)){t.input.ensurePolled(),clearTimeout(i);var u=+new Date;t.activeTouch={start:u,moved:!1,prev:u-r.end<=300?r:null},s.touches.length==1&&(t.activeTouch.left=s.touches[0].pageX,t.activeTouch.top=s.touches[0].pageY)}}),T(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),T(t.scroller,"touchend",function(s){var u=t.activeTouch;if(u&&!Ee(t,s)&&u.left!=null&&!u.moved&&new Date-u.start<300){var f=e.coordsChar(t.activeTouch,"page"),h;!u.prev||o(u,u.prev)?h=new O(f,f):!u.prev.prev||o(u,u.prev.prev)?h=e.findWordAt(f):h=new O(g(f.line,0),D(e.doc,g(f.line+1,0))),e.setSelection(h.anchor,h.head),e.focus(),oe(s)}n()}),T(t.scroller,"touchcancel",n),T(t.scroller,"scroll",function(){t.scroller.clientHeight&&(ir(e,t.scroller.scrollTop),ft(e,t.scroller.scrollLeft,!0),G(e,"scroll",e))}),T(t.scroller,"mousewheel",function(s){return Bl(e,s)}),T(t.scroller,"DOMMouseScroll",function(s){return Bl(e,s)}),T(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(s){Y(e,s)||qt(s)},over:function(s){Y(e,s)||(Ds(e,s),qt(s))},start:function(s){return Ms(e,s)},drop:Z(e,Ts),leave:function(s){Y(e,s)||po(e)}};var a=t.input.getField();T(a,"keyup",function(s){return ko.call(e,s)}),T(a,"keydown",Z(e,Lo)),T(a,"keypress",Z(e,To)),T(a,"focus",function(s){return $i(e,s)}),T(a,"blur",function(s){return Tt(e,s)})}var xn=[];I.defineInitHook=function(e){return xn.push(e)};function Cr(e,t,i,r){var n=e.doc,l;i==null&&(i="add"),i=="smart"&&(n.mode.indent?l=Qt(e,t).state:i="prev");var o=e.options.tabSize,a=w(n,t),s=be(a.text,null,o);a.stateAfter&&(a.stateAfter=null);var u=a.text.match(/^\s*/)[0],f;if(!r&&!/\S/.test(a.text))f=0,i="not";else if(i=="smart"&&(f=n.mode.indent(l,a.text.slice(u.length),a.text),f==Mr||f>150)){if(!r)return;i="prev"}i=="prev"?t>n.first?f=be(w(n,t-1).text,null,o):f=0:i=="add"?f=s+e.options.indentUnit:i=="subtract"?f=s-e.options.indentUnit:typeof i=="number"&&(f=s+i),f=Math.max(0,f);var h="",d=0;if(e.options.indentWithTabs)for(var c=Math.floor(f/o);c;--c)d+=o,h+="	";if(d<f&&(h+=gi(f-d)),h!=u)return Wt(n,h,g(t,0),g(t,u.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<n.sel.ranges.length;p++){var v=n.sel.ranges[p];if(v.head.line==t&&v.head.ch<u.length){var y=g(t,u.length);dn(n,p,new O(y,y));break}}}var Le=null;function ai(e){Le=e}function Cn(e,t,i,r,n){var l=e.doc;e.display.shift=!1,r||(r=l.sel);var o=+new Date-200,a=n=="paste"||e.state.pasteIncoming>o,s=Li(t),u=null;if(a&&r.ranges.length>1)if(Le&&Le.text.join(`
`)==t){if(r.ranges.length%Le.text.length==0){u=[];for(var f=0;f<Le.text.length;f++)u.push(l.splitLines(Le.text[f]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=Nr(s,function(m){return[m]}));for(var h=e.curOp.updateInput,d=r.ranges.length-1;d>=0;d--){var c=r.ranges[d],p=c.from(),v=c.to();c.empty()&&(i&&i>0?p=g(p.line,p.ch-i):e.state.overwrite&&!a?v=g(v.line,Math.min(w(l,v.line).text.length,v.ch+W(s).length)):a&&Le&&Le.lineWise&&Le.text.join(`
`)==s.join(`
`)&&(p=v=g(p.line,0)));var y={from:p,to:v,text:u?u[d%u.length]:s,origin:n||(a?"paste":e.state.cutIncoming>o?"cut":"+input")};Ot(e.doc,y),q(e,"inputRead",e,y)}t&&!a&&Fo(e,t),Mt(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Ho(e,t){var i=e.clipboardData&&e.clipboardData.getData("Text");if(i)return e.preventDefault(),!t.isReadOnly()&&!t.options.disableInput&&ce(t,function(){return Cn(t,i,0,null,"paste")}),!0}function Fo(e,t){if(!(!e.options.electricChars||!e.options.smartIndent))for(var i=e.doc.sel,r=i.ranges.length-1;r>=0;r--){var n=i.ranges[r];if(!(n.head.ch>100||r&&i.ranges[r-1].head.line==n.head.line)){var l=e.getModeAt(n.head),o=!1;if(l.electricChars){for(var a=0;a<l.electricChars.length;a++)if(t.indexOf(l.electricChars.charAt(a))>-1){o=Cr(e,n.head.line,"smart");break}}else l.electricInput&&l.electricInput.test(w(e.doc,n.head.line).text.slice(0,n.head.ch))&&(o=Cr(e,n.head.line,"smart"));o&&q(e,"electricInput",e,n.head.line)}}}function Po(e){for(var t=[],i=[],r=0;r<e.doc.sel.ranges.length;r++){var n=e.doc.sel.ranges[r].head.line,l={anchor:g(n,0),head:g(n+1,0)};i.push(l),t.push(e.getRange(l.anchor,l.head))}return{text:t,ranges:i}}function Eo(e,t,i,r){e.setAttribute("autocorrect",i?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function Io(){var e=k("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=k("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return ie?e.style.width="1000px":e.setAttribute("wrap","off"),zt&&(e.style.border="1px solid black"),Eo(e),t}function $s(e){var t=e.optionHandlers,i=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(r,n){var l=this.options,o=l[r];l[r]==n&&r!="mode"||(l[r]=n,t.hasOwnProperty(r)&&Z(this,t[r])(this,n,o),G(this,"optionChange",this,r))},getOption:function(r){return this.options[r]},getDoc:function(){return this.doc},addKeyMap:function(r,n){this.state.keyMaps[n?"push":"unshift"](ni(r))},removeKeyMap:function(r){for(var n=this.state.keyMaps,l=0;l<n.length;++l)if(n[l]==r||n[l].name==r)return n.splice(l,1),!0},addOverlay:ne(function(r,n){var l=r.token?r:e.getMode(this.options,r);if(l.startState)throw new Error("Overlays may not be stateful.");qo(this.state.overlays,{mode:l,modeSpec:r,opaque:n&&n.opaque,priority:n&&n.priority||0},function(o){return o.priority}),this.state.modeGen++,ae(this)}),removeOverlay:ne(function(r){for(var n=this.state.overlays,l=0;l<n.length;++l){var o=n[l].modeSpec;if(o==r||typeof r=="string"&&o.name==r){n.splice(l,1),this.state.modeGen++,ae(this);return}}}),indentLine:ne(function(r,n,l){typeof n!="string"&&typeof n!="number"&&(n==null?n=this.options.smartIndent?"smart":"prev":n=n?"add":"subtract"),Zt(this.doc,r)&&Cr(this,r,n,l)}),indentSelection:ne(function(r){for(var n=this.doc.sel.ranges,l=-1,o=0;o<n.length;o++){var a=n[o];if(a.empty())a.head.line>l&&(Cr(this,a.head.line,r,!0),l=a.head.line,o==this.doc.sel.primIndex&&Mt(this));else{var s=a.from(),u=a.to(),f=Math.max(l,s.line);l=Math.min(this.lastLine(),u.line-(u.ch?0:1))+1;for(var h=f;h<l;++h)Cr(this,h,r);var d=this.doc.sel.ranges;s.ch==0&&n.length==d.length&&d[o].from().ch>0&&dn(this.doc,o,new O(s,d[o].to()),ke)}}}),getTokenAt:function(r,n){return _n(this,r,n)},getLineTokens:function(r,n){return _n(this,g(r),n,!0)},getTokenTypeAt:function(r){r=D(this.doc,r);var n=Gn(this,w(this.doc,r.line)),l=0,o=(n.length-1)/2,a=r.ch,s;if(a==0)s=n[2];else for(;;){var u=l+o>>1;if((u?n[u*2-1]:0)>=a)o=u;else if(n[u*2+1]<a)l=u+1;else{s=n[u*2+2];break}}var f=s?s.indexOf("overlay "):-1;return f<0?s:f==0?null:s.slice(0,f-1)},getModeAt:function(r){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(r).state).mode:n},getHelper:function(r,n){return this.getHelpers(r,n)[0]},getHelpers:function(r,n){var l=[];if(!i.hasOwnProperty(n))return l;var o=i[n],a=this.getModeAt(r);if(typeof a[n]=="string")o[a[n]]&&l.push(o[a[n]]);else if(a[n])for(var s=0;s<a[n].length;s++){var u=o[a[n][s]];u&&l.push(u)}else a.helperType&&o[a.helperType]?l.push(o[a.helperType]):o[a.name]&&l.push(o[a.name]);for(var f=0;f<o._global.length;f++){var h=o._global[f];h.pred(a,this)&&$(l,h.val)==-1&&l.push(h.val)}return l},getStateAfter:function(r,n){var l=this.doc;return r=Rn(l,r??l.first+l.size-1),Qt(this,r+1,n).state},cursorCoords:function(r,n){var l,o=this.doc.sel.primary();return r==null?l=o.head:typeof r=="object"?l=D(this.doc,r):l=r?o.from():o.to(),we(this,l,n||"page")},charCoords:function(r,n){return Kr(this,D(this.doc,r),n||"page")},coordsChar:function(r,n){return r=bl(this,r,n||"page"),Yi(this,r.left,r.top)},lineAtHeight:function(r,n){return r=bl(this,{top:r,left:0},n||"page").top,lt(this.doc,r+this.display.viewOffset)},heightAtLine:function(r,n,l){var o=!1,a;if(typeof r=="number"){var s=this.doc.first+this.doc.size-1;r<this.doc.first?r=this.doc.first:r>s&&(r=s,o=!0),a=w(this.doc,r)}else a=r;return Ur(this,a,{top:0,left:0},n||"page",l||o).top+(o?this.doc.height-Pe(a):0)},defaultTextHeight:function(){return Lt(this.display)},defaultCharWidth:function(){return kt(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(r,n,l,o,a){var s=this.display;r=we(this,D(this.doc,r));var u=r.bottom,f=r.left;if(n.style.position="absolute",n.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(n),s.sizer.appendChild(n),o=="over")u=r.top;else if(o=="above"||o=="near"){var h=Math.max(s.wrapper.clientHeight,this.doc.height),d=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);(o=="above"||r.bottom+n.offsetHeight>h)&&r.top>n.offsetHeight?u=r.top-n.offsetHeight:r.bottom+n.offsetHeight<=h&&(u=r.bottom),f+n.offsetWidth>d&&(f=d-n.offsetWidth)}n.style.top=u+"px",n.style.left=n.style.right="",a=="right"?(f=s.sizer.clientWidth-n.offsetWidth,n.style.right="0px"):(a=="left"?f=0:a=="middle"&&(f=(s.sizer.clientWidth-n.offsetWidth)/2),n.style.left=f+"px"),l&&Ja(this,{left:f,top:u,right:f+n.offsetWidth,bottom:u+n.offsetHeight})},triggerOnKeyDown:ne(Lo),triggerOnKeyPress:ne(To),triggerOnKeyUp:ko,triggerOnMouseDown:ne(Mo),execCommand:function(r){if(yr.hasOwnProperty(r))return yr[r].call(null,this)},triggerElectric:ne(function(r){Fo(this,r)}),findPosH:function(r,n,l,o){var a=1;n<0&&(a=-1,n=-n);for(var s=D(this.doc,r),u=0;u<n&&(s=wn(this.doc,s,a,l,o),!s.hitSide);++u);return s},moveH:ne(function(r,n){var l=this;this.extendSelectionsBy(function(o){return l.display.shift||l.doc.extend||o.empty()?wn(l.doc,o.head,r,n,l.options.rtlMoveVisually):r<0?o.from():o.to()},Kt)}),deleteH:ne(function(r,n){var l=this.doc.sel,o=this.doc;l.somethingSelected()?o.replaceSelection("",null,"+delete"):Pt(this,function(a){var s=wn(o,a.head,r,n,!1);return r<0?{from:s,to:a.head}:{from:a.head,to:s}})}),findPosV:function(r,n,l,o){var a=1,s=o;n<0&&(a=-1,n=-n);for(var u=D(this.doc,r),f=0;f<n;++f){var h=we(this,u,"div");if(s==null?s=h.left:h.left=s,u=Ro(this,h,a,l),u.hitSide)break}return u},moveV:ne(function(r,n){var l=this,o=this.doc,a=[],s=!this.display.shift&&!o.extend&&o.sel.somethingSelected();if(o.extendSelectionsBy(function(f){if(s)return r<0?f.from():f.to();var h=we(l,f.head,"div");f.goalColumn!=null&&(h.left=f.goalColumn),a.push(h.left);var d=Ro(l,h,r,n);return n=="page"&&f==o.sel.primary()&&tn(l,Kr(l,d,"div").top-h.top),d},Kt),a.length)for(var u=0;u<o.sel.ranges.length;u++)o.sel.ranges[u].goalColumn=a[u]}),findWordAt:function(r){var n=this.doc,l=w(n,r.line).text,o=r.ch,a=r.ch;if(l){var s=this.getHelper(r,"wordChars");(r.sticky=="before"||a==l.length)&&o?--o:++a;for(var u=l.charAt(o),f=Ar(u,s)?function(h){return Ar(h,s)}:/\s/.test(u)?function(h){return/\s/.test(h)}:function(h){return!/\s/.test(h)&&!Ar(h)};o>0&&f(l.charAt(o-1));)--o;for(;a<l.length&&f(l.charAt(a));)++a}return new O(g(r.line,o),g(r.line,a))},toggleOverwrite:function(r){r!=null&&r==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?tt(this.display.cursorDiv,"CodeMirror-overwrite"):$e(this.display.cursorDiv,"CodeMirror-overwrite"),G(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==me()},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:ne(function(r,n){rr(this,r,n)}),getScrollInfo:function(){var r=this.display.scroller;return{left:r.scrollLeft,top:r.scrollTop,height:r.scrollHeight-Ne(this)-this.display.barHeight,width:r.scrollWidth-Ne(this)-this.display.barWidth,clientHeight:Ui(this),clientWidth:ot(this)}},scrollIntoView:ne(function(r,n){r==null?(r={from:this.doc.sel.primary().head,to:null},n==null&&(n=this.options.cursorScrollMargin)):typeof r=="number"?r={from:g(r,0),to:null}:r.from==null&&(r={from:r,to:null}),r.to||(r.to=r.from),r.margin=n||0,r.from.line!=null?ja(this,r):Nl(this,r.from,r.to,r.margin)}),setSize:ne(function(r,n){var l=this,o=function(s){return typeof s=="number"||/^\d+$/.test(String(s))?s+"px":s};r!=null&&(this.display.wrapper.style.width=o(r)),n!=null&&(this.display.wrapper.style.height=o(n)),this.options.lineWrapping&&gl(this);var a=this.display.viewFrom;this.doc.iter(a,this.display.viewTo,function(s){if(s.widgets){for(var u=0;u<s.widgets.length;u++)if(s.widgets[u].noHScroll){_e(l,a,"widget");break}}++a}),this.curOp.forceUpdate=!0,G(this,"refresh",this)}),operation:function(r){return ce(this,r)},startOperation:function(){return ct(this)},endOperation:function(){return dt(this)},refresh:ne(function(){var r=this.display.cachedTextHeight;ae(this),this.curOp.forceUpdate=!0,er(this),rr(this,this.doc.scrollLeft,this.doc.scrollTop),ln(this.display),(r==null||Math.abs(r-Lt(this.display))>.5||this.options.lineWrapping)&&Ji(this),G(this,"refresh",this)}),swapDoc:ne(function(r){var n=this.doc;return n.cm=null,this.state.selectingText&&this.state.selectingText(),Kl(this,r),er(this),this.display.input.reset(),rr(this,r.scrollLeft,r.scrollTop),this.curOp.forceScroll=!0,q(this,"swapDoc",this,n),n}),phrase:function(r){var n=this.options.phrases;return n&&Object.prototype.hasOwnProperty.call(n,r)?n[r]:r},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},mt(e),e.registerHelper=function(r,n,l){i.hasOwnProperty(r)||(i[r]=e[r]={_global:[]}),i[r][n]=l},e.registerGlobalHelper=function(r,n,l,o){e.registerHelper(r,n,o),i[r]._global.push({pred:l,val:o})}}function wn(e,t,i,r,n){var l=t,o=i,a=w(e,t.line),s=n&&e.direction=="rtl"?-i:i;function u(){var x=t.line+s;return x<e.first||x>=e.first+e.size?!1:(t=new g(x,t.ch,t.sticky),a=w(e,x))}function f(x){var b;if(r=="codepoint"){var C=a.text.charCodeAt(t.ch+(i>0?0:-1));if(isNaN(C))b=null;else{var L=i>0?C>=55296&&C<56320:C>=56320&&C<57343;b=new g(t.line,Math.max(0,Math.min(a.text.length,t.ch+i*(L?2:1))),-i)}}else n?b=Fs(e.cm,a,t,i):b=vn(a,t,i);if(b==null)if(!x&&u())t=gn(n,e.cm,a,t.line,s);else return!1;else t=b;return!0}if(r=="char"||r=="codepoint")f();else if(r=="column")f(!0);else if(r=="word"||r=="group")for(var h=null,d=r=="group",c=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;!(i<0&&!f(!p));p=!1){var v=a.text.charAt(t.ch)||`
`,y=Ar(v,c)?"w":d&&v==`
`?"n":!d||/\s/.test(v)?null:"p";if(d&&!p&&!y&&(y="s"),h&&h!=y){i<0&&(i=1,f(),t.sticky="after");break}if(y&&(h=y),i>0&&!f(!p))break}var m=ti(e,t,l,o,!0);return Oi(l,m)&&(m.hitSide=!0),m}function Ro(e,t,i,r){var n=e.doc,l=t.left,o;if(r=="page"){var a=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),s=Math.max(a-.5*Lt(e.display),3);o=(i>0?t.bottom:t.top)+i*s}else r=="line"&&(o=i>0?t.bottom+3:t.top-3);for(var u;u=Yi(e,l,o),!!u.outside;){if(i<0?o<=0:o>=n.height){u.hitSide=!0;break}o+=i*5}return u}var F=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new Ue,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};F.prototype.init=function(e){var t=this,i=this,r=i.cm,n=i.div=e.lineDiv;n.contentEditable=!0,Eo(n,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize);function l(a){for(var s=a.target;s;s=s.parentNode){if(s==n)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(s.className))break}return!1}T(n,"paste",function(a){!l(a)||Y(r,a)||Ho(a,r)||E<=11&&setTimeout(Z(r,function(){return t.updateFromDOM()}),20)}),T(n,"compositionstart",function(a){t.composing={data:a.data,done:!1}}),T(n,"compositionupdate",function(a){t.composing||(t.composing={data:a.data,done:!1})}),T(n,"compositionend",function(a){t.composing&&(a.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),T(n,"touchstart",function(){return i.forceCompositionEnd()}),T(n,"input",function(){t.composing||t.readFromDOMSoon()});function o(a){if(!(!l(a)||Y(r,a))){if(r.somethingSelected())ai({lineWise:!1,text:r.getSelections()}),a.type=="cut"&&r.replaceSelection("",null,"cut");else if(r.options.lineWiseCopyCut){var s=Po(r);ai({lineWise:!0,text:s.text}),a.type=="cut"&&r.operation(function(){r.setSelections(s.ranges,0,ke),r.replaceSelection("",null,"cut")})}else return;if(a.clipboardData){a.clipboardData.clearData();var u=Le.text.join(`
`);if(a.clipboardData.setData("Text",u),a.clipboardData.getData("Text")==u){a.preventDefault();return}}var f=Io(),h=f.firstChild;r.display.lineSpace.insertBefore(f,r.display.lineSpace.firstChild),h.value=Le.text.join(`
`);var d=me();Ut(h),setTimeout(function(){r.display.lineSpace.removeChild(f),d.focus(),d==n&&i.showPrimarySelection()},50)}}T(n,"copy",o),T(n,"cut",o)},F.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},F.prototype.prepareSelection=function(){var e=kl(this.cm,!1);return e.focus=me()==this.div,e},F.prototype.showSelection=function(e,t){!e||!this.cm.display.view.length||((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},F.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},F.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,i=t.doc.sel.primary(),r=i.from(),n=i.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||n.line<t.display.viewFrom){e.removeAllRanges();return}var l=si(t,e.anchorNode,e.anchorOffset),o=si(t,e.focusNode,e.focusOffset);if(!(l&&!l.bad&&o&&!o.bad&&M(Hr(l,o),r)==0&&M(Wr(l,o),n)==0)){var a=t.display.view,s=r.line>=t.display.viewFrom&&Bo(t,r)||{node:a[0].measure.map[2],offset:0},u=n.line<t.display.viewTo&&Bo(t,n);if(!u){var f=a[a.length-1].measure,h=f.maps?f.maps[f.maps.length-1]:f.map;u={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(!s||!u){e.removeAllRanges();return}var d=e.rangeCount&&e.getRangeAt(0),c;try{c=et(s.node,s.offset,u.offset,u.node)}catch{}c&&(!We&&t.state.focused?(e.collapse(s.node,s.offset),c.collapsed||(e.removeAllRanges(),e.addRange(c))):(e.removeAllRanges(),e.addRange(c)),d&&e.anchorNode==null?e.addRange(d):We&&this.startGracePeriod()),this.rememberSelection()}},F.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},F.prototype.showMultipleSelections=function(e){pe(this.cm.display.cursorDiv,e.cursors),pe(this.cm.display.selectionDiv,e.selection)},F.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},F.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return Ge(this.div,t)},F.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&((!this.selectionInEditor()||me()!=this.div)&&this.showSelection(this.prepareSelection(),!0),this.div.focus())},F.prototype.blur=function(){this.div.blur()},F.prototype.getField=function(){return this.div},F.prototype.supportsTouch=function(){return!0},F.prototype.receivedFocus=function(){var e=this,t=this;this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):ce(this.cm,function(){return t.cm.curOp.selectionChanged=!0});function i(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,i))}this.polling.set(this.cm.options.pollInterval,i)},F.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},F.prototype.pollSelection=function(){if(!(this.readDOMTimeout!=null||this.gracePeriod||!this.selectionChanged())){var e=this.getSelection(),t=this.cm;if(Tr&&Lr&&this.cm.display.gutterSpecs.length&&eu(e.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var i=si(t,e.anchorNode,e.anchorOffset),r=si(t,e.focusNode,e.focusOffset);i&&r&&ce(t,function(){ee(t.doc,Ye(i,r),ke),(i.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},F.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,t=e.display,i=e.doc.sel.primary(),r=i.from(),n=i.to();if(r.ch==0&&r.line>e.firstLine()&&(r=g(r.line-1,w(e.doc,r.line-1).length)),n.ch==w(e.doc,n.line).text.length&&n.line<e.lastLine()&&(n=g(n.line+1,0)),r.line<t.viewFrom||n.line>t.viewTo-1)return!1;var l,o,a;r.line==t.viewFrom||(l=ut(e,r.line))==0?(o=H(t.view[0].line),a=t.view[0].node):(o=H(t.view[l].line),a=t.view[l-1].node.nextSibling);var s=ut(e,n.line),u,f;if(s==t.view.length-1?(u=t.viewTo-1,f=t.lineDiv.lastChild):(u=H(t.view[s+1].line)-1,f=t.view[s+1].node.previousSibling),!a)return!1;for(var h=e.doc.splitLines(tu(e,a,f,o,u)),d=nt(e.doc,g(o,0),g(u,w(e.doc,u).text.length));h.length>1&&d.length>1;)if(W(h)==W(d))h.pop(),d.pop(),u--;else if(h[0]==d[0])h.shift(),d.shift(),o++;else break;for(var c=0,p=0,v=h[0],y=d[0],m=Math.min(v.length,y.length);c<m&&v.charCodeAt(c)==y.charCodeAt(c);)++c;for(var x=W(h),b=W(d),C=Math.min(x.length-(h.length==1?c:0),b.length-(d.length==1?c:0));p<C&&x.charCodeAt(x.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(h.length==1&&d.length==1&&o==r.line)for(;c&&c>r.ch&&x.charCodeAt(x.length-p-1)==b.charCodeAt(b.length-p-1);)c--,p++;h[h.length-1]=x.slice(0,x.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(c).replace(/\u200b+$/,"");var L=g(o,c),S=g(u,d.length?W(d).length-p:0);if(h.length>1||h[0]||M(L,S))return Wt(e.doc,h,L,S,"+input"),!0},F.prototype.ensurePolled=function(){this.forceCompositionEnd()},F.prototype.reset=function(){this.forceCompositionEnd()},F.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},F.prototype.readFromDOMSoon=function(){var e=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing)if(e.composing.done)e.composing=null;else return;e.updateFromDOM()},80))},F.prototype.updateFromDOM=function(){var e=this;(this.cm.isReadOnly()||!this.pollContent())&&ce(this.cm,function(){return ae(e.cm)})},F.prototype.setUneditable=function(e){e.contentEditable="false"},F.prototype.onKeyPress=function(e){e.charCode==0||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Z(this.cm,Cn)(this.cm,String.fromCharCode(e.charCode==null?e.keyCode:e.charCode),0))},F.prototype.readOnlyChanged=function(e){this.div.contentEditable=String(e!="nocursor")},F.prototype.onContextMenu=function(){},F.prototype.resetPosition=function(){},F.prototype.needsContentAttribute=!0;function Bo(e,t){var i=Ki(e,t.line);if(!i||i.hidden)return null;var r=w(e.doc,t.line),n=hl(i,r,t.line),l=He(r,e.doc.direction),o="left";if(l){var a=Yt(l,t.ch);o=a%2?"right":"left"}var s=pl(n.map,t.ch,o);return s.offset=s.collapse=="right"?s.end:s.start,s}function eu(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function It(e,t){return t&&(e.bad=!0),e}function tu(e,t,i,r,n){var l="",o=!1,a=e.doc.lineSeparator(),s=!1;function u(c){return function(p){return p.id==c}}function f(){o&&(l+=a,s&&(l+=a),o=s=!1)}function h(c){c&&(f(),l+=c)}function d(c){if(c.nodeType==1){var p=c.getAttribute("cm-text");if(p){h(p);return}var v=c.getAttribute("cm-marker"),y;if(v){var m=e.findMarks(g(r,0),g(n+1,0),u(+v));m.length&&(y=m[0].find(0))&&h(nt(e.doc,y.from,y.to).join(a));return}if(c.getAttribute("contenteditable")=="false")return;var x=/^(pre|div|p|li|table|br)$/i.test(c.nodeName);if(!/^br$/i.test(c.nodeName)&&c.textContent.length==0)return;x&&f();for(var b=0;b<c.childNodes.length;b++)d(c.childNodes[b]);/^(pre|p)$/i.test(c.nodeName)&&(s=!0),x&&(o=!0)}else c.nodeType==3&&h(c.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;d(t),t!=i;)t=t.nextSibling,s=!1;return l}function si(e,t,i){var r;if(t==e.display.lineDiv){if(r=e.display.lineDiv.childNodes[i],!r)return It(e.clipPos(g(e.display.viewTo-1)),!0);t=null,i=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var n=0;n<e.display.view.length;n++){var l=e.display.view[n];if(l.node==r)return ru(l,t,i)}}function ru(e,t,i){var r=e.text.firstChild,n=!1;if(!t||!Ge(r,t))return It(g(H(e.line),0),!0);if(t==r&&(n=!0,t=r.childNodes[i],i=0,!t)){var l=e.rest?W(e.rest):e.line;return It(g(H(l),l.text.length),n)}var o=t.nodeType==3?t:null,a=t;for(!o&&t.childNodes.length==1&&t.firstChild.nodeType==3&&(o=t.firstChild,i&&(i=o.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=e.measure,u=s.maps;function f(y,m,x){for(var b=-1;b<(u?u.length:0);b++)for(var C=b<0?s.map:u[b],L=0;L<C.length;L+=3){var S=C[L+2];if(S==y||S==m){var N=H(b<0?e.line:e.rest[b]),P=C[L]+x;return(x<0||S!=y)&&(P=C[L+(x?1:0)]),g(N,P)}}}var h=f(o,a,i);if(h)return It(h,n);for(var d=a.nextSibling,c=o?o.nodeValue.length-i:0;d;d=d.nextSibling){if(h=f(d,d.firstChild,0),h)return It(g(h.line,h.ch-c),n);c+=d.textContent.length}for(var p=a.previousSibling,v=i;p;p=p.previousSibling){if(h=f(p,p.firstChild,-1),h)return It(g(h.line,h.ch+v),n);v+=p.textContent.length}}var z=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new Ue,this.hasSelection=!1,this.composing=null};z.prototype.init=function(e){var t=this,i=this,r=this.cm;this.createField(e);var n=this.textarea;e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),zt&&(n.style.width="0px"),T(n,"input",function(){A&&E>=9&&t.hasSelection&&(t.hasSelection=null),i.poll()}),T(n,"paste",function(o){Y(r,o)||Ho(o,r)||(r.state.pasteIncoming=+new Date,i.fastPoll())});function l(o){if(!Y(r,o)){if(r.somethingSelected())ai({lineWise:!1,text:r.getSelections()});else if(r.options.lineWiseCopyCut){var a=Po(r);ai({lineWise:!0,text:a.text}),o.type=="cut"?r.setSelections(a.ranges,null,ke):(i.prevInput="",n.value=a.text.join(`
`),Ut(n))}else return;o.type=="cut"&&(r.state.cutIncoming=+new Date)}}T(n,"cut",l),T(n,"copy",l),T(e.scroller,"paste",function(o){if(!(Ee(e,o)||Y(r,o))){if(!n.dispatchEvent){r.state.pasteIncoming=+new Date,i.focus();return}var a=new Event("paste");a.clipboardData=o.clipboardData,n.dispatchEvent(a)}}),T(e.lineSpace,"selectstart",function(o){Ee(e,o)||oe(o)}),T(n,"compositionstart",function(){var o=r.getCursor("from");i.composing&&i.composing.range.clear(),i.composing={start:o,range:r.markText(o,r.getCursor("to"),{className:"CodeMirror-composing"})}}),T(n,"compositionend",function(){i.composing&&(i.poll(),i.composing.range.clear(),i.composing=null)})},z.prototype.createField=function(e){this.wrapper=Io(),this.textarea=this.wrapper.firstChild},z.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},z.prototype.prepareSelection=function(){var e=this.cm,t=e.display,i=e.doc,r=kl(e);if(e.options.moveInputWithCursor){var n=we(e,i.sel.primary().head,"div"),l=t.wrapper.getBoundingClientRect(),o=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,n.top+o.top-l.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,n.left+o.left-l.left))}return r},z.prototype.showSelection=function(e){var t=this.cm,i=t.display;pe(i.cursorDiv,e.cursors),pe(i.selectionDiv,e.selection),e.teTop!=null&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},z.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing)){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var i=t.getSelection();this.textarea.value=i,t.state.focused&&Ut(this.textarea),A&&E>=9&&(this.hasSelection=i)}else e||(this.prevInput=this.textarea.value="",A&&E>=9&&(this.hasSelection=null))}},z.prototype.getField=function(){return this.textarea},z.prototype.supportsTouch=function(){return!1},z.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!Gt||me()!=this.textarea))try{this.textarea.focus()}catch{}},z.prototype.blur=function(){this.textarea.blur()},z.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},z.prototype.receivedFocus=function(){this.slowPoll()},z.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},z.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0;function i(){var r=t.poll();!r&&!e?(e=!0,t.polling.set(60,i)):(t.pollingFast=!1,t.slowPoll())}t.polling.set(20,i)},z.prototype.poll=function(){var e=this,t=this.cm,i=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||ta(i)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var n=i.value;if(n==r&&!t.somethingSelected())return!1;if(A&&E>=9&&this.hasSelection===n||ye&&/[\uf700-\uf7ff]/.test(n))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var l=n.charCodeAt(0);if(l==8203&&!r&&(r="​"),l==8666)return this.reset(),this.cm.execCommand("undo")}for(var o=0,a=Math.min(r.length,n.length);o<a&&r.charCodeAt(o)==n.charCodeAt(o);)++o;return ce(t,function(){Cn(t,n.slice(o),r.length-o,null,e.composing?"*compose":null),n.length>1e3||n.indexOf(`
`)>-1?i.value=e.prevInput="":e.prevInput=n,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},z.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},z.prototype.onKeyPress=function(){A&&E>=9&&(this.hasSelection=null),this.fastPoll()},z.prototype.onContextMenu=function(e){var t=this,i=t.cm,r=i.display,n=t.textarea;t.contextMenuPending&&t.contextMenuPending();var l=st(i,e),o=r.scroller.scrollTop;if(!l||Ce)return;var a=i.options.resetSelectionOnContextMenu;a&&i.doc.sel.contains(l)==-1&&Z(i,ee)(i.doc,Ye(l),ke);var s=n.style.cssText,u=t.wrapper.style.cssText,f=t.wrapper.offsetParent.getBoundingClientRect();t.wrapper.style.cssText="position: static",n.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(e.clientY-f.top-5)+"px; left: "+(e.clientX-f.left-5)+`px;
      z-index: 1000; background: `+(A?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`;var h;ie&&(h=window.scrollY),r.input.focus(),ie&&window.scrollTo(null,h),r.input.reset(),i.somethingSelected()||(n.value=t.prevInput=" "),t.contextMenuPending=c,r.selForContextMenu=i.doc.sel,clearTimeout(r.detectingSelectAll);function d(){if(n.selectionStart!=null){var v=i.somethingSelected(),y="​"+(v?n.value:"");n.value="⇚",n.value=y,t.prevInput=v?"":"​",n.selectionStart=1,n.selectionEnd=y.length,r.selForContextMenu=i.doc.sel}}function c(){if(t.contextMenuPending==c&&(t.contextMenuPending=!1,t.wrapper.style.cssText=u,n.style.cssText=s,A&&E<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=o),n.selectionStart!=null)){(!A||A&&E<9)&&d();var v=0,y=function(){r.selForContextMenu==i.doc.sel&&n.selectionStart==0&&n.selectionEnd>0&&t.prevInput=="​"?Z(i,ro)(i):v++<10?r.detectingSelectAll=setTimeout(y,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(y,200)}}if(A&&E>=9&&d(),hi){qt(e);var p=function(){ve(window,"mouseup",p),setTimeout(c,20)};T(window,"mouseup",p)}else setTimeout(c,50)},z.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled=e=="nocursor",this.textarea.readOnly=!!e},z.prototype.setUneditable=function(){},z.prototype.needsContentAttribute=!1;function iu(e,t){if(t=t?rt(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),t.autofocus==null){var i=me();t.autofocus=i==e||e.getAttribute("autofocus")!=null&&i==document.body}function r(){e.value=a.getValue()}var n;if(e.form&&(T(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var l=e.form;n=l.submit;try{var o=l.submit=function(){r(),l.submit=n,l.submit(),l.submit=o}}catch{}}t.finishInit=function(s){s.save=r,s.getTextArea=function(){return e},s.toTextArea=function(){s.toTextArea=isNaN,r(),e.parentNode.removeChild(s.getWrapperElement()),e.style.display="",e.form&&(ve(e.form,"submit",r),!t.leaveSubmitMethodAlone&&typeof e.form.submit=="function"&&(e.form.submit=n))}},e.style.display="none";var a=I(function(s){return e.parentNode.insertBefore(s,e.nextSibling)},t);return a}function nu(e){e.off=ve,e.on=T,e.wheelEventPixels=hs,e.Doc=se,e.splitLines=Li,e.countColumn=be,e.findColumn=vi,e.isWordChar=yi,e.Pass=Mr,e.signal=G,e.Line=Ct,e.changeEnd=qe,e.scrollbarModel=Wl,e.Pos=g,e.cmpPos=M,e.modes=Ti,e.mimeModes=bt,e.resolveMode=Or,e.getMode=Mi,e.modeExtensions=xt,e.extendMode=oa,e.copyState=it,e.startState=In,e.innerMode=Di,e.commands=yr,e.keyMap=Re,e.keyName=bo,e.isModifierKey=yo,e.lookupKey=Ft,e.normalizeKeyMap=Hs,e.StringStream=U,e.SharedTextMarker=pr,e.TextMarker=Qe,e.LineWidget=dr,e.e_preventDefault=oe,e.e_stopPropagation=Pn,e.e_stop=qt,e.addClass=tt,e.contains=Ge,e.rmClass=$e,e.keyNames=Je}Qs(I),$s(I);var lu="iter insert remove copy getEditor constructor".split(" ");for(var ui in se.prototype)se.prototype.hasOwnProperty(ui)&&$(lu,ui)<0&&(I.prototype[ui]=function(e){return function(){return e.apply(this.doc,arguments)}}(se.prototype[ui]));return mt(se),I.inputStyles={textarea:z,contenteditable:F},I.defineMode=function(e){!I.defaults.mode&&e!="null"&&(I.defaults.mode=e),na.apply(this,arguments)},I.defineMIME=la,I.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),I.defineMIME("text/plain","null"),I.defineExtension=function(e,t){I.prototype[e]=t},I.defineDocExtension=function(e,t){se.prototype[e]=t},I.fromTextArea=iu,nu(I),I.version="5.63.3",I})}(fi)),fi.exports}export{su as r};
