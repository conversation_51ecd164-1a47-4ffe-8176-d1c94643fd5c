import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as p,c as I,o as S,a as m,b as C,d as y,w as g,e as N,f as J,h as b,i as Y,j as F,t as $,n as z}from"./index-B5fOQYc3.js";const j={class:"card-header"},E={key:0,class:"avueForm"},R={key:1,class:"no-data-tip"},T={key:2,class:"card-footer"},P={__name:"property",props:{formOption:{type:Object,default:{}}},emits:["updateAvueForm","apply","reset"],setup(A,{expose:M,emit:x}){const d=x,h=A,v=p(!0),c=p({}),i=p(""),l=p({}),t=p({}),f=p({labelWidth:90,submitBtn:!1,emptyBtn:!1,size:"small",group:[],column:{}}),D=I(()=>l.value&&l.value.formNode&&l.value.options&&Array.isArray(l.value.options)&&l.value.options.length>0),_=a=>{if(console.log("进入init"),!a||typeof a!="object"){console.warn("formOption 无效，使用默认值",a);return}if(!a.formName||!a.formNode||!Array.isArray(a.options)){console.warn("formOption 结构不完整",a);return}l.value=a;const o=a.formNode?.id;if(o)if(t.value[o])console.log(`节点 ${o} 初始数据已存在，不重新保存`);else try{t.value[o]=JSON.parse(JSON.stringify(a)),console.log(`节点 ${o} 初始数据已保存:`,t.value[o])}catch(r){console.error("保存初始数据失败:",r),t.value[o]={formName:a.formName||"",formNode:a.formNode||{},options:(a.options||[]).map(e=>({label:e.label||"",value:e.value!==void 0?e.value:"",type:e.type||"input",order:e.order||0,group:e.group||void 0,dicData:e.dicData||void 0}))},console.log(`节点 ${o} 初始数据保存(备用方式):`,t.value[o])}v.value=!0;let s=a?.options||h.formOption.options;c.value={},i.value=a?.formName||h.formOption.formName,s=[...s].sort((r,e)=>{const n=Number.isInteger(r.order)?r.order:1/0,u=Number.isInteger(e.order)?e.order:1/0;return n-u||r.label.localeCompare(e.label)}),f.value=JSON.parse(JSON.stringify({labelWidth:90,submitBtn:!1,emptyBtn:!1,size:"small",group:[],column:{}})),z(()=>{const r=new Map;s.forEach(e=>{if(c.value[e.label]=e.value,e.group){r.has(e.group)||r.set(e.group,{label:e.group,column:{}});const n=r.get(e.group);n.column[e.label]={label:e.label,disabled:e.disabled,span:22,type:e.type,...e.type==="color"&&{colorFormat:"hex",showAlpha:!1},...e.type==="date"&&{format:e.format||"YYYY-MM-DD",valueFormat:e.valueFormat||"YYYY-MM-DD"},...e.type==="select"&&{props:e.props||{},dicData:e.dicData||[]},change:({value:u,column:B})=>{!v.value&&c.value[e.label]!==u&&d("updateAvueForm",u,B,i.value)}}}else f.value.column[e.label]={label:e.label,disabled:e.disabled,span:22,type:e.type,...e.type==="color"&&{colorFormat:"hex",showAlpha:!1},...e.type==="date"&&{format:e.format||"YYYY-MM-DD",valueFormat:e.valueFormat||"YYYY-MM-DD"},...e.type==="select"&&{props:e.props||{},dicData:e.dicData||[]},change:({value:n,column:u})=>{!v.value&&c.value[e.label]!==n&&d("updateAvueForm",n,u,i.value)}}}),f.value.group=Array.from(r.values()),setTimeout(()=>{v.value=!1},100)})},k=()=>{l.value&&l.value.options&&l.value.options.forEach(a=>{a.value=c.value[a.label]}),console.log("formdata.value",l.value),d("apply",l.value)},w=()=>{console.log("重置");const a=l.value?.formNode?.id;if(!a){console.warn("无法获取当前节点ID，重置失败");return}const o=t.value[a];if(!o){console.warn(`节点 ${a} 没有初始数据，重置失败`);return}try{console.log(`重置节点 ${a} 数据:`,o);const s=JSON.parse(JSON.stringify(o));_(s),d("reset",s)}catch(s){console.error("重置失败:",s)}};return M({init:_,clearResetData:(a=null)=>{a?(delete t.value[a],console.log(`节点 ${a} 的重置数据已清空`)):(t.value={},console.log("所有节点的重置数据已清空"))}}),S(()=>{_()}),(a,o)=>{const s=m("avue-form"),r=m("el-empty"),e=m("el-button"),n=m("el-card");return y(),C(n,{class:"box-card"},{header:g(()=>[F("div",j,[F("span",null,$(i.value),1)])]),default:g(()=>[D.value?(y(),N("div",E,[b(s,{option:f.value,modelValue:c.value,"onUpdate:modelValue":o[0]||(o[0]=u=>c.value=u)},null,8,["option","modelValue"])])):(y(),N("div",R,[b(r,{description:"请选择一个节点查看属性","image-size":80})])),D.value?(y(),N("div",T,[b(e,{type:"primary",onClick:k},{default:g(()=>o[1]||(o[1]=[Y("应用")])),_:1}),b(e,{type:"primary",onClick:w},{default:g(()=>o[2]||(o[2]=[Y("重置")])),_:1})])):J("",!0)]),_:1})}}},q=V(P,[["__scopeId","data-v-77584084"]]);export{q as P};
