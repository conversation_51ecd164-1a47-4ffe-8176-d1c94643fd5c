export default {
  route: {
    dashboard: "Dashboard",
    introduction: "Introduction",
    documentation: "Documentation",
    guide: "Guide",
    permission: "Permission",
    pagePermission: "Page Permission",
    directivePermission: "Directive Permission",
    icons: "Icons",
    components: "Components",
    componentIndex: "Introduction",
    tinymce: "Tinymce",
    markdown: "Markdown",
    jsonEditor: "JSON Editor",
    dndList: "Dnd List",
    splitPane: "SplitPane",
    avatarUpload: "Avatar Upload",
    dropzone: "Dropzone",
    sticky: "Sticky",
    countTo: "CountTo",
    componentMixin: "Mixin",
    backToTop: "BackToTop",
    dragDialog: "Drag Dialog",
    dragSelect: "Drag Select",
    dragKanban: "Drag Kanban",
    charts: "Charts",
    keyboardChart: "Keyboard Chart",
    lineChart: "Line Chart",
    mixChart: "Mix Chart",
    example: "Example",
    nested: "Nested Routes",
    menu1: "Menu 1",
    "menu1-1": "Menu 1-1",
    "menu1-2": "Menu 1-2",
    "menu1-2-1": "Menu 1-2-1",
    "menu1-2-2": "Menu 1-2-2",
    "menu1-3": "Menu 1-3",
    menu2: "Menu 2",
    Table: "Table",
    dynamicTable: "Dynamic Table",
    dragTable: "Drag Table",
    inlineEditTable: "Inline Edit",
    complexTable: "Complex Table",
    treeTable: "Tree Table",
    customTreeTable: "Custom TreeTable",
    tab: "Tab",
    form: "Form",
    createArticle: "Create Article",
    editArticle: "Edit Article",
    articleList: "Article List",
    errorPages: "Error Pages",
    page401: "401",
    page404: "404",
    errorLog: "Error Log",
    excel: "Excel",
    exportExcel: "Export Excel",
    selectExcel: "Export Selected",
    uploadExcel: "Upload Excel",
    zip: "Zip",
    exportZip: "Export Zip",
    theme: "Theme",
    clipboardDemo: "Clipboard",
    i18n: "I18n",
    externalLink: "External Link",
    menu: {
      appPanel: 'AppPanel',
      workbench: 'Workbench',
      system: 'System',
      company: 'Company',
      role: 'Role',
      roleDataPermission: 'Role Data Permission',
      user: 'User',
      permission: 'Permission',
      log: 'Log',
      processMethod: 'Process Method',
      app: 'Application',
      project: 'Project',
      backup: 'Backup',
      serviceState: 'Service State',
      announcement: 'ANC',
      aliasManagement: 'Alias Management',
      logPlotChart: 'Log Plot Chart',
      oil: 'Business',
      oilMap: 'Oil Map',
      oilField: 'Oil Field',
      oilfieldBlock: 'Oilfield Block',
      oilfieldBlockTabs: 'Block Detail Info',
      oilFieldTabs: 'Oil Field Detail Info',
      oilWellTabs: 'Well Detail Info',
      blockGeologicModel: 'Geologic Model',
      oilWell: 'Well',
      oilWellbore: 'Wellbore',
      oilWellboreTabs: 'Wellbore Detail Info',
      wellTrace: 'Well Trace',
      toolStringDetail: 'Tool String Detail',
      logDataFileInfo: 'Curve Object',
      logChannelData: 'Curve Data',
      logDataChart: 'Data Analysis',
      logChannelEdit: 'Curve Edit',
      multiWell: 'Multi-Well Comparison',
      otherWeb: 'iFrame',
      processingModule: 'Processing Module',
      dataPreprocess: 'Data Preprocessing',
      resource: 'Resource',
      locale: 'Domain',
      wellStandard: 'Logging Standard',
      colorPalettes: 'Color Palette',
      dhTool: 'Downhole Tool',
      dhToolLayout: 'Downhole Module',
      witsServer: 'Witsml Server',
      logPlotTemplate: 'Application Template',
      workflow: 'Workflow',
      workFlowProcessRun: 'Workflow Execution',
      downtask: 'Witsml Task',
      pyCodeSamples: 'Python Samples'
    }
  },
  navbar: {
    logOut: "Log Out",
    dashboard: "Dashboard",
    github: "Github",
    screenfull: "Screenfull",
    theme: "Theme",
    size: "Global Size",
    clearCache: "Clear Cache",
    changePassword: "Change Password",
    hello: "Hello",
  },
  login: {
    title: "Login Form",
    logIn: "Log in",
    username: "Username",
    password: "Password",
    any: "any",
    thirdparty: "Or connect with",
    thirdpartyTips:
      "Can not be simulated on local, so please combine you own business simulation! ! !",
  },
  documentation: {
    documentation: "Documentation",
    github: "Github Repository",
  },
  permission: {
    roles: "Your roles",
    switchRoles: "Switch roles",
  },
  guide: {
    description:
      "The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ",
    button: "Show Guide",
  },
  components: {
    documentation: "Documentation",
    tinymceTips:
      "Rich text editor is a core part of management system, but at the same time is a place with lots of problems. In the process of selecting rich texts, I also walked a lot of detours. The common rich text editors in the market are basically used, and the finally chose Tinymce. See documentation for more detailed rich text editor comparisons and introductions.",
    dropzoneTips:
      "Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.",
    stickyTips:
      "when the page is scrolled to the preset position will be sticky on the top.",
    backToTopTips1:
      "When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner",
    backToTopTips2:
      "You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally",
    imageUploadTips:
      "Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.",
  },
  table: {
    dynamicTips1: "Fixed header, sorted by header order",
    dynamicTips2: "Not fixed header, sorted by click order",
    dragTips1: "The default order",
    dragTips2: "The after dragging order",
    title: "Title",
    importance: "Imp",
    type: "Type",
    remark: "Remark",
    search: "Search",
    add: "Add",
    export: "Export",
    reviewer: "reviewer",
    id: "ID",
    date: "Date",
    author: "Author",
    readings: "Readings",
    status: "Status",
    actions: "Actions",
    edit: "Edit",
    publish: "Publish",
    draft: "Draft",
    delete: "Delete",
    cancel: "Cancel",
    confirm: "Confirm",
  },
  errorLog: {
    tips: "Please click the bug icon in the upper right corner",
    description:
      "Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.",
    documentation: "Document introduction",
  },
  excel: {
    export: "Export",
    selectedExport: "Export Selected Items",
    placeholder: "Please enter the file name(default excel-list)",
  },
  zip: {
    export: "Export",
    placeholder: "Please enter the file name(default file)",
  },
  theme: {
    change: "Change Theme",
    documentation: "Theme documentation",
    tips:
      "Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.",
  },
  tagsView: {
    refresh: "Refresh",
    close: "Close",
    closeOthers: "Close Others",
    closeAll: "Close All",
  },
  common: {
    confirmDeletion: "Are you sure to delete this?",
    confirmTitle: "Confirmation",
    failed: "Failed",
    success: "Success",
  },
  appPanel: {
    title: "i-Smart GS Application Desktop",
    subtitle: "One-stop management center, system desktop empowers business decisions, unlocks intelligent management experience",
    myApps: "My Apps",
    searchPlaceholder: "Search applications...",
    allCategories: "All Categories",
    recentProjects: "Recent Projects",
    allProjects: "All Projects",
    noProjects: "None",
    openProject: "Open Project",
    createProject: "Create New Project",
    projectName: "Project Name",
    wellbore: "Wellbore",
    lastEditor: "Last Editor",
    editDate: "Date",
    remark: "Remark",
    operation: "Operation",
    deleteProject: "Delete Project",
    saveChanges: "Save Changes",
    deleteMultiple: "Delete Multiple",
    confirmDelete: "Are you sure you want to delete the selected projects?",
    deleteSuccess: "Delete Successfully",
    deleteFailed: "Delete Failed",
    deleteCancel: "Delete Cancelled",
    selectProjectsToDelete: "Please select projects to delete",
    noChanges: "No Changes",
    searchProjects: "Please enter search keywords...",
    loading: "Loading...",
    noAppsInCategory: "No applications in this category",
    categoryName: "Category Name",
    appName: "Application Name",
    appDescription: "Application Description",
    enterApp: "Enter Application",
    viewDetails: "View Details",
    noSearchResults: "No matching applications found",
    searchResults: "Search Results",
    clearSearch: "Clear Search",
    totalApps: "Total {count} applications",
    appInfo: "Application Information",
    categoryInfo: "Category Information",
    refreshData: "Refresh Data",
    appNames: {
      preprocessing: 'Preprocessing',
      visualization3d: '3D Visualization',
      processingModule: 'Processing Module',
      geosteering: 'Geosteering',
      fastLogPlot: 'Fast Log Plot',
      multiWellCorrelation: 'Multi-well Correlation',
      pythonProcessing: 'Python Processing',
      dataPreprocessing: 'Data Preprocessing',
      pythonEditor: 'Python Editor'
    },
  },
  button: {
    add: "Add",
    confirm: "Ok",
    cancel: "Cancel",
    edit: " Update",
    delete: "Delete",
    enable: "Enabled",
    disable: "Disabled",
    create: "Create",
    failed: "Failed",
    success: "Success",
  },
  processMethod: {
    method: " method",
    methodName: "Method name",
    serial: "No.",
    category: "Category",
    icon: "Icon",
    serviceUrl: "Service url",
    version: "Version",
    lastModified: "Last modified",
    createTime: "Create time",
    status: "Status",
    remark: "Remark",
    inputMethodName: "Please input the method name",
    selectCategory: "Please select the category",
    parameterConfig: "Parameters",
    parameterName: "Name",
    parameterTitle: "Title",
    dataGroup: "Data group",
    dataType: "Data type",
    defValue: "Default value",
    zoned: "Zoned",
    unit: "Unit",
    unitClass: "Unit class",
    desc: "Description",
    min: "Min",
    max: "Max",
    optional: "Optional",
    inputCurveConfig: "Input curves",
    curveName: "Curve name",
    curveTitle: "Curve title",
    outputCurveConfig: "Output curves",
    curveType: "Curve type",
    inputCurveTab: "Input curves",
    parameterTab: "Parameters",
    outputCurveTab: "Output curves",
    settingsTab: "Settings",
    dataset: "Select dataset: ",
    selectDataset: "Please select a dataset",
    nameColumn: "Name",
    valueColumn: "Value",
    unitColumn: "Unit",
    searchParameters: "Search parameters:",
    search: "search",
    depthRange: "Depth range:",
    samplingInterval: "Sampling interval:",
    reset: "Reset",
    titleColumn: "Title",
  },
  other: {
    selectImage: "Select Image",
    moreIcon: "More Icon",
  },
  announcement: {
    title: "Announcement Management",
    list: "Announcement List",
    create: "Create Announcement",
    edit: "Edit Announcement",
    delete: "Delete Announcement",
    publish: "Publish Announcement",
    withdraw: "Withdraw Announcement",
    detail: "Announcement Detail",
    // Table fields
    messageId: "Message ID",
    messageType: "Message Type",
    systemMessage: "System Message",
    publicMessage: "Public Message",
    announcementTitle: "Message Title",
    content: "Content",
    senderName: "Sender",
    receiverType: "Receiver Type",
    targetUser: "Specific User",
    targetAll: "All Users",
    priority: "Priority",
    status: "Status",
    targetType: "Target Users",
    targetUsers: "Specific Users",
    targetRoles: "Specific Roles",
    effectiveTime: "Effective Time",
    expireTime: "Expire Time",
    expiryTime: "Expire Time",
    isTop: "Pinned",
    createTime: "Create Time",
    createdOn: "Create Time",
    sentTime: "Sent Time",
    createBy: "Created By",
    updateTime: "Update Time",
    // Priority options
    priorityLow: "Low",
    priorityNormal: "Normal",
    priorityImportant: "Important",
    priorityUrgent: "Urgent",
    priorityHigh: "High",
    // Status options
    statusDraft: "Draft",
    statusPublished: "Published",
    // Target user types
    targetAll: "All Users",
    targetSpecificUsers: "Specific Users",
    targetSpecificRoles: "Specific Roles",
    // Form labels
    titlePlaceholder: "Please enter announcement title (max 15 chars)",
    contentPlaceholder: "Please enter announcement content (max 100 chars)",
    selectPriority: "Please select priority",
    selectTargetType: "Please select target users",
    selectUsers: "Please select users",
    selectRoles: "Please select roles",
    selectEffectiveTime: "Select effective time",
    selectExpireTime: "Select expire time",
    saveDraft: "Save as Draft",
    publishNow: "Publish Now",
    // Action buttons
    markRead: "Mark as Read",
    markAllRead: "Mark All as Read",
    viewAll: "View All",
    sendMessage: "Send Announcement",
    send: "Send",
    // Validation messages
    titleRequired: "Announcement title is required",
    titleMaxLength: "Title cannot exceed 200 characters",
    contentRequired: "Announcement content is required",
    priorityRequired: "Please select priority",
    targetTypeRequired: "Please select target users",
    effectiveTimeRequired: "Please select effective time",
    expireTimeRequired: "Please select expiry time",
    expireTimeCannotBeEarlier: "Expiry time cannot be earlier than current time",
    // Confirmation messages
    confirmPublish: "Are you sure to publish this announcement?",
    confirmWithdraw: "Are you sure to withdraw this announcement?",
    confirmDelete: "Are you sure to delete this announcement?",
    confirmBatchDelete: "Are you sure to delete selected announcements?",
    confirmSendMessage: "Are you sure to send this announcement?",
    confirmUpdateMessage: "Are you sure to update this announcement?",
    publishSuccess: "Published successfully",
    withdrawSuccess: "Withdrawn successfully",
    sendSuccess: "Sent successfully",
    sendFailed: "Failed to send",
    updateSuccess: "Updated successfully",
    updateFailed: "Update failed",
    deleteSuccess: "Deleted successfully",
    deleteFailed: "Delete failed",
    batchDeleteSuccess: "Batch delete successful",
    batchDeleteFailed: "Batch delete failed",
    selectDeleteRows: "Please select records to delete",
    markReadSuccess: "Marked successfully",
    markAllReadSuccess: "All marked as read",
    operationFailed: "Operation failed",
    loadFailed: "Failed to load announcements",
    // Notification related
    notificationTitle: "Announcement Notifications",
    noAnnouncement: "No announcements",
    unreadCount: " unread",
    timeAgo: {
      minutesAgo: " minutes ago",
      hoursAgo: " hours ago",
      daysAgo: " days ago"
    },
    // Others
    yes: "Yes",
    no: "No",
    all: "All",
    requireConfirmation: "Require Confirmation",
    downloadReport: "Download Report",
    downloadCount: "Download Count",
    config: "Config",
    downloadCountPlaceholder: "Download count",
    // Demo related
    demo: {
      features: "Features",
      realTimeNotification: "Real-time Notification",
      realTimeNotificationDesc: "Support real-time announcement push and unread message reminders to ensure important information is delivered promptly",
      darkMode: "Dark Theme",
      darkModeDesc: "Perfect adaptation to dark and light themes, providing a comfortable visual experience",
      i18n: "Internationalization",
      i18nDesc: "Support Chinese/English switching, interface language automatically adjusts with system settings",
      management: "Full Management",
      managementDesc: "Provide complete announcement creation, editing, publishing, withdrawal and other management functions",
      goToManagement: "Go to Management",
      simulateNotification: "Simulate Notification",
      simulationMessage: "This is a simulated announcement notification demo"
    },
    titleLength: 'Title cannot exceed 15 characters',
    contentLength: 'Content cannot exceed 100 characters',
  },
  common: {
    confirmDeletion: "Are you sure to delete this?",
    confirmTitle: "Confirmation",
    failed: "Failed",
    success: "Success",
    demo: "Demo",
    lightMode: "Light",
    darkMode: "Dark",
    languageChanged: "Language changed successfully",
    themeChanged: "Theme changed successfully"
  },
  workbench: {
    welcome: 'Welcome back',
    passwordExpire: 'Password will expire in {days} days',
    more: 'More',
    totalWell: 'Total Wells',
    yearWell: 'Year Wells',
    monthWell: 'Month Wells',
    wellboreList: 'Wellbore List',
    projectList: 'Recent Projects',
    appList: 'App List',
    messageList: 'Message List',
    system: 'System',
    announcement: 'Announcement',
    systemMsg: 'System',
    wellboreName: 'Wellbore Name',
    workingWellbore: 'Working Wellbore',
    fieldName: 'Field',
    wellName: 'Well',
    startDate: 'Start Date',
    area: 'Area',
    block: 'Block',
    remark: 'Remark',
    upload: 'Upload',
    operation: 'Action',
    updateTime: 'Update Date',
    app: 'App',
    projectName: 'Project Name',
    noProjects: 'No projects',
    createProject: 'Create Project',
    refreshProject: 'Refresh Project List',
    editor: 'Editor',
    createTime: 'Create Time',
    unknown: 'Unknown',
    loading: 'Loading...',
    open: 'Open',
    detail: 'Detail',
    noData: 'No Data',
    uploadSuccess: 'Upload Success',
    uploadFail: 'Upload Failed',
    openProjectFail: 'Project info incomplete, cannot open',
    appDetail: 'App Detail',
    myProjects: 'My Projects',
    projectCount: '{count} Projects',
    unitCount: 'Unit',
  },
  company: {
    index: 'Index',
    action: 'Action',
    edit: 'Edit',
    setArea: 'Set Area',
    delete: 'Delete',
    company: 'Company',
    companyName: 'Name',
    address: 'Address',
    linkMan: 'Contact',
    email: 'Email',
    officePhone: 'Phone',
    cancel: 'Cancel',
    confirm: 'Confirm',
    addArea: 'Add Area',
    downloadReport: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    companyNameRequired: 'Company name is required',
    emailValid: 'Please enter a valid email',
  },
  role: {
    index: 'Index',
    action: 'Action',
    edit: 'Edit',
    delete: 'Delete',
    setPagePermission: 'Set Role Permission',
    setDataPermission: 'Set Data Permission',
    role: 'Role',
    roleName: 'Name',
    remark: 'Remark',
    add: 'Add',
    download: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    roleNameRequired: 'Role name is required',
    pageName: 'Page Name',
    dataPermission: 'Data Permission',
    all: 'Select All',
    addData: 'Add Data',
    viewSelf: 'View Own Data',
    editSelf: 'Edit Own Data',
    deleteSelf: 'Delete Own Data',
    viewDept: 'View Department Data',
    editDept: 'Edit Department Data',
    deleteDept: 'Delete Department Data',
    viewCompany: 'View Company Data',
    editCompany: 'Edit Company Data',
    deleteCompany: 'Delete Company Data',
    viewPublic: 'View Public Data',
    editPublic: 'Edit Public Data',
    deletePublic: 'Delete Public Data',
    viewAll: 'View All Data',
    editAll: 'Edit All Data',
    deleteAll: 'Delete All Data',
    cancel: 'Cancel',
    confirm: 'Confirm',
    refresh: 'Refresh',
  },
  user: {
    index: 'Index',
    action: 'Action',
    edit: 'Edit',
    delete: 'Delete',
    setRole: 'Set User Role',
    lock: 'Lock',
    unlock: 'Unlock',
    company: 'Company',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    isLocked: 'Locked',
    yes: 'Yes',
    no: 'No',
    add: 'Add',
    download: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    userNameRequired: 'Name is required',
    emailValid: 'Please enter a valid email',
    passwordRequired: 'Password must be at least 6 characters',
    phoneRequired: 'Phone is required',
    cancel: 'Cancel',
    confirm: 'Confirm',
    refresh: 'Refresh',
    all: 'Select All',
    role: 'Role',
  },
  log: {
    login: 'Login Log',
    loginTable: 'Login Log Table',
    loginRanking: 'Login Ranking',
    module: 'Module Browsing Log',
    moduleRanking: 'Browsing Ranking',
    data: 'Data Operation Log',
    dataRanking: 'Operation Ranking',
    error: 'System Error Log',
    type: 'Type',
    loginUserName: 'Login Name',
    userName: 'Name',
    cIP: 'Source IP',
    dLoginDate: 'Operation Time',
    cClientInfo: 'Client Info',
    cComputeName: 'Computer Name',
    operationTime: 'Operation Time',
    operationDocName: 'Operation Set',
    result: 'Result',
    errorTime: 'Error Time',
    url: 'URL',
    message: 'Error Message',
    browsingTime: 'Browsing Time',
    moduleName: 'Module Name',
  },
  appManage: {
    index: 'Index',
    action: 'Action',
    edit: 'Edit',
    delete: 'Delete',
    appName: 'App Name',
    category: 'Category',
    icon: 'Icon',
    order: 'Order',
    url: 'App URL',
    deleteUrl: 'Delete URL',
    isInSite: 'Internal Link',
    yes: 'Yes',
    no: 'No',
    remark: 'Remark',
    download: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    add: 'Add',
    cancel: 'Cancel',
    confirm: 'Confirm',
    appNameRequired: 'App name is required',
    categoryRequired: 'Category is required',
    urlRequired: 'App URL is required',
    isInSiteRequired: 'Internal link is required',
    orderRequired: 'Order is required',
  },
  project: {
    index: 'Index',
    action: 'Action',
    delete: 'Delete',
    refresh: 'Refresh',
    download: 'Download',
    downloadReport: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    cancel: 'Cancel',
    confirm: 'Confirm',
    projectName: 'Project Name',
    appId: 'App Name',
    projectType: 'Category',
    wellboreId: 'Wellbore',
    createTime: 'Create Time',
    lastModifyTime: 'Last Modified',
    remark: 'Remark',
    inputKeyword: 'Please enter keyword',
  },
  backupManage: {
    index: 'Index',
    action: 'Action',
    edit: 'Edit',
    delete: 'Delete',
    refresh: 'Refresh',
    download: 'Export',
    downloadReport: 'Download Report',
    downloadCount: 'Download Count',
    downloadCountPlaceholder: 'Download Count',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    createFull: 'Create System Full Backup',
    createIncremental: 'Create System Incremental Backup',
    uploadRestore: 'Upload File Restore',
    deleteBatch: 'Delete Multiple',
    searchPlaceholder: 'Please enter keyword',
    backupName: 'Backup Name',
    backupType: 'Type',
    backupMethod: 'Method',
    state: 'Status',
    progress: 'Progress(%)',
    description: 'Remark',
    createTime: 'Backup Time',
    modify: 'Modify',
    downloadLog: 'Download Log File',
    downloadDb: 'Download Database',
    restore: 'Restore',
    startBackup: 'Start Backup',
    startRestore: 'Start Restore',
    startUpload: 'Start Restore',
    setup: 'Schedule Setup',
    intervalDays: 'Interval Days',
    startTime: 'Scheduled Time',
    startDate: 'Start Date',
    enable: 'Enable',
    fileName: 'File Name',
    selectFile: 'Select File',
    fileType: 'Type',
    system: 'System',
    well: 'Well',
    notSelected: 'No file selected',
    uploadTip: 'Please upload .zip file',
    backupNameRequired: 'Backup name is required',
    intervalDaysRequired: 'Interval days is required',
    startTimeRequired: 'Scheduled time is required',
    startDateRequired: 'Start date is required',
    fileTypeRequired: 'Type is required',
    fileRequired: 'File is required',
    createFullTitle: 'Create System Full Backup',
    createIncrementalTitle: 'Create System Incremental Backup',
    editTitle: 'Edit Info',
    restoreFullTitle: 'Restore System Full Backup',
    restoreIncrementalTitle: 'Restore System Incremental Backup',
    restoreWellFullTitle: 'Restore Well Full Backup',
    restoreWellIncrementalTitle: 'Restore Well Incremental Backup',
    unknownType: 'Unknown Backup Type',
    restoreTip: 'Restore Tip',
    restoreStartTip: 'Start restoring, please wait for completion...',
    restoreSuccess: 'Restore Success',
    restoreFailed: 'Restore Failed',
    backupSuccess: 'Backup Success',
    backupFailed: 'Backup Failed',
    backupNameExist: 'Backup name already exists',
    uploadFailed: 'Upload failed, please complete the upload info',
    confirmRestore: 'Restoring will clear all data modified after the backup, are you sure to restore?',
    confirmRestoreTitle: 'Confirm Restore',
    select: 'Select',
    systemFull: 'System Full',
    systemIncremental: 'System Incremental',
    tipNoFullBackup: 'Tip: The base full backup does not exist',
    tipBasedOnFull: 'Tip: Based on full backup',
    tipRestoreClear: 'Tip: Restoring will clear all data modified after the backup, are you sure to restore?',
    backupListTab: 'Backup List',
    restoreLogTab: 'Restore Log',
    restoreBackupName: 'Restore Backup Name',
    operationName: 'Operator',
    restoreTime: 'Restore Time',
    inputKeyword: "Please input keyword",
    backup: "Backup",
    restore: "Restore",
    fullBackup: "Full",
    incrementalBackup: "Incremental",
    basedOnBackup: "Based on Backup",
    startBackup: "Start Backup",
    startRestore: "Start Restore",
    progress: "Progress",
    restoreWarning: "Warning: Restore operation will overwrite existing data, please operate carefully!"
  },
  resource: {
    // Common
    common: {
      operation: "Actions",
      edit: "Edit",
      delete: "Delete",
      view: "Details",
      add: "Add",
      refresh: "Refresh",
      download: "Download",
      export: "Export",
      import: "Import",
      upload: "Upload",
      search: "Search",
      cancel: "Cancel",
      confirm: "Confirm",
      save: "Save",
      reset: "Reset",
      back: "Back",
      close: "Close",
      submit: "Submit",
      clear: "Clear",
      select: "Select",
      selectAll: "Select All",
      batchDelete: "Batch Delete",
      batchImport: "Batch Import",
      batchExport: "Batch Export",
      sequence: "No.",
      name: "Name",
      type: "Type",
      category: "Category",
      description: "Description",
      remark: "Remark",
      note: "Note",
      createTime: "Create Time",
      updateTime: "Update Time",
      status: "Status",
      enabled: "Enabled",
      disabled: "Disabled",
      pleaseInput: "Please input",
      pleaseSelect: "Please select",
      downloadCount: "Download Count",
      downloadReport: "Download Report"
    },
    // Witsml Server
    witsServer: {
      title: "Witsml Server",
      serverName: "Server Name",
      serverType: "Server Type",
      address: "Address",
      username: "Username",
      password: "Password",
      token: "Token",
      serverNameRequired: "Server name is required",
      serverTypeRequired: "Server type is required",
      addressRequired: "Address is required",
      usernameRequired: "Username is required",
      passwordRequired: "Password is required",
      tokenRequired: "Token is required"
    },
    // Python Code Samples
    pyCodeSamples: {
      title: "Code Sample",
      sampleType: "Sample Type",
      sampleName: "Sample Name", 
      sampleCode: "Code Content",
      sampleTypeRequired: "Sample type is required",
      sampleNameRequired: "Sample name is required",
      inputName: "Please input name"
    },
    // Download Task
    downtask: {
      title: "Download Task",
      server: "Server",
      serverName: "Server Name",
      serverWell: "Server Well",
      serverWellbore: "Server Wellbore",
      log: "Log",
      localWell: "Local Well",
      localWellbore: "Local Wellbore",
      interval: "Interval(s)",
      caller: "Caller",
      originalWellName: "Original Well Name",
      originalWellboreName: "Original Wellbore Name",
      localWellboreName: "Local Wellbore",
      dataName: "Data Name",
      downloadType: "Download Type",
      realtimeDownload: "Realtime Download",
      historicalDownload: "Historical Download",
      startTime: "Start Time",
      endTime: "End Time",
      downloadHistoricalData: "Download Historical Data",
      monitorRealtimeData: "Monitor Realtime Data",
      wellTrajectoryDownloadSettings: "Well Trajectory Download Settings",
      originalStartDepth: "Original Start Depth",
      originalEndDepth: "Original End Depth",
      newStartDepth: "New Start Depth",
      newEndDepth: "New End Depth",
      replace: "Replace",
      merge: "Merge",
      depth: "Depth",
      serverRequired: "Server is required",
      serverWellRequired: "Server well is required",
      serverWellboreRequired: "Server wellbore is required",
      logRequired: "Log is required",
      localWellRequired: "Local well is required",
      localWellboreRequired: "Local wellbore is required",
      curveName: "Curve Name",
      curveUnit: "Curve Unit",
      startIndex: "Start Index",
      endIndex: "End Index",
      indexSpacing: "Index Spacing",
      duration: "Duration",
      dataSize: "Data Size"
    },
    // Locale
    locale: {
      title: "Domain",
      lithSymbol: "Lithology Symbol",
      logResultSymbol: "Log Result Symbol",
      icon: "Icon",
      nameRequired: "Name is required"
    },
    // Downhole Tool
    dhTool: {
      title: "Downhole Tool",
      manufacturer: "Manufacturer",
      seriesNo: "Serial No.",
      logo: "Logo",
      length: "Length",
      diameter: "Diameter",
      weight: "Weight",
      maxPressure: "Max Pressure",
      maxTemp: "Max Temperature",
      plotImage: "Plot Image",
      plotScaleX: "Plot Scale X",
      plotScaleY: "Plot Scale Y",
      mirror: "Mirror",
      turn: "Turn",
      plotHeightMin: "Min Plot Height",
      plotHeightMax: "Max Plot Height",
      plotCenterY: "Plot Center Y",
      plotXoffset: "Plot X Offset",
      measureOrigin: "Measure Origin",
      nameRequired: "Name is required",
      inputNameTypeCategory: "Please input name, type, category"
    },
    // Color Palettes
    colorPalettes: {
      title: "Color Palette",
      paletteName: "Palette Name",
      color: "Color",
      setColor: "Set Palette Color",
      importPalette: "Import Palette",
      uploadXmlFile: "Upload XML file",
      pleaseUploadXml: "Please upload .xml file",
      paletteNameRequired: "Palette name is required",
      inputPaletteNameOrCategory: "Please input palette name or category",
      pleaseSelectFile: "Please select a file to upload",
      fileNameTooLong: "File name is too long, cannot upload"
    },
    // Workflow
    workflow: {
      title: "Workflow",
      workflowModule: "Workflow Module",
      flowChart: "Flow Chart",
      input: "Input",
      output: "Output",
      parameter: "Parameter",
      addParameter: "Add Parameter",
      title_: "Title",
      defaultValue: "Default Value",
      value: "Value",
      visible: "Visible",
      up: "Move Up",
      down: "Move Down",
      execute: "Execute",
      generalWorkflow: "General Workflow",
      instrumentWorkflow: "Instrument Workflow",
      instrument: "Instrument",
      nameRequired: "Name is required",
      titleRequired: "Title is required",
      defaultValueRequired: "Default value is required",
      typeRequired: "Type is required",
      // Module related
      module: {
        abbreviation: "Abbreviation",
        appCategory: "App Category"
      },
      // Process related
      process: {
        toolName: "Tool Name",
        dhToolId: "Type"
      }
    },
    // Well Standard
    wellStandard: {
      title: "Well Standard",
      bitStandard: "Bit Standard",
      casingStandard: "Casing Standard",
      bitName: "Bit Name",
      bitSize: "Bit Size(inch)",
      bitNameRequired: "Bit name is required",
      bitSizeRequired: "Bit size must be greater than 1",
      casingName: "Casing Name",
      casingSize: "Casing Size(inch)",
      outDiameter: "Outer Diameter(inch)",
      weight: "Weight(kg)",
      casingNameRequired: "Casing name is required"
    },
    // Lithology Symbol
    lithSymbol: {
      title: "Lithology Symbol",
      code: "Code",
      chineseName: "Chinese Name",
      integerValue: "Integer Value",
      width: "Width",
      height: "Height",
      icon: "Icon",
      svg: "SVG",
      selectFile: "Select File",
      uploadZipFile: "Please upload .zip file, svg and png file names should be named with id",
      uploadXmlFile: "Please upload .xml",
      codeRequired: "Code is required",
      nameRequired: "Name is required",
      inputKeyword: "Please input keyword"
    },
    // Log Result Symbol
    logResultSymbol: {
      title: "Log Result Symbol",
      code: "Code",
      chineseName: "Chinese Name",
      integerValue: "Integer Value",
      width: "Width",
      height: "Height",
      icon: "Icon",
      svg: "SVG",
      codeRequired: "Code is required",
      nameRequired: "Name is required"
    }
  },
  // Oil Module
  oil: {
    // Common fields
    common: {
      actions: "Actions",
      operation: "Actions",
      edit: "Edit",
      delete: "Delete",
      view: "View",
      add: "Add",
      refresh: "Refresh",
      download: "Download",
      export: "Export",
      import: "Import",
      upload: "Upload",
      search: "Search",
      cancel: "Cancel",
      confirm: "Confirm",
      save: "Save",
      reset: "Reset",
      back: "Back",
      close: "Close",
      submit: "Submit",
      clear: "Clear",
      select: "Select",
      selectAll: "Select All",
      batchDelete: "Batch Delete",
      batchImport: "Batch Import",
      batchExport: "Batch Export",
      sequence: "No.",
      name: "Name",
      type: "Type",
      category: "Category",
      description: "Description",
      remark: "Remark",
      note: "Note",
      createTime: "Create Time",
      updateTime: "Update Time",
      inputPerson: "Input Person",
      status: "Status",
      enabled: "Enabled",
      disabled: "Disabled",
      success: "Success",
      failed: "Failed",
      tip: "Tip",
      pleaseInput: "Please input",
      pleaseSelect: "Please select",
      downloadCount: "Download Count",
      downloadReport: "Download Report",
      inputKeyword: "Please input keyword",
      backup: "Backup",
      restore: "Restore",
      purpose: "Purpose",
      wellFluid: "Well Fluid",
      fullBackup: "Full Backup",
      incrementalBackup: "Incremental Backup",
      backupName: "Backup Name",
      restoreWarning: "Warning: Restore operation will overwrite existing data, please operate carefully!",
      startRestore: "Start Restore",
      startBackup: "Start Backup",
      progress: "Progress",
      save: "Save",
      paletteColors: "Palette Colors",
      clickToSave: "Click to save changes",
      confirmDelete: "Are you sure to delete?",
      confirmDeleteTitle: "Confirm Delete",
      deleteSuccess: "Delete Success",
      deleteCancelled: "Delete Cancelled",
      noSelection: "No Selection",
      batchDeleteConfirm: "This will delete {count} selected items, continue?",
      confirmSave: "Are you sure to save changes?",
      deleteBatch: "Delete Batch",
      enterCorrectDownloadCount: "Enter correct download count",
      deleteConfirm: "Are you sure to delete?",
      // textMap related
      create: "Create",
      update: "Edit",
      config: "Config",
      fullWidth: "Full Width",
      proportionalDisplay: "Proportional Display",
      // File related
      selectFile: "Select File",
      fileRequired: "File upload required",
      fileNotSelected: "No file selected",
      modelNameRequired: "Model name is required",
      categoryRequired: "Category is required",
      pleaseSelectOrInputCategory: "Please select or input category",
      blockNameRequired: "Block name is required",
      oilFieldRequired: "Oil field is required",
      colorPaletteRequired: "Color palette is required",
      structuralModelColorPalette: "Structural Model Color Palette",
      blockName: "Block Name",
      colorPalette: "Color Palette",
      structuralColorPalette: "Structural Color Palette",
      fileUploadRequired: "File is required",
      downloadFailed: "Download Failed",
      block: "Block",
      geologicModel: "Geologic Model",
      geologicModelTitle: "Geologic Model",
      modelName: "Model Name",
      model: "Model",
      inputBy: "Input By",
      pleaseSelectUploadFile: "Please select file to upload",
      pleaseSelectFile: "Please select file",
      pleaseSelectUnit: "Please select unit",
      pleaseSelectDepthColumn: "Please select depth column",
      pleaseSelectIncColumn: "Please select inc column",
      pleaseSelectAziColumn: "Please select azi column",
      pleaseSelectUpDownCurves: "Please select up and down curves",
      selectUnitRequired: "Please select unit",
      selectDepthDataColumn: "Please select depth data column",
      selectIncDataColumn: "Please select inc data column",
      selectAziDataColumn: "Please select azi data column"
    },
    // Oil Field
    oilField: {
      title: "Oil Field",
      oilFieldName: "Oil Field Name",
      oilFieldCode: "Oil Field Code",
      company: "Company",
      area: "Area",
      country: "Country",
      province: "Province",
      city: "City",
      county: "County",
      oilFieldNameRequired: "Oil field name is required",
      areaRequired: "Area is required",
      companyRequired: "Company is required",
      selectCompany: "Select Company",
      selectArea: "Select Area"
    },
    // Oil Well
    oilWell: {
      title: "Well",
      wellName: "Well Name",
      wellCode: "Well Code",
      oilField: "Oil Field",
      block: "Block",
      xCoordinates: "X Coordinates(m)",
      yCoordinates: "Y Coordinates(m)",
      longitude: "Longitude(°)",
      latitude: "Latitude(°)",
      kb: "KB Elevation(m)",
      wellCategory: "Category",
      wellNameRequired: "Well name is required",
      oilFieldRequired: "Oil field is required",
      blockRequired: "Block is required",
      selectOilField: "Select Oil Field",
      selectBlock: "Select Block",
      town: "Town",
      location: "Location",
      altitude: "Altitude(m)",
      drillFloorHeight: "Drill Floor Height(m)",
      complCoreHeight: "Completion Core Height(m)",
      magneticDeclination: "Magnetic Declination",
      mudNature: "Mud Nature",
      mudDensity: "Mud Density",
      mudViscosity: "Mud Viscosity",
      mudResistivity: "Mud Resistivity",
      wellDepth: "Well Depth",
      inputBy: "Input By",
      startDate: "Start Date",
      endDate: "End Date",
      drillingMF: "Drilling Measurement Reference",
      rigType: "Rig Type",
      rigName: "Rig Name"
    },
    // Wellbore
    oilWellbore: {
      title: "Wellbore",
      wellboreName: "Wellbore Name",
      wellboreNumber: "Wellbore Name",
      belongWell: "Belong Well",
      platform: "Platform",
      wellboreType: "Wellbore Type",
      shape: "Well Type",
      purpose: "Purpose",
      wellboreNameRequired: "Wellbore name is required",
      belongWellRequired: "Belong well is required",
      inputWellboreName: "Please input wellbore name for fuzzy search",
      // Well type options
      straight: "Straight",
      horizontal: "Horizontal",
      directional: "Directional",
      deviated: "Deviated",
      // Purpose options
      evaluation: "Evaluation",
      exploration: "Exploration",
      development: "Development",
      // Detail page fields
      company: "Company",
      toolNo: "Tool No.",
      area: "Area",
      footage: "Footage",
      oilField: "Oil Field",
      drillingRate: "Drilling Rate(%)",
      well: "Well",
      bha: "BHA",
      dtf: "Drilling Tool Function",
      mdPlan: "Planned MD(m)",
      caliper: "Caliper",
      tvdPlan: "Planned TVD(m)",
      actualMD: "Actual MD(m)",
      maxTVD: "Max TVD(m)",
      startDate: "Start Date",
      dataSet: "Dataset",
      block: "Block",
      // Well trajectory related
      wellTrajectoryData: "Well Trajectory Data",
      designTrajectory: "Design Trajectory",
      actualTrajectory: "Actual Trajectory", 
      uploaded: "Uploaded",
      notUploaded: "Not Uploaded",
      upload: "Upload",
      view: "View",
      clear: "Clear",
      uploadWellboreModel: "Upload Wellbore Model",
      uploadObjFile: "Upload OBJ File",
      uploadMtlFile: "Upload MTL File",
      image: "Image",
      pleaseUploadObjFile: "Please upload .obj file",
      pleaseUploadMtlFile: "Please upload .mtl file",
      pleaseUploadImageFile: "Please upload .jpg,.png,.jpeg",
      selectTrajectoryFile: "Select Trajectory File",
      pleaseUploadTrajectoryFile: "Please upload .las|.csv|.txt file",
      uploadWellboreTrajectory: "Upload Wellbore Trajectory",
      trajectoryData: "Trajectory Data",
      replace: "Replace",
      merge: "Merge",
      unit: "Unit",
      md: "MD",
      incl: "Inc",
      azimuth: "Azimuth",
      kbElevation: "KB Elevation",
      modelFile: "Model File",
      fileType: "File Type",
      fileName: "File Name",
      noModelFileUploaded: "No model file uploaded.",
      fileNameTooLong: "File name too long, cannot upload",
      clearWellboreTrajectoryData: "This will clear the {type} trajectory data of this wellbore, continue?",
      updateTrajectoryUploadStatus: "Update trajectory upload status",
      design: "Design",
      actual: "Actual",
      confirmDeleteTrajectory: "Confirm Delete",
      deleteCancelled: "Delete Cancelled"
    },
    // Job
    oilJob: {
      title: "Job",
      jobName: "Job Name",
      jobType: "Category",
      purpose: "Purpose",
      status: "Status",
      startTime: "Start Time",
      endTime: "End Time",
      planEndTime: "Plan End Time",
      startDepth: "Start Depth",
      endDepth: "End Depth",
      mdZero: "MD Zero Position",
      bitSize: "Bit Size",
      maxHoleDev: "Max Hole Dev",
      maxDevAzim: "Max Dev Azim",
      company: "Company",
      equipment: "Equipment",
      teamLeader: "Team Leader",
      recordedBy: "Recorded By",
      witnessedBy: "Witnessed By",
      operator: "Operator",
      toolString: "Tool String",
      toolStringConfig: "Tool String Config",
      toolStringDetail: "Tool String Detail",
      editFluid: "Edit Fluid",
      configToolString: "Config Tool String",
      editFluidData: "Edit Fluid Data",
      // Fluid related
      fluidType: "Fluid Type",
      maxTemp: "Max Temperature",
      sourceOfSample: "Source of Sample",
      salinity: "Salinity",
      density: "Density",
      viscosity: "Viscosity",
      fluidLoss: "Fluid Loss",
      ph: "PH Value",
      circStopTime: "Circ Stop Time",
      logBottomTime: "Log Bottom Time",
      rm: "Rm",
      rmf: "Rmf",
      rmc: "Rmc",
      rmAtBht: "Rm at BHT",
      rmfAtBht: "Rmf at BHT",
      rmcAtBht: "Rmc at BHT",
      bht: "BHT",
      sourceOfRmf: "Source of Rmf",
      sourceOfRmc: "Source of Rmc",
      comments: "Comments",
      // Validation messages
      jobNameRequired: "Job name is required",
      startTimeRequired: "Start time is required",
      startDepthRequired: "Start depth is required",
      endDepthRequired: "End depth is required",
      // Job type options
      drilling: "Drilling",
      logging: "Logging",
      completion: "Completion",
      other: "Other",
      // Purpose options  
      evaluation: "Evaluation",
      exploration: "Exploration",
      development: "Development",
      // Status options
      active: "Active",
      activeInjection: "Active-Injection",
      activeProduction: "Active-Production",
      completed: "Completed",
      partiallyPlugged: "Partially Plugged",
      pluggedAndAbandoned: "Plugged & Abandoned",
      abandoned: "Abandoned"
    },
    // Oilfield Block
    oilfieldBlock: {
      title: "Oilfield Block",
      blockName: "Block Name",
      oilField: "Oil Field",
      colorPalette: "Color Palette",
      structuralPalette: "Structural Palette",
      geologicModel: "Geologic Model",
      blockWellList: "Block Well List",
      inputBlockNameOrAddress: "Enter block name or address",
      selectOilField: "Select Oil Field",
      selectStructuralPalette: "Select Structural Palette",
      blockNameRequired: "Block name is required",
      oilFieldRequired: "Oil field is required",
      notes: "Notes",
      address: "Address"
    },
    // Well Layer Top Sets
    wellLayerTopSets: {
      title: "Well Layer Top Sets",
      layerSetName: "Layer Set Name",
      purpose: "Purpose",
      notes: "Notes",
      markerLayerSet: "Marker Layer Set",
      layerSetNameRequired: "Layer set name is required",
      // Marker layer related
      markerLayer: "Marker Layer",
      layerName: "Layer Name",
      md: "MD",
      thick: "Thickness",
      memo: "Memo",
      layerNameRequired: "Layer name is required",
      selectMarkerLayerFile: "Select Marker Layer File",
      selectTxtFile: "Select TXT File",
      uploadMarkerLayer: "Upload Marker Layer",
      uploadMode: "Upload Mode",
      replace: "Replace",
      merge: "Merge",
      topName: "Top Name",
      depth: "Depth",
      thickness: "Thickness",
      saveModification: "Save Modification",
      exportData: "Export",
      uploadData: "Upload",
      duplicateLayerName: "Duplicate layer names exist in the list, cannot submit changes, please modify and try again"
    },
    // File Attach
    fileAttach: {
      title: "File Attach",
      fileName: "File Name",
      fileSize: "File Size",
      fileType: "File Type",
      uploadTime: "Upload Time",
      selectFile: "Select File",
      attachType: "Category",
      material: "Material",
      fileRequired: "File is required",
      // Attachment type options
      autoDetect: "Auto Detect",
      image: "Image",
      textDocument: "Text Document",
      wordDocument: "Word Document",
      excelDocument: "Excel Document",
      compressedFile: "Compressed File",
      otherType: "Other Type",
      // Validation messages
      fileNameRequired: "File name is required",
      categoryRequired: "Category is required",
      sizeRequired: "Size is required",
      // Dialog
      downloadReport: "Download Report",
      downloadCount: "Download Count",
      enterDownloadCount: "Enter download count"
    },
    // Log Data File
    logDataFile: {
      title: "Log Data File",
      dataFile: "Data File",
      dataType: "Data Type",
      dataFormat: "Data Format",
      curveCount: "Curve Count",
      dataset: "Dataset",
      datasetName: "Dataset Name",
      relatedJob: "Related Job",
      selectRelatedJob: "Select Related Job",
      runNumber: "RUN Number",
      isGrowing: "Data is Growing",
      dtbCurtainRange: "DTBCurtain Range(m) +/-",
      interpolationImaging: "Interpolation Imaging",
      interpolationSettings: "Interpolation Settings",
      curveName: "Curve Name",
      outputCurveName: "Output Curve Name",
      upCurve: "Up",
      downCurve: "Down",
      averageCurve: "Average",
      addToUp: "Add to Up",
      addToDown: "Add to Down",
      addToAverage: "Add to Average",
      clearSelection: "Clear Selection",
      inputCurveName: "Enter curve name",
      // Validation messages
      datasetNameRequired: "Dataset name is required",
      // File format support
      supportedFormats: "Support .las|.edx|.wis|.lis|.cff|.wtf|.xtf|.dlis formats",
      // Interpolation imaging related
      curveList: "Curve List",
      noCurveData: "No curve data",
      selectUpCurve: "Select up curve from left",
      selectDownCurve: "Select down curve from left",
      selectAverageCurve: "Select average curve from left",
      averageCurveEmpty: "Average curve is empty, continue?",
      submitCancelled: "Submit cancelled",
      pleaseSelectFile: "Please select file",
      // Dialog
      downloadReport: "Download Report",
      downloadCount: "Download Count",
      enterDownloadCount: "Enter download count"
    },
    // Well Trace
    wellTrace: {
      title: "Well Trace",
      md: "MD",
      inc: "Inc",
      azi: "Azi",
      tvd: "TVD",
      ns: "NS",
      ew: "EW",
      dls: "DLS",
      kb: "KB",
      kbElevation: "KB Elevation:"
    },
    // Wellbore Bit Program
    wellboreBitProgram: {
      title: "Wellbore Bit Program",
      bitType: "Bit Type",
      bitSize: "Bit Size(inch)",
      startDepth: "Start Depth",
      endDepth: "End Depth",
      bottomDepth: "Bottom Depth(m)",
      depth: "Depth",
      // Validation messages
      bitSizeRequired: "Bit size is required",
      bottomDepthRequired: "Bottom depth is required"
    },
    // Wellbore Casing Program
    wellboreCasingProgram: {
      title: "Wellbore Casing Program",
      casingType: "Casing Type",
      casingSize: "Casing Size(inch)",
      casingDepth: "Casing Depth",
      serial: "Serial",
      topDepth: "Top Depth(m)",
      bottomDepth: "Bottom Depth(m)",
      // Validation messages
      casingSizeRequired: "Casing size is required",
      serialRequired: "Serial is required",
      topDepthRequired: "Top depth is required",
      bottomDepthRequired: "Bottom depth is required"
    },
    // Comment
    comment: {
      title: "Comment",
      content: "Content",
      author: "Author",
      commentTime: "Comment Time",
      contentRequired: "Content is required",
      commentPlaceholder: "Say something..."
    },
    // Log Plot List
    logPlotList: {
      title: "Log Plot",
      plotName: "Plot Name",
      plotMemo: "Memo",
      startIndex: "Start Depth",
      endIndex: "End Depth",
      minIndex: "Current Data Min Depth",
      maxIndex: "Current Data Max Depth",
      depthRatio: "Depth Ratio",
      plotNameRequired: "Plot name is required",
      trackList: "Track List",
      noDataFound: "No data found"
    },
    // Wellbore Detail Tabs
    wellboreDetail: {
      wellboreInfo: "Wellbore Info Detail",
      bitProgram: "Bit Program",
      casingProgram: "Casing Program",
      jobList: "Job List",
      fileAttachList: "File Attach List",
      logDataFileList: "Log Data File List",
      logPlotList: "Log Plot List",
      markerLayerList: "Marker Layer",
      markPoints: "Mark Points"
    },
    // Geologic Model
    geologicModel: {
      title: "Geologic Model",
      modelName: "Model Name",
      inputKeyword: "Enter keyword"
    },
    // Oil Well Detail Tabs
    oilWellDetail: {
      wellInfo: "Well Info Detail",
      wellboreList: "Wellbore List"
    },
    // Oil Field Detail Tabs
    oilFieldDetail: {
      fieldInfo: "Oil Field Info Detail",
      blockList: "Block List",
      geologicModelList: "Geologic Model"
    },
    // Oilfield Block Detail Tabs
    oilfieldBlockDetail: {
      blockInfo: "Block Info Detail",
      geologicModelList: "Geologic Model",
      wellList: "Well List"
    },
    // Block Geologic Model
    blockGeologicModel: {
      title: "Geologic Model",
      fileName: "File Name",
      depth: "Depth",
      leftColor: "Left Color",
      rightColor: "Right Color",
      xInitialCoord: "X Initial Coordinate",
      yInitialCoord: "Y Initial Coordinate",
      zInitialCoord: "Z Initial Coordinate",
      xGridPoints: "X Grid Points",
      yGridPoints: "Y Grid Points",
      xMinValue: "X Min Value",
      xMaxValue: "X Max Value",
      yMinValue: "Y Min Value",
      yMaxValue: "Y Max Value",
      zMinValue: "Z Min Value",
      zMaxValue: "Z Max Value",
      zData: "Z Data",
      threeDModel: "3D Model",
      twoDModel: "2D Model",
      detail: "Detail",
      uploadFileFormat: "Please upload .grd or .ascii file",
      fileNameTooLong: "File name too long, cannot upload",
      pleaseSelectUploadFile: "Please select file to upload"
    },
  },
      // Alias Management
    aliasManagement: {
      title: "Alias Management",
      index: "Index",
      curveName: "Curve Name",
      curveAlias: "Curve Alias",
      description: "Description",
      createTime: "Create Time",
      action: "Action",
      delete: "Delete",
      saveAll: "Save All",
      searchPlaceholder: "Enter curve name or alias to search",
      confirmTitle: "Confirm",
      confirm: "Confirm",
      cancel: "Cancel",
      noChanges: "No changes",
      saveSuccess: "Save successful",
      saveFailed: "Save failed",
      deleteSuccess: "Delete successful",
      deleteCancelled: "Delete cancelled",
      confirmDelete: "Are you sure to delete this record?",
      selectDeleteRows: "Please select records to delete",
      batchDeleteConfirm: "Are you sure to delete {count} selected records?",
      batchDeleteSuccess: "Batch delete successful",
              importTitle: "Import Alias",
        uploadText: "Drag file here or click to upload",
        uploadTip: "Support .csv format files",
        importOption: "Import Option",
        mergeOption: "Merge",
        overwriteOption: "Overwrite",
        mergeTip: "Merge: Merge aliases when curve names are the same",
        overwriteTip: "Overwrite: Overwrite aliases when curve names are the same",
        importSuccess: "Import successful",
        importFailed: "Import failed",
      invalidFileType: "File format not supported",
      fileTooLarge: "File size cannot exceed 2MB",
      exportFailed: "Export failed",
      noAlias: "No Alias",
      chineseCommaError: "Cannot use Chinese comma (，), please use English comma (,)",
      invalidCharError: "Contains invalid character: {char}",
      aliasTooLongError: "Alias too long: {alias}, single alias cannot exceed 50 characters",
      emptyAliasError: "Cannot contain empty alias",
      tooManyAliasesError: "Too many aliases, cannot exceed 20",
      duplicateAliasError: "Duplicate aliases found",
      inputPlaceholder: "Enter aliases separated by English commas, e.g.: A1,A2,A3",
      loadFailed: "Load failed",
      exportSuccess: "Export successful",
      pleaseSelectFile: "Please select file",
      emptyCurveNameWarning: "There are records with empty curve names, please fill in the curve name before saving"
    }
};
