'use strict'
// Template version: 1.2.6
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')
const fs = require('fs')

let APIConfig = {
  "user": false,
  "resource": false,
  "oil": false,
  "log": false,
  "project": false,
  "datapreprocess": false,
  "backup": false,
  "dpwr": false,
  "demo": false,
  "ws": false
};

APIConfig = require('../APIConifg.json')

/**
 * 是否使用本地API
 * true：使用本地服务
 * false：使用局域网服务
 */
const USE_LOCAL_API = APIConfig

module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/api/user': {// UserService // 匹配所有以 '/api/user'开头的请求路径
        target: USE_LOCAL_API.user ? 'http://localhost:50001' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.user ? { '^/api/user': '' } : {},
        changeOrigin: true,
      },
      '/api/resource': {// ResourceService
        target: USE_LOCAL_API.resource ? 'http://localhost:50002' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.resource ? { '^/api/resource': '' } : {},
        changeOrigin: true,
      },
      '/api/oil': {// OilService
        target: USE_LOCAL_API.oil ? 'http://localhost:50003' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.oil ? { '^/api/oil': '' } : {},
        changeOrigin: true,
      },
      '/api/log': {// LogService
        target: USE_LOCAL_API.log ? 'http://localhost:50004' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.log ? { '^/api/log': '' } : {},
        changeOrigin: true,
      },
      '/api/project': {// ProjectService
        target: USE_LOCAL_API.project ? 'http://localhost:50005' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.project ? { '^/api/project': '' } : {},
        changeOrigin: true,
      },
      '/api/Preprocess': {// Preprocess
        target: USE_LOCAL_API.datapreprocess ? 'http://localhost:51002' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.datapreprocess ? { '^/api/Preprocess': '' } : {},
        changeOrigin: true,
      },
      '/api/GeoSteering': {// GeoSteering
        target: USE_LOCAL_API.GeoSteering ? 'http://localhost:51004' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.GeoSteering ? { '^/api/GeoSteering': '' } : {},
        changeOrigin: true,
      },
      '/api/Witsml': {// Witsml
        target: USE_LOCAL_API.Witsml ? 'http://localhost:50010' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.Witsml ? { '^/api/Witsml': '' } : {},
        changeOrigin: true,
      },
      '/api/backup': {// BackupService
        target: USE_LOCAL_API.backup ? 'http://localhost:50007' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.backup ? { '^/api/backup': '' } : {},
        changeOrigin: true,
      },
      '/api/dwpr': {// DWPRService
        target: USE_LOCAL_API.Witsml ? 'http://localhost:50006' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.Witsml ? { '^/api/dwpr': '' } : {},
        changeOrigin: true,
      },
      '/api/sdk': {// SDKService
        target: USE_LOCAL_API.sdk ? 'http://localhost:50008' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.sdk ? { '^/api/sdk': '' } : {},
        changeOrigin: true,
      },
      '/api/processModule': {// ProcessModuleService
        target: USE_LOCAL_API.demo ? 'http://localhost:50013' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.demo ? { '^/api/processModule': '' } : {},
        changeOrigin: true,
      },
      '/api/multiWellCompare': {// MultiWellCompare
        target: USE_LOCAL_API.multiWellCompare ? 'http://localhost:51001' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.multiWellCompare ? { '^/api/multiWellCompare': '' } : {},
        changeOrigin: true,
      },
      '/api/markerLayer': {// MarkerLayerService
        target: USE_LOCAL_API.markerLayer ? 'http://localhost:50009' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.markerLayer ? { '^/api/markerLayer': '' } : {},
        changeOrigin: true,
      },
      '/api/visual3D': {// Visual3D
        target: USE_LOCAL_API.visual3D ? 'http://localhost:51003' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.visual3D ? { '^/api/visual3D': '' } : {},
        changeOrigin: true,
      },
      '/ws': {// StateSyncService (WebSocket)
        target: USE_LOCAL_API.ws ? 'http://localhost:50011' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.ws ? { '^/ws': '' } : {},
        changeOrigin: true,
        ws: true,  // 启用 WebSocket 代理
        // 解决 WebSocket 协议升级头丢失问题
        onProxyReqWs: (proxyReq) => {
          proxyReq.setHeader('Sec-WebSocket-Protocol', 'connectionToken');
        },
      },
      '/api/stateSync': {// StateSyncService
        target: USE_LOCAL_API.ws ? 'http://localhost:50011' : 'http://***************:9527',
        pathRewrite: USE_LOCAL_API.ws ? { '^/api/stateSync': '' } : {},
        changeOrigin: true,
      },
    },

    // Various Dev Server settings

    // can be overwritten by process.env.HOST
    // if you want dev by ip, please set host: '0.0.0.0'
    host: 'localhost',
    port: 9527, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: false,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: true,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-source-map',

    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',

    /**
     * You can set by youself according to actual condition
     * You will need to set this if you plan to deploy your site under a sub path,
     * for example GitHub pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then assetsPublicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     */
    assetsPublicPath: '/',

    /**
     * Source Maps
     */
    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build:prod --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report || false,

    // `npm run build:prod --generate_report`
    generateAnalyzerReport: process.env.npm_config_generate_report || false
  }
}
