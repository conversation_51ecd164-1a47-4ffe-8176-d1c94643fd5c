import{k as X,r as F,l as I,o as Y,m as K,e as Q,d as ee,j as s,t as S,p as U,v as te,q as oe,F as ie}from"./index-B5fOQYc3.js";import{S as ne,C as ae,P as se,W as le,O as re,A as ce,D as de,a as he,b as ue,c as pe,M as me}from"./OrbitControls-CLEN8DbS.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ve={class:"wrapper"},ge={id:"info"},we={id:"fileInput"},xe={id:"controls"},Me={id:"heightScaleValue"},He=["innerHTML"],Fe={id:"colorLabels"},be={id:"maxHeightLabel"},ye={id:"height80Label"},Se={id:"height60Label"},Le={id:"height40Label"},_e={id:"height20Label"},Ce={id:"minHeightLabel"},$e=X({__name:"index",setup(ze){const L=new ne;L.background=new ae(0);const f=new se(75,window.innerWidth/window.innerHeight,.1,1e4);f.position.set(0,0,500);let y,_,p=null;const $=F(.2),z=F("3d"),P=F(""),E=F("ZMAP 文件 3D 可视化"),V=F("最高: 0"),D=F("80%: 0"),W=F("60%: 0"),A=F("40%: 0"),N=F("20%: 0"),T=F("最低: 0");function q(){y=new le({antialias:!0}),y.setSize(window.innerWidth,window.innerHeight),document.getElementById("threeContainer")?.appendChild(y.domElement),_=new re(f,y.domElement),_.enableDamping=!0,_.dampingFactor=.05;const t=new ce(16777215,.7);L.add(t);const i=new de(16777215,.8);i.position.set(1,1,1),L.add(i),R(),window.addEventListener("resize",k)}function k(){f.aspect=window.innerWidth/window.innerHeight,f.updateProjectionMatrix(),y.setSize(window.innerWidth,window.innerHeight)}function R(){if(requestAnimationFrame(R),_.update(),p){const t=f.position.distanceTo(p.position),i=p.userData.refDistance||1,e=p.userData.baseContourWidth||1;p.material.uniforms.contourWidth.value=e*(t/i)*2.5}y.render(L,f)}function G(t){const e=t.split(`
`).map(o=>o.trim());let m=null;for(let o=0;o<e.length;o++)if(e[o]==="@"){m=o;break}if(m===null)throw new Error("未找到 header 结束标记 '@'");let v="",l="",c="";for(let o=0;o<m;o++)if(e[o].startsWith("@")){c=e[o+1],v=e[o+2],l=e[o+3];break}if(!v)throw new Error("未找到网格信息行");if(!l)throw new Error("未找到网格间距信息行");const w=c.split(",").map(o=>o.trim()).filter(o=>o!==""),u=v.split(",").map(o=>o.trim()).filter(o=>o!==""),x=l.split(",").map(o=>o.trim()).filter(o=>o!=="");if(u.length<6)throw new Error("网格信息不足，请检查文件格式！");const a={cols:parseInt(u[0]),rows:parseInt(u[1]),xMin:parseFloat(u[2]),xMax:parseFloat(u[3]),yMin:parseFloat(u[4]),yMax:parseFloat(u[5]),nullValue:1e30,rotation:0,xStep:0,yStep:0,data:[]};a.xStep=parseFloat(x[1]),a.yStep=parseFloat(x[2]),a.nullValue=parseFloat(w[1]==""?w[2]:w[1]);const h=[];for(let o=m+1;o<e.length;o++){const n=e[o];if(n==="")continue;const r=n.split(/\s+/).filter(d=>d!=="");for(const d of r)try{const g=parseFloat(d);h.push(g)}catch{console.warn("无法转换数据:",d)}}const H=a.rows*a.cols,b=h.length;if(console.log(`预期数据数量: ${H}, 实际数据数量: ${b}`),b!==H)throw new Error(`数据数量与预期不符，预期 ${H}，实际 ${b}`);for(let o=0;o<a.rows;o++){a.data[o]=[];for(let n=0;n<a.cols;n++){const r=o*a.cols+n;a.data[o][n]=h[r]}}return a}function O(t){const e=t.split(`
`).map(n=>n.trim()),m=e[0],v=e[2],l=e[3],c=e[4];if(!v)throw new Error("未找到网格信息行");if(!c)throw new Error("未找到网格间距信息行");if(!l)throw new Error("未找到网格行列信息行");if(!m)throw new Error("未找到网格缺省值信息行");const w=m.split(/\s+/),u=v.split(/\s+/),x=l.split(/\s+/),a=c.split(/\s+/),h={cols:parseInt(x[1]),rows:parseInt(x[2]),xMin:parseFloat(u[1]),xMax:parseFloat(u[2]),yMin:parseFloat(u[3]),yMax:parseFloat(u[4]),nullValue:parseFloat(w[5]),rotation:0,xStep:0,yStep:0,data:[]};h.xStep=parseFloat(a[1]),h.yStep=parseFloat(a[2]);const H=[];for(let n=6;n<e.length;n++){const r=e[n];if(r==="")continue;const d=r.split(/\s+/).filter(g=>g!=="");for(const g of d)try{const M=parseFloat(g);H.push(M)}catch{console.warn("无法转换数据:",g)}}const b=h.rows*h.cols,o=H.length;if(console.log(`预期数据数量: ${b}, 实际数据数量: ${o}`),o!==b)throw new Error(`数据数量与预期不符，预期 ${b}，实际 ${o}`);for(let n=0;n<h.rows;n++){h.data[n]=[];for(let r=0;r<h.cols;r++){const d=n*h.cols+r;h.data[n][r]=H[d]}}return h}function Z(t){const i=t.xMax-t.xMin,e=t.yMax-t.yMin,m=new he(e,i,t.cols-1,t.rows-1),v=m.attributes.position.array;let l=1/0,c=-1/0;for(let n=0;n<t.rows;n++)for(let r=0;r<t.cols;r++){const d=t.data[n][r];d!==null&&!isNaN(d)&&Math.abs(d)<t.nullValue&&(l=Math.min(l,d),c=Math.max(c,d))}P.value=`
    网格大小: ${t.rows} × ${t.cols}<br>
    X范围: ${t.xMin.toFixed(2)} - ${t.xMax.toFixed(2)}<br>
    Y范围: ${t.yMin.toFixed(2)} - ${t.yMax.toFixed(2)}<br>
    高度范围: ${l.toFixed(2)} - ${c.toFixed(2)}<br>
    网格间距: ${t.xStep} × ${t.yStep}
    `;const w=Math.max(e,i)/(c-l)*$.value,u=0,x=[],a=new Set;for(let n=0,r=0;n<v.length;n+=3,r++){const d=r%t.cols,g=Math.floor(r/t.cols),M=t.rows-1-g;if(M>=0&&M<t.rows&&d>=0&&d<t.cols){const C=t.data[M][d];C!==null&&!isNaN(C)&&Math.abs(C)<t.nullValue?(v[n+2]=(C-l)*w+u,a.add(r)):v[n+2]=u}}for(let n=0;n<t.rows-1;n++)for(let r=0;r<t.cols-1;r++){const d=n*t.cols+r,g=d+1,M=d+t.cols,C=M+1;a.has(d)&&a.has(g)&&a.has(M)&&x.push(d,g,M),a.has(g)&&a.has(M)&&a.has(C)&&x.push(g,C,M)}m.setIndex(x),m.attributes.position.needsUpdate=!0,m.computeVertexNormals();const h=(c-l)/20,H=.2,b=new ue({uniforms:{contourInterval:{value:h},contourWidth:{value:H},minHeight:{value:l},maxHeight:{value:c},heightScale:{value:w},baseHeight:{value:u}},vertexShader:`
            varying float vHeight;
            varying vec4 vPosition;
            uniform float minHeight;
            uniform float heightScale;
            uniform float baseHeight;
            
            void main() {
                vHeight = (position.z - baseHeight) / heightScale + minHeight;
                vPosition = modelViewMatrix * vec4(position, 1.0);
                gl_Position = projectionMatrix * vPosition;
            }
        `,fragmentShader:`
            uniform float contourInterval;
            uniform float contourWidth;
            uniform float minHeight;
            uniform float maxHeight;
            varying float vHeight;
            varying vec4 vPosition;
            
            void main() {
                // 计算标准化高度
                float normalizedHeight = (vHeight - minHeight) / (maxHeight - minHeight);
                float modHeight = mod(vHeight, contourInterval);
                
                // 计算等高线
                float distanceToContour = min(modHeight, contourInterval - modHeight);
                float contourFactor = smoothstep(0.0, contourWidth, distanceToContour);
                
                // 根据高度计算颜色
                vec3 color;
                if (normalizedHeight == 0.0) {                      
                    color = vec3(0.0, 0.0, 0.0);
                }
                else if (normalizedHeight < 0.2) {                      
                    color = vec3(0.0, normalizedHeight * 2.5, 0.5 + normalizedHeight * 2.5);
                } else if (normalizedHeight < 0.4) {
                    color = vec3(0.0, 0.5 + (normalizedHeight - 0.2) * 2.5, 1.0 - (normalizedHeight - 0.2) * 2.5);
                } else if (normalizedHeight < 0.6) {
                    color = vec3((normalizedHeight - 0.4) * 5.0, 1.0, 0.0);
                } else if (normalizedHeight < 0.8) {
                    color = vec3(1.0, 1.0 - (normalizedHeight - 0.6) * 2.5, 0.0);
                } else {
                    color = vec3(1.0, 0.5 + (normalizedHeight - 0.8) * 2.5, (normalizedHeight - 0.8) * 5.0);
                }
                
                // 混合等高线和地形颜色
                vec3 finalColor = mix(vec3(0.0, 0.0, 0.0), color, contourFactor);
                gl_FragColor = vec4(finalColor, 1.0);
            }
        `,side:pe}),o=new me(m,b);return o.rotation.x=-Math.PI/2,o.position.set(0,0,0),o.userData.baseContourWidth=H,o}function j(t,i){try{let e=null;if(i=="zmap"||i=="grd")e=G(t,i);else if(i=="")e=O(t);else{alert("文件格式不支持");return}p&&(L.remove(p),p.geometry.dispose(),p.material?.dispose()),p=Z(e),L.add(p);const m=e.xMax-e.xMin,v=e.yMax-e.yMin,l=Math.max(m,v);p.userData.refDistance=l,z.value==="top"?(f.position.set(0,0,l*1.5),f.lookAt(0,0,0)):(f.position.set(l*.8,l*.8,l*.8),f.lookAt(0,0,0)),f.near=.1,f.far=l*10,f.updateProjectionMatrix(),_.target.set(0,0,0),_.update(),E.value="已加载: "+e.rows*e.cols+" 个数据点";let c=1/0,w=-1/0;for(let x=0;x<e.rows;x++)for(let a=0;a<e.cols;a++){const h=e.data[x][a];h!==null&&!isNaN(h)&&Math.abs(h)<e.nullValue&&(c=Math.min(c,h),w=Math.max(w,h))}const u=w-c;V.value=`最高: ${w.toFixed(1)}`,D.value=`80%: ${(c+u*.8).toFixed(1)}`,W.value=`60%: ${(c+u*.6).toFixed(1)}`,A.value=`40%: ${(c+u*.4).toFixed(1)}`,N.value=`20%: ${(c+u*.2).toFixed(1)}`,T.value=`最低: ${c.toFixed(1)}`}catch(e){console.error("处理文件数据失败:",e),alert("处理文件数据失败: "+e.message)}}function J(t){const e=t.target.files?.[0];if(!e)return;const m=new FileReader;m.onload=function(v){const l=v.target?.result,c=B(e.name);j(l,c)},m.readAsText(e)}function B(t){const i=t.match(/\.([^.]+)$/);return i?i[1]:""}return I($,()=>{const t=document.getElementById("zmapFile");if(p&&t.files?.[0]){const i=t.files[0],e=new FileReader;e.onload=function(m){const v=m.target?.result,l=B(i.name);j(v,l)},e.readAsText(i)}}),I(z,()=>{if(!p)return;const t=p.geometry.parameters.width||1e3,i=p.geometry.parameters.height||1e3,e=Math.max(t,i);z.value==="top"?(f.position.set(0,0,e*1.5),f.lookAt(0,0,0)):(f.position.set(e*.8,e*.8,e*.8),f.lookAt(0,0,0)),_.update()}),Y(()=>{q()}),K(()=>{window.removeEventListener("resize",k),y&&y.dispose(),p&&(L.remove(p),p.geometry.dispose(),p.material?.dispose())}),(t,i)=>(ee(),Q(ie,null,[s("header",null,[s("div",ve,[s("div",ge,S(E.value),1)])]),s("main",null,[i[5]||(i[5]=s("div",{id:"threeContainer"},null,-1)),s("div",we,[s("input",{type:"file",id:"zmapFile",accept:"*",onChange:J},null,32)]),s("div",xe,[s("div",null,[i[2]||(i[2]=s("label",{for:"heightScale"},"高度缩放: ",-1)),U(s("input",{type:"range",id:"heightScale",min:"0.05",max:"1",step:"0.05","onUpdate:modelValue":i[0]||(i[0]=e=>$.value=e)},null,512),[[te,$.value]]),s("span",Me,S($.value),1)]),s("div",null,[i[4]||(i[4]=s("label",{for:"viewMode"},"视图模式: ",-1)),U(s("select",{id:"viewMode","onUpdate:modelValue":i[1]||(i[1]=e=>z.value=e)},i[3]||(i[3]=[s("option",{value:"3d"},"3D 视图",-1),s("option",{value:"top"},"俯视图",-1)]),512),[[oe,z.value]])])]),s("div",{id:"stats",innerHTML:P.value},null,8,He),i[6]||(i[6]=s("div",{id:"colorLegend"},null,-1)),s("div",Fe,[s("div",be,S(V.value),1),s("div",ye,S(D.value),1),s("div",Se,S(W.value),1),s("div",Le,S(A.value),1),s("div",_e,S(N.value),1),s("div",Ce,S(T.value),1)])])],64))}}),De=fe($e,[["__scopeId","data-v-32fa7015"]]);export{De as default};
