import{a as E,e as ee,f as Re,d as L,h as n,w as s,F as fe,s as Ie,b as se,i as Q,t as de,j as ae,x as Ze,l as Se,n as Oe,o as he,r as i,O as $e,p as Be,Q as De,R as ue,S as Ge,U as Ye,V as ft,G as pt,m as Fe,y as Ke,W as ve,X as vt,Y as mt,c as je,Z as Qe,_ as ht,$ as Je,a0 as J,a1 as gt,a2 as xt,k as bt,a3 as Le,a4 as Ae,a5 as yt,D as _t}from"./index-B5fOQYc3.js";import{s as ge,S as Dt,i as et,E as tt,C as at,D as It}from"./rc-dock-C2QYZG-B.js";import{_ as He}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{P as wt}from"./property-C5fC_ukq.js";import"./index-DW_MHI2K.js";const Ct="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAVklEQVR42r2SSQoAMAgD4/8fbQ/dqFVILZiTohlwERxSBSWRFVnAhZi9o9BTFmDMbwDHzAMCMwdAbC4CfI9gl5i+Agwo9QcOpBjg7KHoE2dP5oygtAENDF9WEXnYM1cAAAAASUVORK5CYII=",kt={name:"ContextMenu",data(){return{visible:!1,left:0,top:0,currentNode:null,menuData:[{label:"新建数据集",action:"addDataset",level:2},{label:"删除数据集",action:"removeDataset",level:3},{label:"编辑",action:"edit",level:1}],projectId:""}},computed:{filteredMenu(){return this.menuData.filter(f=>!0)}},methods:{show(f,R,W,v){this.menuData=v,this.visible=!0,this.left=f.clientX,this.top=f.clientY,this.currentNode=R,this.projectId=W,this.$nextTick(()=>{this.checkPosition()})},hide(){this.visible=!1,this.currentNode=null},handleMenuClick(f){this.$emit("menu-action",f,this.currentNode,this.projectId),this.hide()},checkPosition(){const f=this.$el;if(!f)return;const R=window.innerWidth,W=window.innerHeight,v=f.offsetWidth,l=f.offsetHeight;this.left+v>R&&(this.left=R-v),this.top+l>W&&(this.top=W-l)}},mounted(){document.addEventListener("click",this.hide)},beforeUnmount(){document.removeEventListener("click",this.hide)}},St=["onClick"];function Pt(f,R,W,v,l,w){const u=E("el-menu-item"),I=E("el-sub-menu"),M=E("el-menu");return l.visible?(L(),ee("div",{key:0,class:"custom-context-menu",style:Ze({left:l.left+"px",top:l.top+"px"})},[n(M,{class:"el-menu-vertical-demo",collapse:!0},{default:s(()=>[(L(!0),ee(fe,null,Ie(w.filteredMenu,(k,P)=>(L(),ee(fe,{key:P},[k.children?.length>0?(L(),se(I,{key:0,index:k.action},{title:s(()=>[ae("div",null,de(k.label),1)]),default:s(()=>[(L(!0),ee(fe,null,Ie(k.children,(S,G)=>(L(),se(u,{key:G,index:S.action,onClick:j=>w.handleMenuClick(S.action)},{default:s(()=>[Q(de(S.label),1)]),_:2},1032,["index","onClick"]))),128))]),_:2},1032,["index"])):(L(),se(u,{key:1,index:k.action},{default:s(()=>[ae("div",{onClick:S=>w.handleMenuClick(k.action)},de(k.label),9,St)]),_:2},1032,["index"]))],64))),128))]),_:1})],4)):Re("",!0)}const Tt=He(kt,[["render",Pt],["__scopeId","data-v-8f08602f"]]),Mt={class:"custom-tree"},Vt={key:0},Et={key:1,class:"custom-tree-node"},At={key:2},jt={key:3},Ft={__name:"ResourceTree",props:{treeData:{type:Array,default:[]},isLoading:{type:Boolean,default:!1}},emits:["triggerAddTab","triggerMenuAction","update:isLoading","triggerRemoveTab"],setup(f,{expose:R,emit:W}){const v=f;Se(()=>v.treeData,c=>{c.length&&Oe(()=>{S()})},{deep:!0,immediate:!0}),he(()=>{});const l=i(null),w=i(null),u={children:"children",label:"name"},I=i(null),M=W;function k(c,h,m){c.preventDefault();const g={1:[{action:"editProject",label:"Edit Project"},{action:"refresh",label:"Refresh"}],2:[{action:"refresh",label:"Refresh"}],3:[{action:"cloneDataset",label:"Clone Dataset"},{action:"deleteDataset",label:"Delete Dataset"},{action:"refresh",label:"Refresh"}],4:[{action:"editCurve",label:"Edit Curve"},{action:"refresh",label:"Refresh"}]},B=m.level,x=g[B]||[];x.length!==0&&l.value.show(c,m,v.treeData[0].id,x)}function P(c,h,m){I.value=h.data,M("triggerMenuAction",{actionType:c,nodeData:h,projectId:m})}function S(){document.querySelectorAll(".custom-tree-node").forEach(h=>{const m=h.parentNode.querySelector(".el-checkbox");m&&(m.style.display="inline-block")})}function G(c,h){h.level==4&&(I.value=c,w.value.setCurrentKey(c.id),M("triggerAddTab","1",h,c.name))}function j(c,h){let m=w.value.getNode(c.id);h.checkedKeys.includes(c.id)?M("triggerAddTab","",m,c.name):M("triggerRemoveTab",c.id)}function T(c,h){for(const m of c){if(m.id===h)return m;if(m.children&&m.children.length>0){const g=T(m.children,h);if(g)return g}}return null}function b(c){w.value.setCurrentKey(c),I.value=T(v.treeData,c)}function X(c,h){w.value.setChecked(c,h)}return R({setCheckNode:b,setCheckedKeys:X}),(c,h)=>{const m=E("Folder"),g=E("el-icon"),B=E("TrendCharts"),x=E("Coin"),y=E("el-tree"),A=$e("loading");return L(),ee("div",Mt,[Be((L(),se(y,{data:f.treeData,props:u,"node-key":"id","default-expand-all":"","highlight-current":"","show-checkbox":"","expand-on-click-node":!1,onNodeClick:G,onCheck:j,ref_key:"tree",ref:w,onNodeContextmenu:k},{default:s(({node:V,data:H})=>[V.level===1?(L(),ee("span",Vt,[n(g,{size:15},{default:s(()=>[n(m)]),_:1}),ae("span",null,de(H.name),1)])):V.level===4?(L(),ee("span",Et,[h[0]||(h[0]=ae("img",{src:Ct,alt:""},null,-1)),ae("span",null,de(H.name),1)])):V.level===3?(L(),ee("span",At,[n(g,{size:15},{default:s(()=>[n(B)]),_:1}),ae("span",null,de(H.name),1)])):(L(),ee("span",jt,[n(g,{size:15},{default:s(()=>[n(x)]),_:1}),ae("span",null,de(H.name),1)]))]),_:1},8,["data"])),[[A,f.isLoading]]),n(Tt,{ref_key:"contextMenu",ref:l,onMenuAction:P},null,512)])}}};function We(f){return ge({url:"/Preprocess/Project/CreateProject",method:"post",headers:{"Content-Type":"multipart/form-data"},data:f})}function Nt(f){return ge({url:"/Preprocess/Project/UpdateProject",method:"post",params:f})}function Lt(f){return ge({url:"/Preprocess/Project/GetProject?id="+f,method:"get"})}function Rt(f){return ge({url:"/log/LogPlotChart/getLogPlotDataFile?wellboreId="+f,method:"get"})}function lt(f){return ge({url:"/Preprocess/Project/AddDataset",method:"post",headers:{"Content-Type":"multipart/form-data"},data:f})}function nt(f){return ge({url:"/log/Dataset/Delete",method:"post",headers:{"Content-Type":"multipart/form-data"},data:f})}function Gt(f){return ge({url:"/log/Dataset/Clone",method:"post",headers:{"Content-Type":"multipart/form-data"},data:f})}function Ot(f){return ge({url:"/multiWellCompare/Log/GetChannelList?wellboreId="+f.wellboreId+"&logName="+f.logName})}function Ue(f){return ge({url:"/oil/oilWell/list",method:"post",params:f})}const $t={class:"dialog-footer"},Bt={__name:"createProject",emits:["project-created"],setup(f,{expose:R,emit:W}){const v=W,l=De({name:"",wellId:"",remark:""}),w=De({wellId:[{required:!0,message:"请选择井",trigger:"change"}],name:[{required:!0,message:"请输入工程名称",trigger:"blur"}]}),u=i(""),I=i(""),M=i(),k=i([]),P=i(!1),S=i(null);function G(h,m,g){P.value=!0,M.value=g,g===1?(I.value="Edit Project",l.name=h.data.name,u.value=m):(I.value="Create Project",c())}const j=async h=>h?await h.validate():!1;async function T(h){await j(h)&&(M.value===1?await X():await b())}const b=async()=>{const h=new URLSearchParams(window.location.search),m=new FormData;m.append("name",l.name),m.append("wellId",l.wellId),m.append("remark",l.remark),m.append("appId",h.get("appId"));try{const g=await We(m);g.success&&(v("project-created",g.data),window.history.pushState({},"",`?id=${g.data}`),ue.success("创建成功"),P.value=!1)}catch(g){ue.error(g.message)}},X=async()=>{try{(await Nt({id:u.value,name:l.name,remark:l.remark})).success&&(ue.success("修改成功"),v("project-created",u.value),P.value=!1)}catch(h){ue.error(h.message)}};function c(){Ue().then(h=>{h.success?k.value=h.data:ue({message:h.message,type:"error"})})}return R({openDialog:G}),(h,m)=>{const g=E("el-input"),B=E("el-form-item"),x=E("el-option"),y=E("el-select"),A=E("el-form"),V=E("el-button"),H=E("el-dialog");return L(),ee("div",null,[n(H,{title:I.value,modelValue:P.value,"onUpdate:modelValue":m[5]||(m[5]=r=>P.value=r),width:"550px","close-on-click-modal":!1},{footer:s(()=>[ae("div",$t,[n(V,{onClick:m[3]||(m[3]=r=>P.value=!1)},{default:s(()=>m[6]||(m[6]=[Q("Cancel")])),_:1}),n(V,{type:"primary",onClick:m[4]||(m[4]=r=>T(S.value))},{default:s(()=>m[7]||(m[7]=[Q("Ok")])),_:1})])]),default:s(()=>[n(A,{model:l,ref_key:"formRef",ref:S,rules:w},{default:s(()=>[n(B,{label:"name",prop:"name",class:"form-item","label-width":"80px"},{default:s(()=>[n(g,{modelValue:l.name,"onUpdate:modelValue":m[0]||(m[0]=r=>l.name=r),placeholder:"Please enter project name",class:"input-field"},null,8,["modelValue"])]),_:1}),M.value!==1?(L(),se(B,{key:0,label:"Well",class:"form-item","label-width":"80px",prop:"wellId"},{default:s(()=>[n(y,{modelValue:l.wellId,"onUpdate:modelValue":m[1]||(m[1]=r=>l.wellId=r),placeholder:"Please select well",filterable:""},{default:s(()=>[(L(!0),ee(fe,null,Ie(k.value,r=>(L(),se(x,{key:r.id,label:r.wellName,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):Re("",!0),n(B,{label:"Remark",class:"form-item","label-width":"80px"},{default:s(()=>[n(g,{modelValue:l.remark,"onUpdate:modelValue":m[2]||(m[2]=r=>l.remark=r),placeholder:"Please enter remark",class:"input-field"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}},Ht={class:"dialog-footer"},zt={__name:"addDataSet",props:{dataSetTable:{type:Array,default:[]}},emits:["addChannel"],setup(f,{expose:R,emit:W}){const v=i(!1),l=i(""),w=i(""),u=i(""),I=i([]),M=i([]),k=i([]),P=i(null),S=i(!1),G=W,j=f;function T(){if(k.value.length>0){const B=I.value.find(y=>y.fileName===l.value),x=new FormData;x.append("projectId",u.value),x.append("wellboreId",w.value),x.append("datasetId",B?.id),x.append("datasetName",B?.sourceFileName),x.append("logCurvesStr",k.value.map(y=>y.channelName).join(",")),lt(x).then(y=>{y.success&&(ue({message:y.message,type:"success"}),G("addChannel",u.value))}).catch(y=>{console.error("新增数据集出错:",y)}).finally(()=>{v.value=!1})}}function b(B){Rt(B).then(x=>{x.success&&(I.value=x.data)})}function X(B,x){w.value=B,u.value=x,v.value=!0,l.value="",k.value=[],M.value=[],b(B)}function c(){S.value=!0,Ot({wellboreId:w.value,logName:l.value}).then(B=>{if(B.success){M.value=B.data;let x=I.value.find(V=>V.fileName===l.value);const y=j.dataSetTable.find(V=>V.id===x.id),A=new Set(y?.children.map(V=>V.name)||[]);Oe(()=>{M.value.forEach(V=>{A.has(V.channelName)&&P.value?.toggleRowSelection(V,!0)})})}S.value=!1})}function h(B){k.value=B}function m(B,x){Ge({title:"提示",message:"确定删除数据集？",showCancelButton:!0,confirmButtonText:"OK",cancelButtonText:"Cancel"}).then(()=>{g(B,x)}).catch(()=>{})}function g(B,x){const y=new FormData;y.append("projectId",x),y.append("datasetId",B),nt(y).then(A=>{A.success&&(ue({message:A.message,type:"success"}),G("addChannel",x))}).catch(A=>{console.error("删除数据集出错:",A)}).finally(()=>{})}return R({openDataSetDialog:X,removeDataSetPrompt:m}),(B,x)=>{const y=E("el-col"),A=E("el-option"),V=E("el-select"),H=E("el-row"),r=E("el-table-column"),N=E("el-table"),_=E("el-button"),F=E("el-dialog"),O=$e("loading");return L(),ee("div",null,[n(F,{title:"Add DataSet",modelValue:v.value,"onUpdate:modelValue":x[3]||(x[3]=q=>v.value=q),width:"700px","close-on-click-modal":!1},{footer:s(()=>[ae("div",Ht,[n(_,{onClick:x[1]||(x[1]=q=>v.value=!1)},{default:s(()=>x[5]||(x[5]=[Q("Cancel")])),_:1}),n(_,{type:"primary",onClick:x[2]||(x[2]=q=>T())},{default:s(()=>x[6]||(x[6]=[Q("Ok")])),_:1})])]),default:s(()=>[n(H,null,{default:s(()=>[n(y,{span:4},{default:s(()=>x[4]||(x[4]=[ae("div",{style:{height:"32px","line-height":"32px"}},"Dataset",-1)])),_:1}),n(y,{span:20},{default:s(()=>[n(V,{modelValue:l.value,"onUpdate:modelValue":x[0]||(x[0]=q=>l.value=q),placeholder:"Please select dataset",onChange:c},{default:s(()=>[(L(!0),ee(fe,null,Ie(I.value,q=>(L(),se(A,{key:q.fileName,label:q.sourceFileName,value:q.fileName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(H,null,{default:s(()=>[Be((L(),se(N,{ref_key:"multipleTableRef",ref:P,data:M.value,style:{width:"100%"},onSelectionChange:h},{default:s(()=>[n(r,{type:"selection",width:"55"}),n(r,{property:"channelName",label:"曲线名称",width:"100",align:"center"}),n(r,{prop:"dataType",label:"数据类型",width:"100",align:"center"}),n(r,{label:"颜色",width:"120",align:"center"},{default:s(q=>[ae("div",{style:Ze(`border-top: 3px solid ${q.row.prsColor}; width: 100%`)},null,4)]),_:1}),n(r,{prop:"channelUnit",label:"单位",width:"80",align:"center"}),n(r,{prop:"indexSpacing",label:"采样间距",align:"center"}),n(r,{prop:"totalSamples",label:"采样数",align:"center"})]),_:1},8,["data"])),[[O,S.value]])]),_:1})]),_:1},8,["modelValue"])])}}},we=Ye("editCurve",{state:()=>({wellId:"",datasetId:"",curveId:"",activeAction:"",isStartEdit:!1,minIndex:0,maxIndex:0,curveEditInstance:{},depthPairsInstance:null}),actions:{setWellId(f){this.wellId=f},setDatasetId(f){this.datasetId=f},setCurveId(f){this.curveId=f},setActiveAction(f){this.activeAction=f},setIsStartEdit(f){this.isStartEdit=f},setMinIndex(f){this.minIndex=f},setMaxIndex(f){this.maxIndex=f},registerCurveEdit(f,R){R&&f&&(this.curveEditInstance[f]=R)},registerDepthPairs(f){this.depthPairsInstance=f}}}),ze=Ye("resourceTree",{state:()=>({projectTreeData:[]}),actions:{updateTreeData(f){this.projectTreeData=f}}}),Wt={class:"project"},Ut={__name:"index",emits:["click","tabClose"],setup(f,{expose:R,emit:W}){const v=we(),l=ze(),w=i(),u=i(),I=i(),M=i([]),k=i([]),P=i(!1),S=i(!1),G=ft(),j=pt();R({resourceTreeRef:w});const T=W;function b(){Ge({title:"提示",message:"未找到工程，是不是创建新工程？",showCancelButton:!0,confirmButtonText:"OK",cancelButtonText:"Cancel",closeOnClickModal:!1}).then(()=>{u.value.openDialog()}).catch(()=>{})}function X({actionType:_,nodeData:F,projectId:O}){({cloneDataset:()=>x(F,O),deleteDataset:()=>y(F,O),editProject:()=>c(F,O),refresh:()=>h(O),editCurve:()=>g(" ",F,F.data.name)})[_]?.()}function c(_,F){u.value.openDialog(_,F,1)}function h(_){S.value=!0,Lt(_).then(F=>{if(F.success){const O=F.data;v.setWellId(O.projectBindInfo.wellId);const q={id:O.id,name:O.name,children:O.projectBindInfo.wellbores.map(oe=>({...oe}))};M.value=[q],l.updateTreeData([q]);const le={...G.query};le.id=O.id,j.push({query:le}),P.value=!1,S.value=!1}else ue({message:F.message,type:"error"}),S.value=!1})}function m(_){h(_)}function g(_,F,O){v.setCurveId(F.data.id),v.setDatasetId(F.parent.data.id);let q={wellId:v.wellId,datasetId:v.datasetId,id:v.curveId};_!=="1"&&w.value.setCheckedKeys(F.data.id,!0),T("click",_,q,O)}function B(_){T("tabClose",_)}function x(_,F){const O=new FormData;O.append("wellboreId",_.parent.data.id),O.append("id",_.data.id),O.append("type",_.data.type),Gt(O).then(q=>{q.success&&(ue({message:"克隆成功",type:"success"}),h(F))})}function y(_,F){Ge({title:"提示",message:"确定删除数据集？",showCancelButton:!0,confirmButtonText:"OK",cancelButtonText:"Cancel",closeOnClickModal:!1}).then(()=>{A(_.data.id,_.parent.data.id,_.data.type,F)}).catch(()=>{})}function A(_,F,O,q){const le=new FormData;le.append("wellboreId",F),le.append("id",_),le.append("type",O),nt(le).then(oe=>{oe.success&&(ue({message:"删除成功",type:"success"}),h(q))}).catch(oe=>{console.error("删除数据集出错:",oe)}).finally(()=>{})}function V(){const _=new Date,F=_.getFullYear(),O=(_.getMonth()+1).toString().padStart(2,"0"),q=_.getDate().toString().padStart(2,"0"),le=_.getHours().toString().padStart(2,"0"),oe=_.getMinutes().toString().padStart(2,"0"),re=_.getSeconds().toString().padStart(2,"0"),ie=_.getMilliseconds().toString().padStart(3,"0");return`${F}${O}${q}${le}${oe}${re}${ie}`}async function H(_){let le=(await Ue()).data.find(ie=>ie.id===_).wellName+"_工程_"+V();const oe=new URLSearchParams(window.location.search),re=new FormData;re.append("name",le),re.append("wellId",_),re.append("remark",le),re.append("appId",oe.get("appId")),We(re).then(ie=>{if(ie.success){const pe=ie.data;h(pe)}})}async function r(_){let le=(await Ue()).data.find(ie=>ie.id===_.wellId).wellName+"_工程_"+V();const oe=new URLSearchParams(window.location.search),re=new FormData;re.append("name",le),re.append("wellId",_.wellId),re.append("remark",le),re.append("appId",oe.get("appId")),We(re).then(ie=>{if(ie.success){const pe=ie.data;let xe={projectId:pe,datasetName:_.datasetName,datasetId:_.datasetId,wellboreId:_.wellboreId,logCurvesStr:[_.channelName]};lt(xe).then(Ce=>{Ce.success&&h(pe)})}})}const N=_=>{_.origin===window.location.origin&&_.data.type==="projectInfo"&&r(_.data.data)};return he(()=>{let _=G.query.id,F=G.query.wellId,O=G.query.channelId;!_&&!F&&!O?b():_?h(_):F?(P.value=!0,H(F)):O&&(P.value=!0,window.addEventListener("message",N))}),Fe(()=>{window.removeEventListener("message",N)}),(_,F)=>{const O=$e("loading");return Be((L(),ee("div",Wt,[n(Ft,{ref_key:"resourceTreeRef",ref:w,onTriggerAddTab:g,onTriggerRemoveTab:B,treeData:M.value,isLoading:S.value,onTriggerMenuAction:X},null,8,["treeData","isLoading"]),n(Bt,{ref_key:"createProjectRef",ref:u,onProjectCreated:m},null,512),n(zt,{ref_key:"addDataSetRef",ref:I,onAddChannel:h,dataSetTable:k.value},null,8,["dataSetTable"])])),[[O,P.value,void 0,{fullscreen:!0,lock:!0}]])}}},Yt=He(Ut,[["__scopeId","data-v-8bef25ae"]]),Xt={class:"tabmenu",ref:"tabMenuRef"},Kt={class:"pane-content"},qt={key:1,class:"content-item"},Zt={key:2,class:"content-item"},Qt={__name:"TabMenu",props:{tabId:{type:String,default:""}},emits:["add-tab"],setup(f,{expose:R,emit:W}){const v=we(),l=W,w=f,u=i(!0),I=i("home"),M=i([{label:"Home",value:"home",children:[{label:"Curve Edit",value:"add_curveEdit",icon:"Edit"},{label:"ADM & DSHIFT",value:"add_admAndDshift",icon:"SwitchFilled"}]}]),k={label:"Curve Edit",value:"curveEdit",children:[{label:"Start Edit",value:"startEdit",icon:"VideoPlay"},{label:"Draw Curve",value:1,icon:"EditPen"},{label:"Draw By Point",value:2,icon:"Pointer"},{label:"Constant Value",value:3,icon:"Crop"},{label:"Vertical Shift",value:4,icon:"Rank"},{label:"Smooth Curve Section",value:5,icon:"Filter"},{label:"Text Edit",value:6,icon:"Edit"},{label:"Save",value:"saveEdit",icon:"DocumentChecked"},{label:"Data Positioning",value:7,icon:"Location",single:!0}]},P={label:"ADM & DSHIFT",value:"admAndDshift",children:[{label:"Add Depth Pairs",value:8,icon:"CirclePlus"},{label:"Move Depth Pairs",value:9,icon:"CirclePlus"}]},S=h=>{switch(v.setActiveAction(h),h){case 8:v.depthPairsInstance?.CreateDepthPair();break;case 9:v.depthPairsInstance?.SwitchDepthPairEditable();break;case 6:v.isStartEdit&&v.curveEditInstance&&w.tabId&&v.curveEditInstance[w.tabId].handleCurveEdit();break;case"add_curveEdit":G(k);break;case"add_admAndDshift":G(P);break;case"startEdit":j();break;case"saveEdit":v.isStartEdit&&b();break}},G=(h=k)=>{M.value.some(g=>g.value===h.value)||(h.value==="curveEdit"?M.value.splice(1,0,h):M.value.push(h),h.value==="admAndDshift"&&l("add-tab","admAndDshift")),I.value=h.value},j=()=>{if(v.isStartEdit)v.setIsStartEdit(!1);else{const h=new FormData;h.append("id",v.curveId),Dt(h).then(m=>{m.success?v.setIsStartEdit(!0):ue.error(m.message)})}},T=h=>{const m=M.value.findIndex(g=>g.value===h);m>-1&&M.value.splice(m,1),I.value="home"},b=()=>{v.curveEditInstance[w.tabId].saveCurve()},X=h=>{const m=[];let g=[];for(const B of h)B.single?(g.length>0&&(m.push(g),g=[]),m.push([B])):(g.push(B),g.length===2&&(m.push(g),g=[]));return g.length>0&&m.push(g),m},c=h=>{v.setActiveAction(""),l("add-tab",h)};return R({setSelectedTab:h=>{I.value=h},handleEditTabs:G,handleRemoveTab:T}),he(()=>{const h=localStorage.getItem("theme")||"auto";document.documentElement.classList.toggle("dark",h==="dark"),u.value=h==="auto"}),(h,m)=>{const g=E("el-divider"),B=E("el-button"),x=E("el-tab-pane"),y=E("el-tabs");return L(),ee("div",Xt,[n(y,{modelValue:I.value,"onUpdate:modelValue":m[0]||(m[0]=A=>I.value=A),onTabChange:c},{default:s(()=>[(L(!0),ee(fe,null,Ie(M.value,(A,V)=>(L(),se(x,{key:V,label:A.label,name:A.value},{default:s(()=>[ae("div",Kt,[A.children?.length>0?(L(!0),ee(fe,{key:0},Ie(X(A.children),(H,r)=>(L(),ee(fe,{key:r},[H[0].single?(L(),se(g,{key:0,direction:"vertical"})):Re("",!0),H[0].single?(L(),ee("div",qt,[n(B,{icon:H[0].icon,onClick:N=>S(H[0].value),class:Ke({"is-active":ve(v).activeAction===H[0].value})},{default:s(()=>[Q(de(H[0].label),1)]),_:2},1032,["icon","onClick","class"])])):(L(),ee("div",Zt,[(L(!0),ee(fe,null,Ie(H,(N,_)=>(L(),se(B,{key:_,icon:N.icon,onClick:F=>S(N.value),class:Ke({"is-active":ve(v).activeAction===N.value&&ve(v).isStartEdit})},{default:s(()=>[Q(de(N.label),1)]),_:2},1032,["icon","onClick","class"]))),128))]))],64))),128)):Re("",!0)])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])],512)}}};function Jt(f){return vt()?(mt(f),!0):!1}const ea=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const ta=ea?window:void 0;function qe(f){var R;const W=Qe(f);return(R=W?.$el)!=null?R:W}function aa(){const f=ht(!1),R=Je();return R&&he(()=>{f.value=!0},R),f}function la(f){const R=aa();return je(()=>(R.value,!!f()))}function ot(f,R,W={}){const{window:v=ta,...l}=W;let w;const u=la(()=>v&&"ResizeObserver"in v),I=()=>{w&&(w.disconnect(),w=void 0)},M=je(()=>{const S=Qe(f);return Array.isArray(S)?S.map(G=>qe(G)):[qe(S)]}),k=Se(M,S=>{if(I(),u.value&&v){w=new ResizeObserver(R);for(const G of S)G&&w.observe(G,l)}},{immediate:!0,flush:"post"}),P=()=>{I(),k()};return Jt(P),{isSupported:u,stop:P}}const na={key:1},it={__name:"tableTextEdit",props:{tableData:{type:Array,default:()=>[]},tableHeader:{type:Array,default:()=>[]},startIndex:{type:Number,default:0},endIndex:{type:Number,default:20}},emits:["curve-data"],setup(f,{emit:R}){const W=R,v=f,l=we(),w=je(()=>v.tableData?v.tableData.slice(v.startIndex,v.endIndex):v.tableData);function u(M,k,P,S){let G=v.tableHeader[0].name;v.tableData.forEach((j,T)=>{j[G]===M[G]&&(v.tableData[T].editing=!0)})}function I(M,k){const P=v.startIndex+k;console.log(P),P<l.minIndex&&l.setMinIndex(P),P>l.maxIndex&&l.setMaxIndex(P),console.log(l.minIndex),console.log(l.maxIndex),W("curve-data",M)}return(M,k)=>{const P=E("el-input"),S=E("el-table-column"),G=E("el-table");return L(),ee("div",null,[n(G,{data:w.value,height:"100%",style:{width:"100%"},onCellClick:u},{default:s(()=>[(L(!0),ee(fe,null,Ie(f.tableHeader,(j,T)=>(L(),se(S,{key:j.name,label:j.alias,prop:j.name,width:j.width},{default:s(b=>[b.row.editing&&b.row.editing===!0&&T===1&&ve(l).isStartEdit?(L(),se(P,{key:0,onBlur:X=>I(b.row,b.$index),modelValue:b.row[j.name],"onUpdate:modelValue":X=>b.row[j.name]=X},null,8,["onBlur","modelValue","onUpdate:modelValue"])):(L(),ee("span",na,de(b.row[j.name]),1))]),_:2},1032,["label","prop","width"]))),128))]),_:1},8,["data"])])}}},oa={class:"dialog-footer"},rt={__name:"textEdit",emits:["update","done"],setup(f,{expose:R,emit:W}){const v=we(),l=W,w=i(""),u=i(!1);let I=[];function M(G,j){const T=JSON.parse(JSON.stringify(G));if(j===1)w.value=T.join(`
`),I=T;else{const b=T.reverse();w.value=b.join(`
`),I=b}u.value=!0}function k(G){const j=G.split(`
`);let T=null,b=null;j.forEach((X,c)=>{X!==I[c]&&(I[c]=X,(T===null||c<T)&&(T=c),(b===null||c>b)&&(b=c))}),T!==null&&(v.setMinIndex(v.minIndex===0?T:Math.min(v.minIndex,T)),v.setMaxIndex(v.maxIndex===0?b:Math.max(v.maxIndex,b)))}function P(){l("done",w.value.split(`
`)),u.value=!1}function S(){u.value=!1}return R({openTextEdit:M}),(G,j)=>{const T=E("el-input"),b=E("el-button"),X=E("el-dialog");return L(),se(X,{width:"400px",modelValue:u.value,"onUpdate:modelValue":j[4]||(j[4]=c=>u.value=c),"close-on-click-modal":!1,"modal-append-to-body":!1,"append-to-body":"",onClose:j[5]||(j[5]=c=>S()),title:"Curve Data Edit Text"},{footer:s(()=>[ae("div",oa,[n(b,{onClick:j[2]||(j[2]=c=>P())},{default:s(()=>j[6]||(j[6]=[Q("Save")])),_:1}),n(b,{onClick:j[3]||(j[3]=c=>S())},{default:s(()=>j[7]||(j[7]=[Q("Cancel")])),_:1})])]),default:s(()=>[n(T,{ref:"textarea",type:"textarea",autosize:{minRows:20,maxRows:20},modelValue:w.value,"onUpdate:modelValue":j[0]||(j[0]=c=>w.value=c),onInput:j[1]||(j[1]=c=>k(c))},null,8,["modelValue"])]),_:1},8,["modelValue"])}}},ia={__name:"curveVertical",props:{tabId:{type:String,default:""},curveData:{type:Object,default:{}}},emits:["updateCurveData"],setup(f,{expose:R,emit:W}){const v=f,l=we(),w=i(null),u=i(null),I=i([]),M=i([]),k=i([]),P=i(0),S=i(!1),G=i(""),j=i("");i("");const T=i([]),b=i(""),X=i(""),c=i(""),h=i(""),m=i(""),g=i([]),B=i(null),x=i(null),y=i(0),A=i(20),V=i(90),H=i(100);let r=!1,N="";ot(w,()=>{u.value?.resize()});const _=W;function F(){const a=Ve();u.value&&u.value.dispose();let t=[];t.push(M.value[1].alias),u.value=J(et(w.value)),u.value.setOption({title:{text:"Data Curve"},tooltip:{trigger:"axis"},legend:{data:t},grid:{left:20,right:50,bottom:"3%",containLabel:!0},toolbox:{feature:{saveAsImage:{}}},xAxis:{type:"value",boundaryGap:!1},yAxis:{type:"category",boundaryGap:[0,"100%"],data:k.value},dataZoom:[{type:"slider",yAxisIndex:0,start:90,end:100,filterMode:"empty"},{type:"inside",yAxisIndex:0,moveOnMouseMove:!1,start:90,end:100,filterMode:"empty"}],animation:!1,series:[{id:"a",name:M.value[1].alias,type:"line",data:a},{id:"b",type:"line",name:"data",color:"#ccc",data:[]},{id:"c",type:"line",color:"#f00",data:[]}],graphic:{type:"rect",invisible:!0,shape:{x:100,y:50,width:200,height:300},style:{fill:"rgba(255, 0, 0, 0.5)",stroke:"#red",lineWidth:2}}}),u.value.getZr().on("mousedown",q),u.value.getZr().on("mousemove",le),u.value.getZr().on("mouseup",oe),y.value=0,A.value=20,u.value.on("dataZoom",function(e){e.end?(V.value=e.start,H.value=e.end,y.value=Math.floor(P.value*(100-e.end)/100)):(V.value=e.batch[0].start,H.value=e.batch[0].end,y.value=Math.floor(P.value*(100-e.batch[0].end)/100)),A.value=y.value+20})}function O(){x.value.openTextEdit(g.value,2)}function q(a){S.value=!0,G.value=a.offsetX,j.value=a.offsetY;let t=[a.offsetX,a.offsetY],e=u.value.convertFromPixel("grid",t);b.value=e[1],X.value=e[0].toFixed(4);let d=P.value-e[1]-1;l.activeAction===1&&(T.value=[]);const o=Math.max(d,0);(l.minIndex===0||o<l.minIndex)&&l.setMinIndex(o)}function le(a){const t=a.offsetX;let e=B.value.offsetWidth;t>65&&t<e-350&&S.value&&l.isStartEdit&&(l.activeAction===1?re(a):l.activeAction===2?ie(a):l.activeAction===3?pe(a):l.activeAction===4?xe(a):l.activeAction===5&&Ce(a))}function oe(a){S.value=!1;const t=J(m.value),e=J(T.value),d=J(I.value),o=J(g.value);if(l.activeAction===1){let p=[a.offsetX,a.offsetY],C=u.value.convertFromPixel("grid",p),D=C[1],$=P.value-C[1]-2;$>l.maxIndex&&l.setMaxIndex($);for(let Y=e.length;Y>D;Y--)if(typeof e[Y]<"u"){const z=d.length-1-Y;z>=0&&z<d.length&&(d[z][t]=e[Y],o[Y]=e[Y])}u.value.setOption({series:[{id:"a",data:o}]}),u.value.setOption({series:[{id:"c",data:[]}]})}else if(l.activeAction===2){let p=P.value-b.value-1;p>l.maxIndex&&l.setMaxIndex(p)}else if(l.activeAction===3){let p=[a.offsetX,a.offsetY],C=u.value.convertFromPixel("grid",p),D=P.value-C[1]-1;D>l.maxIndex&&l.setMaxIndex(D)}else if(l.activeAction===5){let p=[a.offsetX,a.offsetY],C=u.value.convertFromPixel("grid",p),D=[];for(let U=b.value;U>C[1];U--){const z=parseFloat(o[U]);z!=-9999&&z!=-9999.25&&z!=-999.25&&!isNaN(z)&&D.push(z)}let $=Pe(D,5);if($.length>1)for(let U=0;U<$.length;U++)o[b.value-U]=$[U].toFixed(4),d[d.length-1-(b.value-U)][t]=$[U].toFixed(4);let Y=P.value-C[1]-1;Y>l.maxIndex&&l.setMaxIndex(Y),u.value.setOption({series:[{id:"a",data:o}],graphic:{invisible:!0}})}else l.activeAction===7&&(y.value=P.value-b.value-1,A.value=P.value-(b.value-18-1));b.value="",c.value="",h.value="",T.value=[],be()}function re(a){let t=[a.offsetX,a.offsetY],e=u.value.convertFromPixel("grid",t);const d=e[1],o=e[0];if(c.value!=""&&d!=c.value+1)for(let p=c.value;p>d;p--)T.value[p]=((o-h.value)/(c.value-d)+o).toFixed(4);T.value[d]=o.toFixed(4),u.value.setOption({series:[{id:"c",data:T.value}]}),c.value=d,h.value=o}function ie(a){const t=J(I.value),e=J(g.value),d=J(k.value);let o=[a.offsetX,a.offsetY],p=u.value.convertFromPixel("grid",o);t[t.length-1-b.value][m.value]=p[0].toFixed(4),e[b.value]=p[0].toFixed(4);let C=b.value-5,D=b.value+5;C<0&&(C=0),D>=e.length&&(D=e.length-1);let $=d[d.length-1-C],Y=e[C],U=d[d.length-1-b.value],z=e[b.value],Z=d[d.length-1-D],ce=e[D];for(let te=C;te<b.value;te++){let ne=ye($,Y,U,z,d[d.length-1-te]);isNaN(ne)||(e[te]=ne,t[t.length-1-te][m.value]=ne)}for(let te=b.value+1;te<D;te++){let ne=ye(U,z,Z,ce,d[d.length-1-te]);isNaN(ne)||(e[te]=ne,t[t.length-1-te][m.value]=ne)}u.value.setOption({series:[{id:"a",data:e}]})}function pe(a){const t=J(I.value),e=J(g.value);let d=[a.offsetX,a.offsetY];const p=u.value.convertFromPixel("grid",d)[1],C=t.length-1-p;if(!(C<0||C>=t.length)){if(c.value!=""&&p!=c.value-1)for(let D=p;D<c.value;D++)t[t.length-1-D][m.value]=X.value,e[D]=X.value;t[t.length-1-p][m.value]=X.value,e[p]=X.value,u.value.setOption({series:[{id:"a",data:e}]}),c.value=p}}function xe(a){r=!0;const t=J(I.value),e=J(g.value),d=J(m.value);let o=[a.offsetX,a.offsetY];const C=u.value.convertFromPixel("grid",o)[0];let D=parseFloat(C)-X.value;D>10&&(D=10),D<-10&&(D=-10);for(let $=0;$<e.length;$++){let Y=parseFloat(t[$][d]),U=parseFloat(e[$]);Y!=-9999&&Y!=-9999.25&&Y!=-999.25&&(t[$][d]=(Y+D).toFixed(4)),U!=-9999&&U!=-9999.25&&U!=-999.25&&(e[$]=(U+D).toFixed(4))}u.value.setOption({series:[{id:"a",data:e}]})}function Ce(a){let t=a.event.offsetY-j.value,e=B.value.offsetWidth;u.value.setOption({graphic:{type:"rect",invisible:!1,shape:{x:65,y:j.value,width:e-415,height:t},style:{fill:"rgba(255, 0, 0, 0.5)",stroke:"#red",lineWidth:2}}})}function Pe(a,t){let e=0,d=[],o,p;for(o=0;o<t;o++)e+=a[o];for(d.push(e/t),p=t;p<a.length;p++)e=e-a[p-t]+a[p],d.push(e/t);return d}function ye(a,t,e,d,o){try{let p=new Decimal(d).minus(new Decimal(t)).div(new Decimal(e).minus(new Decimal(a))).toFixed(4);return new Decimal(o).minus(new Decimal(a)).times(new Decimal(p)).plus(new Decimal(t)).toFixed(4)}catch{return"_"}}function Me(a){const t=J(I.value),e=J(g.value);t.forEach((d,o)=>{if(d[N]===a[N]){t[o].editing=!1;const p=t[o][m.value];parseFloat(p)===-9999||parseFloat(p)===-9999.25||parseFloat(p)===-999.25?e[e.length-1-o]="_":e[e.length-1-o]=p,u.value.setOption({series:[{id:"a",data:e}]})}}),be()}function Te(a){const t=J(I.value),e=J(g.value);if(document.querySelectorAll(".button").forEach(o=>{o.style.backgroundColor="#fff",o.style.color="#000"}),a){for(let o=0;o<a.length;o++)a[o]!="_"&&(e[e.length-1-o]=a[o],t[o][m.value]=a[o]);u.value.setOption({series:[{id:"a",data:e}]})}be()}function Ve(){return g.value=[],I.value.forEach((a,t)=>{let e=a[m.value];parseFloat(e)===-9999||parseFloat(e)===-9999.25||parseFloat(e)===-999.25?g.value.push("_"):g.value.push(e)}),g.value.reverse(),g.value}function be(){const a={...v.curveData,data:[...g.value.slice().reverse()]};_("updateCurveData",a)}function ke(){requestAnimationFrame(()=>{u.value?.resize()})}function Ee(){const a=[...g.value].map(Number).reverse(),t=r?a:a.slice(l.minIndex,l.maxIndex+1),e=new FormData;e.append("wellId",l.wellId),e.append("datasetId",l.datasetId),e.append("id",l.curveId),e.append("startIndex",r?0:l.minIndex),e.append("data",t.join(",")),tt(e).then(d=>{d.success?(l.setIsStartEdit(!1),l.setMinIndex(0),l.setMaxIndex(0),r=!1,ue({message:"操作成功",type:"success"})):ue({message:d.message,type:"error"})})}he(async()=>{Se(()=>v.curveData,a=>{if(!a?.index?.indexValue?.length||!a?.data?.length)return;N=a.index.indexType===0?"Depth":"Time";let t=a.index.indexUnit,e=a.name,d=a.unit;k.value=a.index.indexValue,g.value=a?.data||[],I.value=a.index.indexValue.map((o,p)=>({[e]:g.value[p]?.toString()||"-9999.0",[N]:o.toString()})),M.value=[{name:N,alias:N+"("+t+")",width:"130"},{name:e,alias:e+"("+d+")",width:"170"}],P.value=I.value.length,parseFloat(k.value[0])<k.value[k.value.length-1]&&k.value.reverse(),m.value=a.name,F()},{immediate:!0}),window.addEventListener("resize",ke),l.registerCurveEdit(v.tabId,_e)});const _e={handleCurveEdit:O,saveCurve:Ee};return R(_e),Fe(()=>{window.removeEventListener("resize",ke),u.value&&(u.value.dispose(),u.value=null)}),(a,t)=>(L(),ee("div",{class:"curveVertical",ref_key:"curveVerticalContainer",ref:B},[ae("div",{ref_key:"echartsContainer",ref:w,style:{width:"calc(100% - 300px)",height:"100%"}},null,512),n(it,{tableData:I.value,tableHeader:M.value,startIndex:y.value,endIndex:A.value,onCurveData:Me},null,8,["tableData","tableHeader","startIndex","endIndex"]),n(rt,{ref_key:"textEditRef",ref:x,onDone:Te},null,512)],512))}},ra={__name:"curveHorizontal",props:{tabId:{type:String,default:""},curveData:{type:Object,default:{}}},emits:["updateCurveData"],setup(f,{expose:R,emit:W}){const v=f,l=we(),w=i(null),u=i(null),I=i([]),M=i([]),k=i([]),P=i(0),S=i(!1),G=i(""),j=i("");i("");const T=i([]),b=i(""),X=i(""),c=i(""),h=i(""),m=i(""),g=i([]),B=i(null),x=i(null),y=i(0),A=i(20),V=i(90),H=i(100);let r=!1,N="";ot(w,()=>{u.value?.resize()});const _=W;function F(){u.value&&u.value.dispose();let a=[];a.push(M.value[1].alias),u.value=J(et(w.value));const t=Ve(),d=[...J(k.value)];u.value.setOption({title:{text:"Data Curve"},tooltip:{trigger:"axis"},legend:{data:a},grid:{left:20,right:50,bottom:"3%",containLabel:!0},toolbox:{feature:{saveAsImage:{}}},xAxis:{type:"category",boundaryGap:!1,data:d},yAxis:{type:"value"},dataZoom:[{type:"slider",start:0,end:10,filterMode:"empty"},{type:"inside",moveOnMouseMove:!1,start:0,end:10,filterMode:"empty"}],animation:!1,series:[{id:"a",name:M.value[1].alias,type:"line",data:t},{id:"b",type:"line",name:"data",color:"#ccc",data:[]},{id:"c",type:"line",color:"#f00",data:[]}],graphic:{type:"rect",invisible:!0,shape:{x:100,y:50,width:200,height:300},style:{fill:"rgba(255, 0, 0, 0.5)",stroke:"#red",lineWidth:2}}}),u.value.getZr().on("mousedown",q),u.value.getZr().on("mousemove",le),u.value.getZr().on("mouseup",oe),y.value=0,A.value=20,u.value.on("dataZoom",function(o){o.end?(V.value=o.start,H.value=o.end,y.value=Math.floor(P.value*o.start/100)):(V.value=o.batch[0].start,H.value=o.batch[0].end,y.value=Math.floor(P.value*o.batch[0].start/100)),A.value=y.value+20})}function O(){x.value.openTextEdit(g.value,1)}function q(a){S.value=!0,G.value=a.offsetX,j.value=a.offsetY;let t=[a.offsetX,a.offsetY],e=u.value.convertFromPixel("grid",t);b.value=e[0],X.value=e[1].toFixed(4);let d=e[0];l.activeAction===1&&(T.value=[]);const o=Math.max(d,0);(l.minIndex===0||d<l.minIndex)&&l.setMinIndex(o)}function le(a){const t=a.offsetY;let e=B.value.offsetHeight;t>60&&t<e-43&&S.value&&(l.activeAction===1?re(a):l.activeAction===2?ie(a):l.activeAction===3?pe(a):l.activeAction===4?xe(a):l.activeAction===5&&Ce(a))}function oe(a){S.value=!1;const t=J(m.value),e=J(T.value),d=J(I.value),o=J(g.value);let p=[a.offsetX,a.offsetY],C=u.value.convertFromPixel("grid",p);if(l.activeAction===1){let D=C[0];D>l.maxIndex&&l.setMaxIndex(D);for(let $=b.value+1;$<e.length;$++){let Y=e[$];typeof Y<"u"&&(d[$][t]=Y,o[$]=Y)}u.value.setOption({series:[{id:"a",data:g.value}]}),u.value.setOption({series:[{id:"c",data:[]}]})}else if(l.activeAction===2){let D=b.value;D>l.maxIndex&&l.setMaxIndex(D)}else if(l.activeAction===3){let D=C[0];D>l.maxIndex&&l.setMaxIndex(D)}else if(l.activeAction===5){let D=[a.offsetX,a.offsetY],$=u.value.convertFromPixel("grid",D),Y=[];for(let Z=b.value;Z<$[0];Z++){const ce=parseFloat(g.value[Z]);ce!=-9999&&ce!=-9999.25&&ce!=-999.25&&!isNaN(ce)&&Y.push(ce)}let U=Pe(Y,5);if(U.length>1)for(let Z=0;Z<U.length;Z++)o[Z+b.value]=U[Z].toFixed(4),d[Z+b.value][t]=U[Z].toFixed(4);let z=$[0];z>l.maxIndex&&l.setMaxIndex(z),u.value.setOption({series:[{id:"a",data:g.value}],graphic:{invisible:!0}})}else l.activeAction===7&&(y.value=b.value-1,A.value=b.value+20-1);b.value="",c.value="",h.value="",T.value=[],be()}function re(a){let t=[a.offsetX,a.offsetY],e=u.value.convertFromPixel("grid",t);const d=e[0],o=e[1];if(c.value!=""&&d!=c.value+1)for(let p=c.value;p<d;p++)T.value[p]=((o-h.value)/(d-c.value)+o).toFixed(4);T.value[d]=o.toFixed(4),u.value.setOption({series:[{id:"c",data:T.value}]}),c.value=d,h.value=o}function ie(a){const t=J(I.value),e=J(g.value),d=J(k.value);let o=[a.offsetX,a.offsetY],p=u.value.convertFromPixel("grid",o);t[b.value][m.value]=p[1].toFixed(4),e[b.value]=p[1].toFixed(4);let C=b.value-5,D=b.value+5;C<0&&(C=0),D>=e.length&&(D=e.length-1);let $=d[C],Y=e[C],U=d[b.value],z=e[b.value],Z=d[D],ce=e[D];for(let te=C;te<b.value;te++){let ne=ye($,Y,U,z,d[te]);isNaN(ne)||(e[te]=ne,t[te][m.value]=ne)}for(let te=b.value+1;te<D;te++){let ne=ye(U,z,Z,ce,d[te]);isNaN(ne)||(e[te]=ne,t[te][m.value]=ne)}u.value.setOption({series:[{id:"a",data:g.value}]})}function pe(a){const t=J(I.value),e=J(g.value);let d=[a.offsetX,a.offsetY];const p=u.value.convertFromPixel("grid",d)[0];if(c.value!=""&&p!=c.value+1)for(let C=c.value;C<p;C++)t[C][m.value]=X.value,e[C]=X.value;t[p][m.value]=X.value,e[p]=X.value,u.value.setOption({series:[{id:"a",data:g.value}]}),c.value=p}function xe(a){r=!0;const t=J(I.value),e=J(g.value),d=J(m.value);let o=[a.offsetX,a.offsetY];const C=u.value.convertFromPixel("grid",o)[0];let D=parseFloat(C)-X.value;D>10&&(D=10),D<-10&&(D=-10);for(let $=0;$<e.length;$++){let Y=parseFloat(t[$][d]),U=parseFloat(e[$]);Y!=-9999&&Y!=-9999.25&&Y!=-999.25&&(t[$][d]=(Y+D).toFixed(4)),U!=-9999&&U!=-9999.25&&U!=-999.25&&(e[$]=(U+D).toFixed(4))}u.value.setOption({series:[{id:"a",data:e}]})}function Ce(a){let t=a.event.offsetX-G.value,e=B.value.offsetHeight;u.value.setOption({graphic:{type:"rect",invisible:!1,shape:{x:G.value,y:60,width:t,height:e-105},style:{fill:"rgba(255, 0, 0, 0.5)",stroke:"#red",lineWidth:2}}})}function Pe(a,t){let e=0,d=[],o,p;for(o=0;o<t;o++)e+=a[o];for(d.push(e/t),p=t;p<a.length;p++)e=e-a[p-t]+a[p],d.push(e/t);return d}function ye(a,t,e,d,o){try{let p=new Decimal(d).minus(new Decimal(t)).div(new Decimal(e).minus(new Decimal(a))).toFixed(4);return new Decimal(o).minus(new Decimal(a)).times(new Decimal(p)).plus(new Decimal(t)).toFixed(4)}catch{return"_"}}function Me(a){const t=J(I.value),e=J(g.value);t.forEach((d,o)=>{if(d[N]===a[N]){t[o].editing=!1;const p=t[o][m.value];parseFloat(p)===-9999||parseFloat(p)===-9999.25||parseFloat(p)===-999.25?e[o]="_":e[o]=p,u.value.setOption({series:[{id:"a",data:e}]})}}),be()}function Te(a){const t=J(I.value),e=J(g.value);if(document.querySelectorAll(".button").forEach(o=>{o.style.backgroundColor="#fff",o.style.color="#000"}),a){for(let o=0;o<a.length;o++)a[o]!="_"&&(e[o]=a[o],t[o][m.value]=a[o]);u.value.setOption({series:[{id:"a",data:e}]})}be()}function Ve(){return g.value=[],I.value.forEach((a,t)=>{let e=a[m.value];parseFloat(e)===-9999||parseFloat(e)===-9999.25||parseFloat(e)===-999.25?g.value.push("_"):g.value.push(e)}),g.value}function be(){const a={...v.curveData,data:[...g.value]};_("updateCurveData",a)}function ke(){requestAnimationFrame(()=>{u.value?.resize()})}function Ee(){const a=[...g.value].map(Number),t=r?a:a.slice(l.minIndex,l.maxIndex+1),e=new FormData;e.append("wellId",l.wellId),e.append("datasetId",l.datasetId),e.append("id",l.curveId),e.append("startIndex",r?0:l.minIndex),e.append("data",t.join(",")),tt(e).then(d=>{d.success?(l.setIsStartEdit(!1),l.setMinIndex(0),l.setMaxIndex(0),r=!1,ue({message:"操作成功",type:"success"})):ue({message:d.message,type:"error"})})}he(async()=>{Se(()=>v.curveData,a=>{if(!a?.index?.indexValue?.length||!a?.data?.length)return;N=a.index.indexType===0?"Depth":"Time";let t=a.index.indexUnit,e=a.name,d=a.unit;k.value=a.index.indexValue,parseFloat(k.value[0])>k.value[k.value.length-1]&&k.value.reverse(),g.value=a.data,I.value=a.index.indexValue.map((o,p)=>({[e]:g.value[p]?.toString()||"-9999.0",[N]:o.toString()})),M.value=[{name:N,alias:N+"("+t+")",width:"130"},{name:e,alias:e+"("+d+")",width:"170"}],P.value=I.value.length,m.value=a.name,F()},{immediate:!0}),window.addEventListener("resize",ke),l.registerCurveEdit(v.tabId,_e)});const _e={handleCurveEdit:O,saveCurve:Ee};return R(_e),Fe(()=>{window.removeEventListener("resize",ke),u.value&&(u.value.dispose(),u.value=null)}),(a,t)=>(L(),ee("div",{class:"curveVertical",ref_key:"curveHorizontalContainer",ref:B},[ae("div",{ref_key:"echartsContainer",ref:w,style:{width:"calc(100% - 300px)",height:"100%"}},null,512),n(it,{tableData:I.value,tableHeader:M.value,startIndex:y.value,endIndex:A.value,onCurveData:Me},null,8,["tableData","tableHeader","startIndex","endIndex"]),n(rt,{ref_key:"textEditRef",ref:x,onDone:Te},null,512)],512))}},sa={class:"CurveEdit"},da={__name:"index",props:{tabId:{type:String,default:""},info:{type:Object,default:()=>({})}},emits:["curveTabClose"],setup(f,{emit:R}){const W=R,v=i(!0),l=we(),w=i(null),u=i(!1),I=f;let M=null;Se(v,()=>{l.setActiveAction("startEdit"),M&&(w.value=M)});const k=S=>{M=S},P=async()=>{try{u.value=!0;const S=await at(I.info);S?.success?w.value=S.data:w.value=null}catch(S){console.error("请求异常:",S),w.value=null}finally{u.value=!1}};return he(()=>{P()}),Fe(()=>{W("curveTabClose",I.tabId)}),(S,G)=>{const j=E("el-switch"),T=E("el-empty"),b=$e("loading");return Be((L(),ee("div",sa,[w.value?(L(),ee(fe,{key:0},[n(j,{modelValue:v.value,"onUpdate:modelValue":G[0]||(G[0]=X=>v.value=X),"inline-prompt":"","active-text":"Vertical","inactive-text":"Horizontal"},null,8,["modelValue"]),(L(),se(gt(v.value?ia:ra),{"tab-id":f.tabId,curveData:w.value,onUpdateCurveData:k},null,40,["tab-id","curveData"]))],64)):(L(),se(T,{key:1,description:"No Data"}))])),[[b,u.value]])}}},st=Ye("dataStore",{state:()=>({offsetChannelId:"",preDepthShift:0,tableData:[],baseUnit:""}),actions:{setOffsetChannelId(f){this.offsetChannelId=f},setPreDepthShift(f){this.preDepthShift=f},setTableData(f){this.tableData=f},setBaseUnit(f){this.baseUnit=f}}}),ua={style:{"font-weight":"bold"}},ca={style:{"font-weight":"bold"}},fa={key:1},pa={key:1},va={class:"clickRightMenu",id:"depthPairContextMenu",style:{display:"none",width:"150px"}},ma={class:"dialog-footer"},ha={__name:"depthPairs",setup(f,{expose:R}){const W=ze(),v=st(),l=we(),{proxy:w}=Je(),u=i({}),I=i(null),M=i(null),k=i(""),P=i(""),S=i("10"),G=i(!1),j=i(!1),T=i({CorrWinLength:16,Corrthreshold:.8,SearchLength:4,MaxDepthPairs:100}),b=i(""),X={children:"children",label:"name",value:"id"},c=i([]);Se(c,t=>{v.setTableData(t)},{deep:!0});const h=()=>{const t=r.getPlotParams();r.updatePlotParams(t.startIndex_,t.endIndex_,S.value)};function m(){v.setPreDepthShift(parseInt(k.value)),r.updatePlotPrsObj(r.getPlotTrackObjs("offset")[0].objId_,{offset:k.value?parseInt(k.value):0})}function g(){c.value.push({id:Te(),base:-9999,offset:-9999,isEdit:""})}function B(){c.value=c.value.filter(t=>t.id!=b.value),r.deleteDepthPair(b.value)}function x(){Ge.confirm("Are you sure to delete all these "+c.value.length+" depth pairs").then(()=>{for(let t=0;t<c.value.length;t++){const e=c.value[t];r.deleteDepthPair(e.id)}c.value=[]})}function y(t,e,d,o){c.value.forEach(p=>{p.id===t.id&&(e.property==="base"?p.isEdit="base":p.isEdit="offset")})}function A(t,e,d){b.value=t.id}function V(t){c.value.forEach(e=>{e.id===t.id&&(e.isEdit="",e.base!=-9999&&e.offset!=-9999&&(r.deleteDepthPair(e.id),r.createDepthPairLineWithDepth(e.id,r.getPlotTrackObjs("base")[0].objId_,e.base,r.getPlotTrackObjs("offset")[0].objId_,e.offset)))})}function H(){T.value={CorrWinLength:16,Corrthreshold:.8,SearchLength:4,MaxDepthPairs:100}}let r=De({}),N=De({userId:"",id:"",plotName:"",plotMemo:"",wellboreName:"",markerLayerId:"",startIndex:0,endIndex:0,currentTopIndex:0,depthRatio:0,logPlotTracks:[],dataEntries:[],logPlotMarkerlayers:[],logPlotBookmarkers:[]});De({templateId:null,plotTrackName:"Depth",plotTrackIndex:0,trackType:"depth",trackLinearity:2,trackWidth:60,showHorizontalGrid:!0,showVerticalGrid:!0,horizontalMajorGridSpacing:50,horizontalMinorGridSpacing:10,verticalMajorGridSpacing:2,verticalMinorGridSpacing:5,logPlotPrsObjs:[],tag:null,orderCol:"",entityType:1,name:"LogPlotTrack",nameSpace:"LogHub.FrameWork.DataModel.Log",pk:"Id",fields:"_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",state:0,dataDic:{},id:"67403a2dc75fcbfdb61eb2c0",owerId:null,departId:null,companyId:null,shareState:0}),De({trackID:"7992cf2c4cd12d67064e9866",plotPrsName:"K",prsIndex:1,prsColor:"#1364FF",prsType:1,prsLineType:1,lValue:.07,rValue:10.4,prsLineThickness:2,palettes:"",bindLogName:"640f1bf2cbde4b8cb2155c6b7b2508d2.las",bindChannelSetIndex:"0",bindChannelName:"K",segmentDepth:100,unit:"%",paletteColors:null,tag:null,orderCol:"",entityType:1,name:"LogPlotCurve",nameSpace:"LogHub.FrameWork.DataModel.Log",pk:"Id",fields:"_id,TrackId,PlotPrsName,PrsIndex,PrsColor,PrsType,PrsLineType,LValue,RValue,PrsLineThickness,Palettes,BindLogName,BindChannelSetIndex,BindChannelName,SegmentDepth",state:0,dataDic:{},id:"be0dc62ad53ec129bb417991",owerId:null,departId:null,companyId:null,shareState:0}),De({logPrsObjId:"f6feacf133ade2110589abb8",prsType:"curve",logId:"61dcd3a3990b27df23a74ea5",logName:"640f1bf2cbde4b8cb2155c6b7b2508d2.las",channelSetIndex:"0",channelName:"GRSL",index:null,lastDataIndex:23538,val:null,channelUnit:"gAPI",startIndex:1678.969970703125,endIndex:3472.565570703125,palettes:"",minValueValid:0,maxValueValid:0});let _=De({});function F(){return((1+Math.random())*65536|0).toString(16).substring(1)}function O(){return F()+F()+F()+F()+F()+F()}O();function q(){const t=document.getElementById("well-log-app");if(!t){console.error("未找到容器元素");return}t.innerHTML="",N=le(),oe(t,N)}function le(){return{userId:"",id:"",plotName:"",plotMemo:"",wellboreName:"",markerLayerId:"",startIndex:2e3,endIndex:2400,currentTopIndex:0,depthRatio:100,logPlotTracks:[{templateId:null,plotTrackName:"Depth",plotTrackIndex:0,trackType:"depth",trackLinearity:2,trackWidth:60,showHorizontalGrid:!0,showVerticalGrid:!0,horizontalMajorGridSpacing:50,horizontalMinorGridSpacing:10,verticalMajorGridSpacing:2,verticalMinorGridSpacing:5,logPlotPrsObjs:[],tag:null,orderCol:"",entityType:1,name:"LogPlotTrack",nameSpace:"LogHub.FrameWork.DataModel.Log",pk:"Id",fields:"_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",state:0,dataDic:{},id:"67622f122843af4da4283872",owerId:null,departId:null,companyId:null,shareState:0},{templateId:null,plotTrackName:"Base",plotTrackIndex:1,trackType:"normal",trackLinearity:1,trackWidth:250,showHorizontalGrid:!0,showVerticalGrid:!0,horizontalMajorGridSpacing:0,horizontalMinorGridSpacing:0,verticalMajorGridSpacing:4,verticalMinorGridSpacing:5,logPlotPrsObjs:[],tag:null,orderCol:"",entityType:1,name:"LogPlotTrack",nameSpace:"LogHub.FrameWork.DataModel.Log",pk:"Id",fields:"_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",state:0,dataDic:{},id:"base",owerId:null,departId:null,companyId:null,shareState:0},{templateId:null,plotTrackName:"Offset",plotTrackIndex:2,trackType:"normal",trackLinearity:1,trackWidth:250,showHorizontalGrid:!0,showVerticalGrid:!0,horizontalMajorGridSpacing:0,horizontalMinorGridSpacing:0,verticalMajorGridSpacing:4,verticalMinorGridSpacing:5,logPlotPrsObjs:[],tag:null,orderCol:"",entityType:1,name:"LogPlotTrack",nameSpace:"LogHub.FrameWork.DataModel.Log",pk:"Id",fields:"_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",state:0,dataDic:{},id:"offset",owerId:null,departId:null,companyId:null,shareState:0}],dataEntries:[],logPlotMarkerlayers:[],logPlotBookmarkers:[]}}function oe(t,e){const d=pe(e.logPlotMarkerlayers||[]),o=xe(e.logPlotBookmarkers||[]),p=Ce(e.logPlotTracks||[]);r=new WebCanvas.TWCLogPlot({appContainer:t,plot:{measure:{unit:"meter",ratio:e.depthRatio,startIndex:e.startIndex,endIndex:e.endIndex},tracks:p,bookmarkers:o,topsets:d}}),r.updatePlotParams(e.startIndex,e.endIndex,e.depthRatio),r.goToDepthIndex(e.startIndex),r.setDepthPairLineContextMenuFunc(function(C){P.value=C.depthPairId,document.getElementById("depthPairContextMenu").style.display="block",document.getElementById("depthPairContextMenu").style.left=C.wCanvas_.lastMouseMovePos.x+563+"px",document.getElementById("depthPairContextMenu").style.top=C.wCanvas_.lastMouseMovePos.y+42+"px"}),r.setMouseOverPlotChartFunc(function(C,D,$,Y){var U=j.value?"Time":"Depth",z="<b>"+U+"：</b>"+C+D;ie.ShowTip(this,z,$,Y)}),r.setMouseLeavePlotChartFunc(function(){document.getElementById("tipPanel")!=null&&(document.getElementById("tipPanel").style.display="none")}),r.setDepthPairEditedFunction(function(C,D,$){let Y=!0;if(c.value&&c.value.length>0)for(let U=0;U<c.value.length;U++)C===c.value[U].id&&(c.value[U].base=D,c.value[U].offset=$,Y=!1);Y&&c.value.push({id:C,base:D,offset:$,isEdit:""})}),r.getDepthPairEditable()}function re(t){var e=new Object;return e.x=t.offsetLeft,e.y=t.offsetTop,e}var ie={_tipPanel:null,Init:function(){if(this._tipPanel==null){var t=document.createElement("div");document.body.insertBefore(t,document.body.childNodes[0]),t.id="tipPanel",t.style.display="none",t.style.position="absolute",t.style.zIndex="999"}},AttachTip:function(){},DetachTip:function(){},ShowTip:function(t){if(document.getElementById("tipPanel")!=null){var e="<p>"+arguments[1]+"</p>";document.getElementById("tipPanel").innerHTML=e;var d=re(document.getElementById("well-log-app1-"));document.getElementById("tipPanel").style.left=1*parseInt(arguments[2])+d.x+10+"px",document.getElementById("tipPanel").style.top=1*parseInt(arguments[3])+d.y+10+"px",document.getElementById("tipPanel").style.display=""}},HideTip:function(){document.getElementById("tipPanel")!=null&&(document.getElementById("tipPanel").style.display="none")}};function pe(t){return t.map(e=>({Id:e.id,LayerName:e.layerName,MD:e.md}))}function xe(t){return t.map(e=>({Id:e.id,Description:e.description,MD:e.md}))}function Ce(t){return t.map(e=>{const d=(e.logPlotPrsObjs||[]).map(o=>({chName:o.plotPrsName,Id:o.id,type:o.prsType===1?"curve":"image",color:o.prsColor,lineStyle:o.prsLineType,lineThickness:o.prsLineThickness,segmentDataDepth:o.segmentDepth,paletteColors:o.paletteColors,label:o.plotPrsName,unit:o.unit,index:o.prsIndex,lower:o.lValue,upper:o.rValue,onEdit:function(p){editPrsObj(p)}}));return{type:e.trackType,label:e.plotTrackName,Id:e.id,valueScaleType:e.trackLinearity===1?"linear":"log",index:e.plotTrackIndex,width:e.trackWidth,showVerticalGrid:e.showVerticalGrid,showHorizontalGrid:e.showHorizontalGrid,horizontalMajorGridSpacing:e.horizontalMajorGridSpacing,horizontalMinorGridSpacing:e.horizontalMinorGridSpacing,verticalMajorGridSpacing:e.verticalMajorGridSpacing,verticalMinorGridSpacing:e.verticalMinorGridSpacing,logPrsVos:d,onEdit:function(o){editTrack(o)}}})}function Pe(t){c.value;for(let d=0;d<c.value.length;d++){const o=c.value[d];r.deleteDepthPair(o.id)}c.value=[],S.value="10";let e="";t==="base"?e=I.value:(e=M.value,v.setOffsetChannelId(e)),_={channelID:e,channelName:ye(W.projectTreeData,e).name,color:"#114672",maxValueValid:500,minValueValid:0},Me(t)}function ye(t,e){function d(o,p=null){for(const C of o){if(C.id===e)return{name:C.name,parentNode:p};if(C.children){const D=d(C.children,C);if(D)return D}}return null}return d(t)}async function Me(t){const e=r.getPlotTrack(t),d=e.objs_;d&&d.length>0&&r.deletePlotPrsObj(d[0].objId_);const o=e.parent_.objs_,p=[];for(const ne of o)if(ne.ObjType==="Track")for(const K of ne.objs_)p.push(K);const C=O();let D=!1;for(const ne of p)if(ne.objId_===C){D=!0;break}if(D)return w.$notify({title:"提示信息",message:"测井图中已存在该对象",type:"warning",duration:2e3}),!1;const $=_.channelID;let Y=ye(W.projectTreeData,$).parentNode.id,z=(await at({wellId:l.wellId,datasetId:Y,id:$})).data;t==="base"&&v.setBaseUnit(z.unit);const Z={chName:_.channelName,Id:C,index:1,type:"curve",color:_.color,lineStyle:1,lineThickness:2,segmentDataDepth:z.index.indexValue[z.index.indexValue.length-1],label:_.channelName,unit:z.unit,paletteColors:[],lower:_.minValueValid,upper:_.maxValueValid},ce=r.getPlotParams();p.length===0?r.updatePlotParams(z.index.indexValue[0],z.index.indexValue[z.index.indexValue.length-1],10):r.updatePlotParams(Math.min(ce.startIndex_,z.index.indexValue[0]),Math.max(ce.endIndex_,z.index.indexValue[z.index.indexValue.length-1]),10),r.addPlotPrsObj(e.trackId_,Z);let te=new WebCanvas.LogPrsDataContainer({logPrsObjId:Z.Id,chName:_.channelName,channelSetIndex:_.setIndexID,startIndex:z.index.indexValue[0],endIndex:z.index.indexValue[z.index.indexValue.length-1],palettes:Z.paletteColors,type:Z.type,getDataFunc:function(ne,K,me,Ca,ka,dt,ut){const Xe=[];if(z.data!=null&&z.data.length>0&&me==="curve"){for(let Ne=0;Ne<z.data.length;Ne++){const ct={tvd:z.index.indexValue[Ne],y:z.data[Ne]};Xe.push(ct)}r.setNewData(ne,dt,ut,Xe)}r.setDataLoadingStatus(ne,!1)}});r.addDataContainer([te])}function Te(){return((1+Math.random())*65536|0).toString(16).substring(1)}function Ve(){r.createNewDepthPairLine(Te())}function be(){r.switchDepthPairEditable()}function ke(){r.deleteSelectedDepthPair();for(let t=0;t<c.value.length;t++)c.value[t].id===P.value&&c.value.splice(t,1);Ee()}function Ee(){r.clearContextMenu()}function _e(t,e){function d(p,C){for(const D of C){if(D.id===p)return[D];if(D.children&&D.children.length>0){const $=d(p,D.children);if($.length>0)return[D,...$]}}return[]}const o=d(e,t);return o.length>0?o.map(p=>p.name).join(" > "):""}function a(){console.log(),I.value=null,M.value=null,S.value="10";for(let t=0;t<c.value.length;t++){const e=c.value[t];r.deleteDepthPair(e.id)}if(c.value=[],Reflect.ownKeys(r).length!=0){const t=r.getPlotParams();t&&r.updatePlotParams(t.startIndex_,t.endIndex_,S.value)}Reflect.ownKeys(r).length!=0&&r.getPlotTrackObjs("base")[0]&&r.deletePlotPrsObj(r.getPlotTrackObjs("base")[0].objId_),Reflect.ownKeys(r).length!=0&&r.getPlotTrackObjs("offset")[0]&&r.deletePlotPrsObj(r.getPlotTrackObjs("offset")[0].objId_)}return R({initDepthPairs:a}),he(()=>{l.registerDepthPairs({CreateDepthPair:Ve,SwitchDepthPairEditable:be}),Oe(()=>{q()})}),(t,e)=>{const d=E("el-tree-select"),o=E("el-form-item"),p=E("el-form"),C=E("el-col"),D=E("el-row"),$=E("el-table-column"),Y=E("el-input"),U=E("el-table"),z=E("el-button"),Z=E("el-option"),ce=E("el-select"),te=E("el-input-number"),ne=E("el-dialog");return L(),ee(fe,null,[n(D,null,{default:s(()=>[n(C,{span:12,style:{padding:"10px"}},{default:s(()=>[n(D,null,{default:s(()=>[n(C,{span:24},{default:s(()=>[n(p,{ref:"roleRef",model:u.value,"label-width":"100px"},{default:s(()=>[n(o,{label:"Base Curve"},{default:s(()=>[n(d,{modelValue:I.value,"onUpdate:modelValue":e[0]||(e[0]=K=>I.value=K),data:ve(W).projectTreeData,"render-after-expand":!1,props:X,onChange:e[1]||(e[1]=K=>Pe("base"))},{label:s(({label:K,value:me})=>[ae("span",ua,de(K)+": ",1),Q(" "+de(_e(ve(W).projectTreeData,me)),1)]),_:1},8,["modelValue","data"])]),_:1}),n(o,{label:"Offset Curve"},{default:s(()=>[n(d,{modelValue:M.value,"onUpdate:modelValue":e[2]||(e[2]=K=>M.value=K),data:ve(W).projectTreeData,"render-after-expand":!1,props:X,onChange:e[3]||(e[3]=K=>Pe("offset"))},{label:s(({label:K,value:me})=>[ae("span",ca,de(K)+": ",1),Q(" "+de(_e(ve(W).projectTreeData,me)),1)]),_:1},8,["modelValue","data"])]),_:1}),n(o)]),_:1},8,["model"])]),_:1})]),_:1}),n(D,null,{default:s(()=>[n(C,{span:20,style:{"padding-right":"10px"}},{default:s(()=>[n(U,{ref:"tableRef",data:c.value,style:{width:"100%"},"max-height":"600","highlight-current-row":"",onCellDblclick:y,onRowClick:A},{default:s(()=>[n($,{type:"index",width:"50"}),n($,{property:"base",label:"Base"},{default:s(K=>[K.row.isEdit==="base"?(L(),se(Y,{key:0,onBlur:me=>V(K.row),modelValue:K.row.base,"onUpdate:modelValue":me=>K.row.base=me},null,8,["onBlur","modelValue","onUpdate:modelValue"])):(L(),ee("span",fa,de(parseFloat(K.row.base).toFixed(3)),1))]),_:1}),n($,{property:"offset",label:"Offset"},{default:s(K=>[K.row.isEdit==="offset"?(L(),se(Y,{key:0,onBlur:me=>V(K.row),modelValue:K.row.offset,"onUpdate:modelValue":me=>K.row.offset=me},null,8,["onBlur","modelValue","onUpdate:modelValue"])):(L(),ee("span",pa,de(parseFloat(K.row.offset).toFixed(3)),1))]),_:1})]),_:1},8,["data"])]),_:1}),n(C,{span:4},{default:s(()=>[e[17]||(e[17]=ae("p",null,"PreDepthShift",-1)),ae("p",null,[n(Y,{modelValue:k.value,"onUpdate:modelValue":e[4]||(e[4]=K=>k.value=K),onChange:m},null,8,["modelValue"])]),e[18]||(e[18]=ae("br",null,null,-1)),ae("p",null,[n(z,{style:{width:"100%"},onClick:g},{default:s(()=>e[14]||(e[14]=[Q("Add...")])),_:1})]),ae("p",null,[n(z,{style:{width:"100%"},onClick:B},{default:s(()=>e[15]||(e[15]=[Q("Delete...")])),_:1})]),ae("p",null,[n(z,{style:{width:"100%"},onClick:x},{default:s(()=>e[16]||(e[16]=[Q("Delete All...")])),_:1})])]),_:1})]),_:1})]),_:1}),n(C,{span:12,style:{padding:"10px"}},{default:s(()=>[ae("ul",va,[ae("li",{id:"deleteDepthPair",onClick:e[5]||(e[5]=K=>ke())}," Delete Depth Pairs ")]),n(ce,{modelValue:S.value,"onUpdate:modelValue":e[6]||(e[6]=K=>S.value=K),title:"Select Depth Scale",onChange:h,style:{width:"100px"},id:"depthRatio",required:""},{default:s(()=>[n(Z,{value:"10"},{default:s(()=>e[19]||(e[19]=[Q("1:10")])),_:1}),n(Z,{value:"20"},{default:s(()=>e[20]||(e[20]=[Q("1:20")])),_:1}),n(Z,{value:"50"},{default:s(()=>e[21]||(e[21]=[Q("1:50")])),_:1}),n(Z,{value:"100"},{default:s(()=>e[22]||(e[22]=[Q("1:100")])),_:1}),n(Z,{value:"200"},{default:s(()=>e[23]||(e[23]=[Q("1:200")])),_:1}),n(Z,{value:"300"},{default:s(()=>e[24]||(e[24]=[Q("1:300")])),_:1}),n(Z,{value:"400"},{default:s(()=>e[25]||(e[25]=[Q("1:400")])),_:1}),n(Z,{value:"500"},{default:s(()=>e[26]||(e[26]=[Q("1:500")])),_:1}),n(Z,{value:"600"},{default:s(()=>e[27]||(e[27]=[Q("1:600")])),_:1}),n(Z,{value:"700"},{default:s(()=>e[28]||(e[28]=[Q("1:700")])),_:1}),n(Z,{value:"800"},{default:s(()=>e[29]||(e[29]=[Q("1:800")])),_:1}),n(Z,{value:"900"},{default:s(()=>e[30]||(e[30]=[Q("1:900")])),_:1}),n(Z,{value:"1000"},{default:s(()=>e[31]||(e[31]=[Q("1:1000")])),_:1}),n(Z,{value:"1500"},{default:s(()=>e[32]||(e[32]=[Q("1:1500")])),_:1}),n(Z,{value:"2000"},{default:s(()=>e[33]||(e[33]=[Q("1:2000")])),_:1}),n(Z,{value:"3000"},{default:s(()=>e[34]||(e[34]=[Q("1:3000")])),_:1}),n(Z,{value:"4000"},{default:s(()=>e[35]||(e[35]=[Q("1:4000")])),_:1}),n(Z,{value:"5000"},{default:s(()=>e[36]||(e[36]=[Q("1:5000")])),_:1}),n(Z,{value:"10000"},{default:s(()=>e[37]||(e[37]=[Q("1:10000")])),_:1})]),_:1},8,["modelValue"]),e[38]||(e[38]=Q("   ")),e[39]||(e[39]=ae("div",{id:"well-log-app",tabindex:"0",style:{width:"100%",height:"calc(100vh - 260px)"}},null,-1))]),_:1})]),_:1}),n(ne,{title:"Auto Search Parameter",modelValue:G.value,"onUpdate:modelValue":e[13]||(e[13]=K=>G.value=K),width:"500px","append-to-body":""},{footer:s(()=>[ae("div",ma,[n(z,{onClick:H},{default:s(()=>e[40]||(e[40]=[Q("Default")])),_:1}),n(z,{type:"primary",onClick:e[11]||(e[11]=K=>G.value=!1)},{default:s(()=>e[41]||(e[41]=[Q("OK")])),_:1}),n(z,{onClick:e[12]||(e[12]=K=>G.value=!1)},{default:s(()=>e[42]||(e[42]=[Q("Cancel")])),_:1})])]),default:s(()=>[n(p,{ref:"parameterRef",model:T.value,rules:t.parameterRules,"label-width":"125px"},{default:s(()=>[n(D,null,{default:s(()=>[n(C,{span:12},{default:s(()=>[n(o,{label:"CorrWinLength",prop:"CorrWinLength"},{default:s(()=>[n(te,{modelValue:T.value.CorrWinLength,"onUpdate:modelValue":e[7]||(e[7]=K=>T.value.CorrWinLength=K),step:1},null,8,["modelValue"])]),_:1})]),_:1}),n(C,{span:12},{default:s(()=>[n(o,{label:"Corrthreshold",prop:"Corrthreshold"},{default:s(()=>[n(te,{modelValue:T.value.Corrthreshold,"onUpdate:modelValue":e[8]||(e[8]=K=>T.value.Corrthreshold=K),step:.1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(D,null,{default:s(()=>[n(C,{span:12},{default:s(()=>[n(o,{label:"SearchLength",prop:"SearchLength"},{default:s(()=>[n(te,{modelValue:T.value.SearchLength,"onUpdate:modelValue":e[9]||(e[9]=K=>T.value.SearchLength=K),step:1},null,8,["modelValue"])]),_:1})]),_:1}),n(C,{span:12},{default:s(()=>[n(o,{label:"MaxDepthPairs",prop:"MaxDepthPairs"},{default:s(()=>[n(te,{modelValue:T.value.MaxDepthPairs,"onUpdate:modelValue":e[10]||(e[10]=K=>T.value.MaxDepthPairs=K),step:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},ga=He(ha,[["__scopeId","data-v-cc9d5746"]]);function xa(f){return ge({url:"/Preprocess/Adm/DepthCorrect",headers:{"Content-Type":"application/json"},method:"post",data:f})}const ba={style:{"font-weight":"bold"}},ya={__name:"outPut",emits:["update","done"],setup(f,{expose:R,emit:W}){const v=W,l=ze(),w=st(),u=i({}),I=i([]),M=i([]);i([]);const k=i(""),P=i(""),S=i("");i("");const G=i([]),j=i(""),T=i(!1),b=i(0);let X="";const c={children:"children",label:"name",value:"id"},h=V=>{G.value=V};function m(){let V=[];G.value.forEach(N=>{V.push(N.id)});const r=w.tableData.map(N=>({DepthSrc:Number(N.base),DepthDest:Number(N.offset)}));xa({WellboreId:X,DatasetId:k.value,ChannelIds:V,DepthPairs:r,DepthUnit:w.baseUnit}).then(N=>{N.success?ue.success("操作成功"):ue.error(N.message)})}Se(b,V=>{b.value>=100&&setTimeout(()=>{T.value=!1,v("done"),window.clearInterval(j.value)},2e3)});function g(V){I.value=[];const H=B(l.projectTreeData,V);I.value=H;const r=A(l.projectTreeData,V);r.length>=2&&(X=r[r.length-2].id)}function B(V,H){const r=[],N=_=>{_.forEach(F=>{if(F.id===H){const O=(q,le)=>{q.forEach(oe=>{r.push({...oe,dataSet:le}),oe.children&&O(oe.children,oe.name)})};F.children&&O(F.children,F.name)}else F.children&&N(F.children)})};return N(V),r}function x(){I.value=[],k.value="",P.value="",S.value="",l.projectTreeData&&(M.value=y(l.projectTreeData))}function y(V){const H=r=>JSON.parse(JSON.stringify(r));return V.map(r=>H(r)).filter(r=>r.type===2?!1:(r.children&&(r.children=y(r.children)),!0)).map(({type:r,...N})=>N)}function A(V,H){function r(_,F){for(const O of F){if(O.id===_)return[O];if(O.children&&O.children.length>0){const q=r(_,O.children);if(q.length>0)return[O,...q]}}return[]}const N=r(H,V);return N.length>0?N:[]}return he(()=>{x()}),R({}),(V,H)=>{const r=E("el-tree-select"),N=E("el-form-item"),_=E("el-form"),F=E("el-button"),O=E("el-col"),q=E("el-row"),le=E("el-table-column"),oe=E("el-progress"),re=E("el-dialog");return L(),ee(fe,null,[n(q,null,{default:s(()=>[n(O,{span:24,style:{padding:"10px"}},{default:s(()=>[n(q,null,{default:s(()=>[n(O,{span:24},{default:s(()=>[n(_,{ref:"roleRef",model:u.value,"label-width":"140px"},{default:s(()=>[n(N,{label:"Input File"},{default:s(()=>[n(r,{modelValue:k.value,"onUpdate:modelValue":H[0]||(H[0]=ie=>k.value=ie),data:M.value,"render-after-expand":!1,props:c,onChange:g},{label:s(({label:ie,value:pe})=>[ae("span",ba,de(ie)+": ",1),Q(" "+de(A(ve(l).projectTreeData,pe).map(xe=>xe.name).join(" > ")),1)]),_:1},8,["modelValue","data"])]),_:1})]),_:1},8,["model"]),n(F,{style:{float:"right"},onClick:m},{default:s(()=>H[2]||(H[2]=[Q("Start Dshift")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),n(q,null,{default:s(()=>[n(O,{span:24,style:{padding:"10px"}},{default:s(()=>[n(ve(xt),{data:I.value,onSelectionChange:h,style:{width:"100%",height:"calc(100vh - 430px)"}},{default:s(()=>[n(le,{type:"index",label:"NO",width:"50"}),n(le,{type:"selection",width:"55"}),n(le,{prop:"name",label:"Curve"}),n(le,{prop:"dataSet",label:"DateSet"})]),_:1},8,["data"])]),_:1})]),_:1}),n(re,{title:"生成过程中请稍后",modelValue:T.value,"onUpdate:modelValue":H[1]||(H[1]=ie=>T.value=ie),width:"55%","append-to-body":""},{default:s(()=>[n(oe,{"text-inside":!0,"stroke-width":20,percentage:b.value,status:"success"},null,8,["percentage"])]),_:1},8,["modelValue"])],64)}}},_a={__name:"index",emits:["update","admTabClose"],setup(f,{expose:R,emit:W}){const v=W,l=ze(),w=i(!1);function u(M){w.value=!0,Oe(()=>{outputRef.value.initProjectTreeData(),depthPairsRef.value.initDepthPairs()})}function I(){v("callChildBMethod",l.projectId),w.value=!1}return R({handleOpenAdm:u}),Fe(()=>{v("admTabClose")}),(M,k)=>{const P=E("el-tab-pane"),S=E("el-tabs");return L(),se(S,{type:"border-card"},{default:s(()=>[n(P,{label:"Depth Pairs"},{default:s(()=>[n(ga,{ref:"depthPairsRef"},null,512)]),_:1}),n(P,{label:"Output"},{default:s(()=>[n(ya,{ref:"outputRef",onDone:I},null,512)]),_:1})]),_:1})}}},Da=He(_a,[["__scopeId","data-v-3886f775"]]),Ia={class:"layout-box"},wa={class:"dock-container"},Ea=bt({__name:"index",setup(f){const R=i();let W=1;const v=i();i();let l=i();const w=i(null);i(),i({formName:"form_group",options:[{label:"name",value:"jack",group:"config",type:"input"},{label:"age",value:14,group:"config",type:"number"},{label:"sex",value:0,group:"config",type:"select",dicData:[{label:"男",value:0},{label:"女",value:1}]},{label:"color",value:"#000000",type:"color",group:"task"},{label:"date",value:"2025-04-25",type:"date",group:"config"}]}),i([]);const u=Le(da,{forwardRef:!0,useReactWrapper:!0}),I=Le(Yt,{forwardRef:!0,useReactWrapper:!0}),M=Le(Da,{forwardRef:!0,useReactWrapper:!0});Le(wt,{forwardRef:!0,useReactWrapper:!0});function k(x,y,A){const V=R.value?.__veauryReactRef__;if(!V){console.log("Layout instance not found");return}const H=W,r={id:y.id,title:Ae.createElement("div",{style:{padding:"3px 14px",cursor:"pointer"},onClick:N=>{c("curveEdit",y.id)}},A),content:Ae.createElement(u,{index:H,title:A,tabId:y.id,info:y,id:l.value,onClick:N=>{},onCurveTabClose:N=>{v.value&&v.value.__veauryVueRef__.$refs.resourceTreeRef.setCheckedKeys(N,!1)}}),closable:!0,group:"main",cached:!0};V.dockMove(r,"main","middle"),h("curveEdit"),l.value=y.id,W++}function P(x){const y=R.value?.__veauryReactRef__;if(!y){console.log("Layout instance not found");return}const A=y.find(x);return A?(y.updateTab(A.id,A),!0):!1}function S(x){const y=R.value?.__veauryReactRef__;if(!y)return;const A=y.find(x);y.dockMove(A,"","")}function G(x){v.value&&v.value.__veauryVueRef__.$refs.resourceTreeRef.setCheckNode(x)}const j=je(()=>({width:"100%",height:"calc(100vh - 40px)",background:"#1E1E1E",color:"#CCCCCC"})),T={default:{floatable:!0,maximizable:!0,tabLocked:!1,newWindow:!0,preferredFloatWidth:[200,800],preferredFloatHeight:[200,600]},main:{floatable:!0,maximizable:!0,tabLocked:!1,newWindow:!0,preferredFloatWidth:[400,1e3],preferredFloatHeight:[300,800]}},b=je(()=>({defaultLayout:X,style:j.value,groups:T,dropMode:"default",mode:"horizontal",maximizable:!0,floatable:!0,newWindow:!0,disableDock:!1,draggable:!0,resizable:!0,onLayoutChange:x=>{const y=A=>{A.children&&A.children.forEach(V=>{V.activeId&&V.group==="main"&&G(V.activeId),V.children&&y(V)})};x.dockbox&&y(x.dockbox)}})),X={dockbox:{mode:"horizontal",children:[{mode:"vertical",size:250,children:[{id:"left",group:"left",tabs:[{id:"explorer",title:"Project",content:Ae.createElement(I,{ref:x=>{v.value=x},onClick:(x,y,A)=>{!P(y.id)&&x!=="1"&&(w.value?.handleEditTabs(),k(x,y,A))},onTabClose:x=>{S(x)}}),closable:!1,cached:!0,group:"left"}]}]},{mode:"vertical",size:1e3,children:[{id:"main",group:"main",tabs:[],panelLock:{panelStyle:"main",minWidth:400,minHeight:200}}]}]}},c=(x,y)=>{y&&(l.value=y),h(x)},h=x=>{w.value&&w.value.setSelectedTab(x)},m=x=>{if(x==="admAndDshift")g();else if(x==="curveEdit"){const y=R.value?.__veauryReactRef__;if(!y){console.log("Layout instance not found");return}let A=[],V=y.getLayout().dockbox;if(V.children){for(const r of V.children)if(r.activeId&&r.group==="main")for(const N of r.tabs)N.id!=="admAndDshift"&&A.push(N.id)}const H=y.find(A[0]);y.updateTab(A[0],H)}},g=()=>{const x=R.value?.__veauryReactRef__;if(!x){console.log("Layout instance not found");return}if(x.find("wellLog"))return;const y={id:"admAndDshift",title:Ae.createElement("div",{style:{padding:"3px 14px",cursor:"pointer"},onClick:A=>{c("admAndDshift","")}},"ADM & DSHIFT"),content:Ae.createElement(M,{onClick:A=>{},onAdmTabClose:()=>{w.value?.handleRemoveTab("admAndDshift")}}),closable:!0,group:"main",cached:!0};x.dockMove(y,"main","middle")};he(()=>{});const B=yt(It);return(x,y)=>(L(),ee("div",Ia,[n(Qt,{ref_key:"tabMenuRef",ref:w,onAddTab:m,tabId:ve(l)},null,8,["tabId"]),ae("div",wa,[n(ve(B),_t({ref_key:"dockRef",ref:R},b.value),null,16)])]))}});export{Ea as default};
