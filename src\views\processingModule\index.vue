<template>
  <el-container v-loading="loading">
    <!-- 头部 -->
    <el-header class="gray" style="height: 40px;">
      <div style="display: inline-block;margin-left: 1px; padding-top: 0px;">
        <span style="position: absolute;top:10px">DHTools:</span>
        <select
          id="dhTools"
          onchange="changeDhTool()"
          class="selectStyle"
          style="position: absolute;top:5px;left:90px"
          editable="false"
        />
      </div>

      <div style="display: inline-block;margin-left: 230px; width: 10px;">
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <el-button
        size="mini"
        title="保存"
        class="icon-gs-save icon-gs-m"
        @click="saveProject()"
      />

      <div style="display: inline-block;margin-left: 10px; width: 10px;">
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <div style="display: inline-block;margin-left: 10px; width: 100px;">
        <select
          id="depthRatio"
          onchange="changeDepthRatio()"
          class="selectStyle"
          style="position: absolute;top:5px"
          editable="false"
        >
          <option value="10">1:10</option>
          <option value="20">1:20</option>
          <option value="50">1:50</option>
          <option value="100">1:100</option>
          <option value="200">1:200</option>
          <option value="300">1:300</option>
          <option value="400">1:400</option>
          <option value="500">1:500</option>
          <option value="600">1:600</option>
          <option value="700">1:700</option>
          <option value="800">1:800</option>
          <option value="900">1:900</option>
          <option value="1000">1:1000</option>
          <option value="1500">1:1500</option>
          <option value="2000">1:2000</option>
          <option value="3000">1:3000</option>
          <option value="4000">1:4000</option>
          <option value="5000">1:5000</option>
          <option value="10000">1:10000</option>
        </select>
      </div>

      <div style="display: inline-block;margin-left: 10px; width: 10px;">
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <div
        v-if="PackageId != ''"
        style="display: inline-block;margin-left: 10px; width: 20px;"
      >
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <div style="display: inline-block;margin-left: 10px; width: 10px;">
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <div style="display: inline-block;margin-right: 26px;">
        <el-button
          :disabled="btnRunChangeEnable"
          style="position: absolute;top:5px;padding: 6px 6px;"
          type="success"
          size="mini"
          title="Run"
          class="el-icon-caret-right"
          @click="RunProject()"
        />
      </div>

      <div style="display: inline-block;margin-left: 10px; width: 10px;">
        <span style="color:darkgray;position: absolute;top:8px">|</span>
      </div>

      <div
        v-show="btnRunChangeEnable"
        style="display: inline-block;margin-left: 10px; "
      >
        <span
          id="spanRunWorkflowInfo"
          style="color:rgb(81, 20, 196);position: absolute;top:8px;"
          >正在运行workflow…</span
        >
      </div>
      <div
        v-show="btnRunChangeEnable"
        style="display: inline-block;margin-left: 150px; "
      >
        <el-progress
          :percentage="progressPercent"
          style="position: absolute;top:5px;padding: 6px 6px;width: 300px;"
        ></el-progress>
      </div>
    </el-header>
    <el-container>
      <!-- 侧边栏 -->
      <el-aside
        ref="leftSide"
        :style="{
          width: leftSideWidth,
          height: leftSideHeight,
          overflow: leftSideOverflow,
        }"
      >
        <div
          :title="wellboreName"
          style="border-bottom:1px solid #CCCCCC; margin-bottom: 5px;width:100%;padding-top: 8px;padding-bottom: 8px;padding-left: 3px;font-size: 10x;font-weight: bold;color: blue;background-color: beige;"
        >
          {{ wellboreName }}
        </div>

        <div
          v-move
          :style="{ left: leftSideWidth, height: leftSideHeight }"
          class="moveBtn"
        >
          <span
            id="spanHideLeftTree"
            title="收缩"
            onclick="hideShowTargetTree(0)"
            style="height:20px;width:10px;background-color:aqua;cursor:pointer;position: absolute; top:50%;font-size: 10px;padding-top: 4px;"
            >◁</span
          >
          <span
            id="spanShowLeftTree"
            title="展开"
            onclick="hideShowTargetTree(1)"
            style="height:20px;width:10px;background-color:aqua;cursor:pointer;position: absolute; top:50%;font-size: 10px;padding-top: 4px;display: none;"
            >▷</span
          >
        </div>
      </el-aside>

      <!-- 主体内容 -->
      <el-main class="noPadding main">
        <!-- 测试按钮 -->
        <div>
          <span>Input method name: </span>
          <el-input v-model="methodName" width="200" style="width: 200px;" />
          <el-button @click="openDialog" class="testButton"
            >Test method</el-button
          >

          <!-- 测试弹框 -->
          <el-dialog
            :visible.sync="dialogVisible"
            width="30%"
            :close-on-click-modal="false"
            style="height: 100%;  margin-top: -100px;"
          >
            <template slot="title">
              <div class="method-title">
                <img :src="matchedMethod.icon" alt="method image" />
                <span>{{ methodName }}</span>
              </div>
            </template>

            <div style="width: 100%; display: inline-block;">
              <el-form
                ref="dataForm"
                :model="temp"
                :rules="rules"
                :style="{ height: methodDialogHeight, width: '100%' }"
                label-position="right"
                label-width="120px"
              >
                <div class="top">
                  <!-- 工具栏 -->
                  <el-row
                    type="flex"
                    justify="start"
                    align="middle"
                    class="toolbarHead"
                  >
                    <el-col :span="8" style="text-align: left;">
                      <el-button
                        :disabled="btnRunChangeEnable"
                        type="success"
                        size="mini"
                        title="Run"
                        class="el-icon-caret-right"
                        @click="confirmRun()"
                      />
                    </el-col>

                    <!-- 保存按钮，绝对定位到容器中间 -->
                    <!-- <el-col style="position: absolute; left: 50%; top: 50%; transform: translate(-40%, -45%);">
                      <el-button size="mini" title="保存" class="icon-gs-save icon-gs-m" @click="showSaveDialog()" />
                    </el-col> -->

                    <!-- 清除参数 -->
                    <!-- <el-col :span="8" style="text-align: right;">
                    <i class="el-icon-s-open" style="font-size: 15px; margin-right: -150px;"></i>
                  </el-col> -->
                  </el-row>
                </div>

                <!-- 中间 -->
                <methodInfo
                  ref="methodInfo"
                  :key="componentKey"
                  @dataset-changed="handleDatasetChanged"
                  @settings-changed="handleSettingsChanged"
                  :parameterData="parameter_g_List"
                  :inputList="inputList"
                  :outputList="outputList"
                  :settingsData="settingsData"
                  :datasetId="datasetId"
                  :wellboreId="wellboreId"
                ></methodInfo>
              </el-form>
            </div>

            <!-- 弹框底部 -->
            <span slot="footer" class="dialog-footer">
              <el-button @click="dialogVisible = false">{{
                $t("button.cancel")
              }}</el-button>
              <el-button type="primary" @click="submitForm">{{
                $t("button.confirm")
              }}</el-button>
            </span>
          </el-dialog>

          <!-- 运行弹框 -->
          <!-- <el-dialog title="Run" :visible.sync="dialogVisibleRun" width="30%">
          <span>您确定要计算吗？</span>
          <template #footer>
            <el-button @click="dialogVisibleRun = false">取消</el-button>
            <el-button type="primary" @click="confirmRun">确认</el-button>
          </template>
        </el-dialog> -->

          <!-- 输入曲线编辑弹框 -->
          <!-- <el-dialog title="输入曲线编辑" :visible.sync="dialogVisibleInputsEdit" width="30%">
          <el-form ref="updateForm">
            <el-form-item label="名字：">
              <el-input disabled=true v-model="updateForm.inputName" style="width: 150px;" />
            </el-form-item>

            <el-form-item label="输入值：">
              <el-input v-model="updateForm.inputValue" style="width: 280px;" />
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisibleInputsEdit = false">取消</el-button>
            <el-button type="primary" @click="submitFormInputsEdit">确定</el-button>
          </span>
        </el-dialog> -->

          <!-- 保存确认弹框 -->
          <el-dialog
            title="确认保存"
            :visible.sync="saveDialogVisible"
            width="30%"
            @close="saveDialogVisible = false"
          >
            <p>您确认要保存吗？</p>
            <span slot="footer" class="dialog-footer">
              <el-button @click="saveDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="confirmSave">确认</el-button>
            </span>
          </el-dialog>
        </div>
      </el-main>
    </el-container>

    <!-- 工作流弹框-->
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="dialogProgressVisible"
      title="Workflow method running progress"
      width="550px"
    >
      <el-form ref="dataFormProject" label-position="left" label-width="80px">
        <el-form-item>
          {{ progressTitle }}
        </el-form-item>

        <el-form-item>
          <el-progress :percentage="progressPercent" />
        </el-form-item>

        <el-form-item>
          &nbsp;
        </el-form-item>

        <el-form-item>
          <el-progress
            v-if="subProgressPercent > 0"
            :percentage="subProgressPercent"
          />
          {{ subProgressMsg }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="AbortProject()">Stop</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
// 组件
import { CoreMethod } from "@/core-method";
import methodInfo from "./component/methodInfo";
// 接口
import {
  List,
  FindByMethodName,
  Update,
  GetDataSet,
  GetChannel,
  CalculateChannel,
} from "@/api/processingModule/index";

export default {
  name: "RunMethod",
  directives: {
    move(el, bindings, vnode) {
      el.onmousedown = function(e) {
        var init = e.clientX;
        var initWidth = parseInt(vnode.context.leftSideWidth);
        document.onmousemove = function(e) {
          var end = e.clientX;
          var newWidth = end - init + initWidth;
          vnode.context.leftSideWidth = newWidth + "px";
          //  var vWidth=document.body.clientWidth-newWidth-30;
          // vnode.context.mainWidth=vWidth+'px';
          vnode.context.setScale();
          vnode.context.reFresh();
          //  parent.style.width = newWidth + "px";
        };
        document.onmouseup = function() {
          document.onmousemove = document.onmouseup = null;
        };
      };
    },
  },
  components: {
    methodInfo,
    // createProject,
    // projectTree,
    // WorkFlow,
    // ImgCanvas
  },
  data() {
    return this.initData({
      selectableInputCurves: [],
      // 级联选项数据
      cascaderOptions: [],
      saveDialogVisible: false, // 控制保存弹框的显示/隐藏
      updateForm: {},
      inputNames: "",
      inputValues: "",
      outputValue: "",
      selectedValue: "", // 默认选中值为空
      defaultSettings: {
        startIndex: "",
        endIndex: "",
        samplingInterval: "",
      },
      // 设置参数数据
      settingsData: {
        startIndex: "",
        endIndex: "",
        samplingInterval: "",
      },
      // 组件更新 key
      componentKey: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityName: undefined,
      },

      bottomDialogVisible: false,
      dialogVisible: false,
      btnRunChangeEnable: false,
      dialogVisibleRun: false,
      dialogVisibleInputsEdit: false,
      dialogVisibleParamsEdit: false,
      selectedOption: "", // 选中的下拉框值
      CurvePackageName: "",
      samplingInterval: "", //采集间隔
      loading: false, // 是否显示loading圈
      appProjectId: "", // 工程Id
      appId: "", // appId
      // tree
      treeChecked: [],
      wellboreId: "", // 井眼id
      wellboreName: "", // 井眼名称
      datasetId: "", // 选择的数据集名称
      Id: "",
      PackageId: "",
      ImageName: "",
      ImageMemo: "",
      // g_Plot: null,
      startIndex: "",
      endIndex: "",
      DepthSpacing: "QuantitativeCalculation",
      leftSideWidth: "300px",
      leftSideHeight: "900px",
      methodDialogHeight: "600px", //method弹框高度
      leftSideOverflow: "scroll",
      midWidth: "650px",
      mainWidth: "1000px",
      temp: {},
      workFlowData: {},
      inputList: [],
      outputList: [],
      allChannels: [], // DsChannels 数据
      paramList: [],
      parameter_g_List: [],
      parameter_l_List: [],
      paramZoneList: [],
      paramZone_l_List: [],
      // dhtoolsList:[],
      dialogProjectInfoVisible: false,
      channelSelect: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      showTabParameter_g: true,
      showTabParameter_l: true,
      selMethod: {},
      activeName: "in",
      searchAll: "",
      dialogStatus: "",
      // btnRunChangeEnable: false,
      progressPercent: 0,
      progressTitle: "",
      subProgressPercent: 0,
      subProgressMsg: "",
      dialogProgressVisible: false,
      checkDropNode: null,
      rules: {}, //表单校验规则
      rulesProjectInfo: {
        ImageName: [
          {
            required: true,
            message: "工程名称必填",
            trigger: "blur",
          },
        ],
      },
      boolList: ["yes", "no"],
      typeList: [
        {
          id: "simple",
          title: "simple",
        },
        {
          id: "array",
          title: "array",
        },
      ],

      depthRatioList: [
        {
          id: 10,
          title: "1:10",
        },
        {
          id: 20,
          title: "1:20",
        },
        {
          id: 50,
          title: "1:50",
        },
        {
          id: 100,
          title: "1:100",
        },
        {
          id: 200,
          title: "1:200",
        },
        {
          id: 500,
          title: "1:500",
        },
        {
          id: 1000,
          title: "1:1000",
        },
        {
          id: 2000,
          title: "1:2000",
        },
      ],
      trackTypeList: [
        {
          id: "depth",
          title: "深度",
        },
        {
          id: "normal",
          title: "普通",
        },
      ],
      trackLinearityList: [
        {
          id: 1,
          title: "线性",
        },
        {
          id: 2,
          title: "对数",
        },
      ],
      colorPalettesList: [],
      palette: [
        "#000000",
        "#7d7d7d",
        "#870014",
        "#ec1c23",
        "#ff7e26",
        "#fef100",
        "#22b14b",
        "#00a1e7",
        "#3f47cc",
        "#a349a4",
        "#ffffff",
        "#c3c3c3",
        "#b87957",
        "#feaec9",
        "#ffc80d",
        "#eee3af",
        "#b5e61d",
        "#99d9ea",
        "#7092be",
        "#c8bfe7",
      ],
      methodName: "",
      methodList: [],
      matchedMethod: {},
    });
  },
  computed: {
    //筛选出对应的那行
    filteredInputsList() {
      if (this.CurvePackageName && this.CurvePackageName.length > 0) {
        const lastCurvePackage = this.CurvePackageName[
          this.CurvePackageName.length - 1
        ];
        return this.inputList.filter(
          (item) => item.inputValue === lastCurvePackage
        );
      }
      return this.inputList;
    },
  },
  watch: {},
  created() {},
  mounted() {
    // this.setScale();
    // this.loadDHTools();
    // window.addEventListener('resize', this.setScale);
    // window.changeDepthRatio = this.changeDepthRatio;
    // window.changeDhTool = this.changeDhTool;
    // window.hideShowTargetTree = this.hideShowTargetTree;
    // // 不存在projectId则走创建工程流程
    // if (!this.Id) {
    //   this.createProject();
    // }
    // this.init()
    // this.init2();
    this.fetchData();
    console.log(this.startIndex);
  },
  methods: CoreMethod({
    async fetchData() {
      try {
        const response = await GetDataSet();
        if (response && response.data) {
          this.cascaderOptions = response.data;
        } else {
          this.$message.error("获取数据失败");
        }
      } catch (error) {
        console.error("请求失败:", error);
        this.$message.error("请求数据失败，请稍后再试");
      }
    },
    handleDatasetChanged(datasetInfo) {
      // 这里可以更新父组件的状态或执行其他操作
      this.selectableInputCurves = datasetInfo.selectableInputCurves;
      this.wellboreId = datasetInfo.wellboreId;
      this.datasetId = datasetInfo.datasetId;
      this.startIndex = datasetInfo.startIndex;
      this.endIndex = datasetInfo.endIndex;
      
      // 更新设置数据
      this.settingsData = {
        startIndex: datasetInfo.startIndex || "",
        endIndex: datasetInfo.endIndex || "",
        samplingInterval: datasetInfo.samplingInterval || ""
      };
    },
    handleSettingsChanged(newSettings) {
      this.settingsData = newSettings;
    },
    // 显示保存弹框
    showSaveDialog() {
      this.saveDialogVisible = true;
    },

    // 确认保存
    confirmSave() {
      this.saveDialogVisible = false;
      this.saveMethods();
    },

    //保存方法
    saveMethods() {
      // 扁平化分组参数
      function flattenParams(groupedList) {
        return groupedList.reduce((arr, group) => {
          if (group.children && Array.isArray(group.children)) {
            return arr.concat(group.children);
          }
          return arr;
        }, []);
      }
      const inputList = JSON.parse(JSON.stringify(this.inputList));
      const parameterList = flattenParams(this.parameter_g_List); // 扁平化参数
      const outputCurveList = JSON.parse(JSON.stringify(this.outputList));
      // 构建保存数据对象
      const saveData = {
        id: "1",
        inputs: JSON.stringify(inputList),
        parameters: JSON.stringify(parameterList), // 扁平化后的参数
        outputs: JSON.stringify(outputCurveList),
      };
      console.log(saveData);
      Update(saveData).then((resp) => {
        if (resp && resp.errorCode == 0) {
          this.init2(); // 刷新数据
          this.$message.success("保存成功");
        } else {
          this.$message.error("保存失败，请重试");
        }
      });
      // this.dialogVisibleInputsEdit = false;
      // this.dialogVisibleParamsEdit = false;
    },

    //编辑切换
    // toggleEdit(row) {
    //   // 切换当前行的 editable 状态
    //   if (row.editable === undefined) {
    //     this.$set(row, 'editable', false); // 初始化 editable 属性
    //   }
    //   row.editable = !row.editable;
    // },

    //输入曲线编辑回显
    // editBtnInputs(row) {
    //   this.dialogVisibleInputsEdit = true;
    //   this.$set(this.updateForm, "id", row.id)
    //   this.$set(this.updateForm, "inputName", row.inputName)
    //   this.$set(this.updateForm, "inputValue", row.inputValue)
    // },

    //输入曲线编辑确定
    // submitFormInputsEdit() {
    //   var list = JSON.parse(JSON.stringify(this.inputList))
    //   for (var i = 0; i < list.length; i++) {
    //     if (list[i].id == this.updateForm.id) {
    //       list[i].inputValue = this.updateForm.inputValue
    //     }
    //   }
    //   var workFlowMethod = {};
    //   workFlowMethod.id = "1"
    //   workFlowMethod.inputs = list

    //   console.log(11111)
    //   console.log(workFlowMethod)

    //   console.log(this.inputList)
    //   workFlowMethod.inputs = JSON.stringify(list)
    //   Update(workFlowMethod).then((resp) => {
    //     console.log(resp.errorCode == 0)
    //     if (resp && resp.errorCode == 0) {
    //       this.init2(); //刷新
    //       // console.log(111177777)
    //       this.$message.success('更新成功');
    //     }
    //   })
    //   this.dialogVisibleInputsEdit = false
    // },

    //参数编辑回显
    // editBtnParams(row) {
    //   this.dialogVisibleParamsEdit = true;
    //   this.$set(this.updateForm, "id", row.id)
    //   this.$set(this.updateForm, "paramsName", row.paramsName)
    //   this.$set(this.updateForm, "value", row.value)
    // },

    //参数编辑确认
    // submitFormParamsEdit() {
    //   var list = JSON.parse(JSON.stringify(this.parameter_g_List))
    //   for (var i = 0; i < list.length; i++) {
    //     if (list[i].id == this.updateForm.id) {
    //       list[i].value = this.updateForm.value
    //     }
    //   }
    //   var workFlowMethod = {};
    //   workFlowMethod.id = "1"
    //   workFlowMethod.parameters = list
    //   console.log(111111)
    //   console.log(this.parameter_g_List)
    //   workFlowMethod.parameters = JSON.stringify(list)
    //   Update(workFlowMethod).then((resp) => {
    //     console.log(999999)
    //     console.log(resp)
    //     console.log(resp.errorCode == 0)
    //     if (resp && resp.errorCode == 0) {
    //       this.init2(); //刷新
    //       this.$message.success('更新成功');
    //     }
    //   })
    //   this.dialogVisibleParamsEdit = false
    // },

    // 构造处理方法参数的公共方法
    buildProcessMethodParams(inputParams) {    
      return {
        processMethodId: this.matchedMethod.id || this.selMethod.id, // 处理方法ID
        projectId: this.$route.query.projectId, // 项目ID    "272d664e57ea4e4090ae5361270ccd42"
        wellboreId: this.wellboreId, // 井眼ID
        datasetId: this.datasetId, // 数据集ID
        inputParams: inputParams, // 输入参数，JSON字符串格式
        userId: this.$store.state.user.userId, // 用户ID
        createTime: new Date().toISOString() // 创建时间
      };
    },

    // 构造完整配置数据的公共方法
    buildCompleteConfigData() {
      const inputList = JSON.parse(JSON.stringify(this.inputList));
      
      // 确保 inputList 中的每个项目都有 displayName
      if (inputList && inputList.length > 0) {
        inputList.forEach((inputItem) => {
          if (inputItem.value && !inputItem.displayName) {
            // 如果没有 displayName，尝试从子组件获取
            const methodInfoComponent = this.$refs.methodInfo;
            if (methodInfoComponent) {
              const displayName = methodInfoComponent.getCurveNameById(inputItem.value);
              if (displayName) {
                inputItem.displayName = displayName;
              }
            }
          }
        });
      }
      
      // 处理参数列表，过滤掉空分组
      let parameterList = JSON.parse(JSON.stringify(this.parameter_g_List));
      if (parameterList && parameterList.length > 0) {
        // 如果是分组格式，过滤掉空分组
        if (parameterList[0].groupFlag) {
          parameterList = parameterList.filter(group => 
            group.children && group.children.length > 0
          );
        }
      }
      
      const outputCurveList = JSON.parse(JSON.stringify(this.outputList));
      
      const saveData = {
        inputs: JSON.stringify(inputList),
        parameters: JSON.stringify(parameterList),
        outputs: JSON.stringify(outputCurveList),
        settings: JSON.stringify(this.settingsData), // 添加设置参数
        datasetId: this.datasetId, // 添加数据集ID
        wellboreId: this.wellboreId, // 添加井眼ID
      };
      
      return JSON.stringify(saveData);
    },

    //确认运行
    // confirmRun() {
    //   // 显示进度对话框
    //   this.dialogProgressVisible = true;
    //   // 初始化进度值
    //   this.progressPercent = 0;
    //   this.subProgressPercent = 0;
    //   this.progressTitle = "Method running progress";

    //   // 输入曲线校验
    //   const { valid, err } = this.inputCurvesValidate();
    //   if (!valid) {
    //     this.$message.error(err);
    //     this.dialogProgressVisible = false; // 关闭对话框
    //     return;
    //   }

    //   const {
    //     valid: outValid,
    //     err: outErr,
    //     outputValue,
    //   } = this.outputChannelsValidate();
    //   if (!outValid) {
    //     this.$message.error(outErr);
    //     this.dialogProgressVisible = false; // 关闭对话框
    //     return;
    //   }

    //   //模拟进度条
    //   const interval = setInterval(() => {
    //     if (this.progressPercent < 50) {
    //       this.progressPercent += 10;
    //     }
    //   }, 200);     
    //   // 构造完整的配置数据
    //   const inputParams = this.buildCompleteConfigData();

    //   // 构造统一模型参数
    //   const processMethodParams = this.buildProcessMethodParams(inputParams);
      
    //   console.log(processMethodParams);

    //   //调用后端接口
    //   this.jsonRequest('/api/processModule/ProcessModule/RunMethod', processMethodParams, 'post').then((data) => {
    //     if (data.success) {
    //       this.$message.success('处理方法执行成功！');
    //       // 可以在这里处理返回结果
    //       console.log('处理方法执行结果:', data);
    //     } else {
    //       this.$message.error(data.message || '处理方法执行失败！');
    //     }
    //   }).catch((error) => {
    //     console.error('处理方法执行错误:', error);
    //     this.$message.error('处理方法执行失败，请重试！');
    //   });

    //   clearInterval(interval); // 停止模拟进度
    //   this.progressPercent = 100; // 设置为100%
    //   this.dialogProgressVisible = false; // 关闭对话框
    //   return;

    //   const channels = this.inputList.map((input) => input.value).join(",");
    //   CalculateChannel(this.wellboreId, this.datasetId, channels, outputValue)
    //     .then((res) => {
    //       clearInterval(interval); // 停止模拟进度
    //       this.progressPercent = 100; // 设置为100%
    //       const result = res.data;
    //       // 更新输出结果
    //       // this.outputList = [{
    //       //   outputName: 'Mosaic Image',
    //       //   outputValue: result.name, // 新计算的曲线名称
    //       //   outputUnit: result.unit
    //       // }];
    //       setTimeout(() => {
    //         this.dialogProgressVisible = false;
    //         this.$message.success("Calculation completed!");
    //       }, 1500);
    //     })
    //     .catch((error) => {
    //       clearInterval(interval);
    //       console.error("计算失败:", error);
    //       this.dialogProgressVisible = false;
    //       this.$message.error("Calculation failed, please try again!");
    //     });
    // },

    //确认运行
confirmRun() {
  // 输入曲线校验
  const { valid, err } = this.inputCurvesValidate();
  if (!valid) {
    this.$message.error(err);
    return;
  }

  const {
    valid: outValid,
    err: outErr,
    outputValue,
  } = this.outputChannelsValidate();
  if (!outValid) {
    this.$message.error(outErr);
    return;
  }

  // 构造完整的配置数据
  const inputParams = this.buildCompleteConfigData();

  // 构造统一模型参数
  const processMethodParams = this.buildProcessMethodParams(inputParams);

  console.log(processMethodParams);

  //调用后端接口
  this.jsonRequest('/api/processModule/ProcessModule/RunMethod', processMethodParams, 'post').then((data) => {
    if (data.success) {
      this.$message.success('调用方法成功，开始计算...');
      // 可以在这里处理返回结果
      console.log('调用方法结果:', data);
    } else {
      this.$message.error(data.message || '调用方法失败！');
    }
  }).catch((error) => {
    console.error('调用方法错误:', error);
    this.$message.error('调用方法失败，请重试！');
  });
},

    //工作流停止
    AbortProject() {
      // AbortWorkflow(this.Id)
      //   .then((res) => {
      //     this.btnRunChangeEnable = false;
      //     this.dialogProgressVisible = false;
      //     this.$message({
      //       message: res.message,
      //       type: 'success', // 消息类型，可以是 success、warning、info、error 中的一种
      //       duration: 4000, // 消息显示的持续时间，默认为 4000 毫秒
      //     });
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   });
    },

    //运行方法按钮
    methodRun() {
      this.dialogVisibleRun = true;
    },

    //测试按钮
    async openDialog() {
      // 清除之前的数据
      this.clearPreviousData();
      
      if (this.methodName && this.methodName.trim() !== "") {
        const { success, data: matchedMethods } = await FindByMethodName(
          this.methodName
        );

        if (success) {
          if (matchedMethods && matchedMethods.length > 0) {
            this.matchedMethod = matchedMethods[0];

            // 1. 解析 matchedMethod 的默认配置
            const defaultInputs = JSON.parse(this.matchedMethod.inputs || '[]');
            const defaultParams = JSON.parse(this.matchedMethod.parameters || '[]');
            const defaultOutputs = JSON.parse(this.matchedMethod.outputs || '[]');

            // 2. 获取保存的配置
            let savedInputs = [];
            let savedParams = [];
            let savedOutputs = [];
            let savedSettings = null;
            let savedDatasetId = null;
            let savedWellboreId = null;

            try {
              const response = await this.request('/api/processModule/ProcessModule/GetMethodParams', { processMethodId: this.matchedMethod.id },'post');
              if (response.success && response.data) {
                const savedConfig = JSON.parse(response.data);
                if (savedConfig.inputs) savedInputs = JSON.parse(savedConfig.inputs);
                if (savedConfig.parameters) savedParams = JSON.parse(savedConfig.parameters);
                if (savedConfig.outputs) savedOutputs = JSON.parse(savedConfig.outputs);
                if (savedConfig.settings) savedSettings = JSON.parse(savedConfig.settings);
                if (savedConfig.datasetId) savedDatasetId = savedConfig.datasetId;
                if (savedConfig.wellboreId) savedWellboreId = savedConfig.wellboreId;
              }
            } catch (error) {
              // 获取失败，忽略，使用默认值
            }

            // 3. 合并逻辑（value 优先级：saved.value > def.defValue > ""）
            function mergeByName(defaultArr, savedArr) {
              return defaultArr.map(def => {
                const saved = savedArr.find(item => item.name === def.name);
                return {
                  ...def,
                  value: saved && saved.value !== undefined ? saved.value
                        : (def.defValue !== undefined ? def.defValue : "")
                };
              });
            }

            // 递归合并分组参数
            function mergeParams(defaultArr, savedArr) {
              return defaultArr.map(def => {
                if (def.groupFlag && Array.isArray(def.children)) {
                  // 分组
                  const savedGroup = savedArr.find(item => item.groupFlag && item.name === def.name);
                  return {
                    ...def,
                    children: mergeParams(def.children, (savedGroup && savedGroup.children) ? savedGroup.children : [])
                  };
                } else {
                  // 普通参数
                  const saved = savedArr.find(item => item.name === def.name);
                  return {
                    ...def,
                    value: saved && saved.value !== undefined ? saved.value
                          : (def.defValue !== undefined ? def.defValue : "")
                  };
                }
              });
            }

            this.inputList = mergeByName(defaultInputs, savedInputs);
            this.outputList = mergeByName(defaultOutputs, savedOutputs);
            this.parameter_g_List = mergeParams(defaultParams, savedParams);
            this.settingsData = savedSettings || { startIndex: '', endIndex: '', samplingInterval: '' };
            this.datasetId = savedDatasetId || '';
            this.wellboreId = savedWellboreId || '';

            // 按组分类参数信息（保持原有分组逻辑）
            const parameters = this.parameter_g_List;
            if (parameters && parameters.length > 0) {
              const isAlreadyGrouped = parameters[0] && parameters[0].groupFlag;
              if (!isAlreadyGrouped) {
                const groupMap = new Map();
                parameters.forEach((parameter) => {
                  const groupName = parameter.dataGroup;
                  if (!groupMap.has(groupName)) {
                    groupMap.set(groupName, []);
                  }
                  groupMap.get(groupName).push(parameter);
                });
                const groupedParameters = [];
                groupMap.forEach((value, key) => {
                  if (value && value.length > 0) {
                    groupedParameters.push({
                      name: key,
                      groupFlag: true,
                      children: value,
                    });
                  }
                });
                this.parameter_g_List = groupedParameters;
              } else {
                this.parameter_g_List = parameters.filter(group => group.children && group.children.length > 0);
              }
            } else {
              this.parameter_g_List = [];
            }

            // 强制更新视图
            this.$nextTick(() => {
              this.componentKey++;
            });

            this.dialogVisible = true;
          } else {
            this.$message.error("未找到方法");
          }
        }
      } else {
        this.$message.error("请输入方法名，并且方法名不能为空字符");
      }
    },

    // 清除之前的数据
    clearPreviousData() {
      console.log("清除之前的数据");
      
      // 清除数据集相关数据
      this.datasetId = "";
      this.wellboreId = "";
      this.settingsData = {
        startIndex: "",
        endIndex: "",
        samplingInterval: ""
      };
      
      // 清除输入、参数、输出列表
      this.inputList = [];
      this.parameter_g_List = [];
      this.outputList = [];
      
      // 重置组件key，强制重新渲染
      this.componentKey = 0;
      
      // 调用子组件的清除方法
      this.$nextTick(() => {
        if (this.$refs.methodInfo) {
          this.$refs.methodInfo.clearComponentData();
        }
      });
      
      console.log("数据已清除:", {
        datasetId: this.datasetId,
        wellboreId: this.wellboreId,
        settingsData: this.settingsData,
        inputList: this.inputList,
        parameter_g_List: this.parameter_g_List,
        outputList: this.outputList,
        componentKey: this.componentKey
      });
    },
    //提交
    submitForm() {
      // 扁平化分组参数
      function flattenParams(groupedList) {
        return groupedList.reduce((arr, group) => {
          if (group.children && Array.isArray(group.children)) {
            return arr.concat(group.children);
          }
          return arr;
        }, []);
      }
      // 构造完整的配置数据
      const inputList = JSON.parse(JSON.stringify(this.inputList));
      const parameterList = flattenParams(this.parameter_g_List); // 扁平化参数
      const outputCurveList = JSON.parse(JSON.stringify(this.outputList));
      const saveData = {
        inputs: JSON.stringify(inputList),
        parameters: JSON.stringify(parameterList),
        outputs: JSON.stringify(outputCurveList),
        settings: JSON.stringify(this.settingsData), // 添加设置参数
        datasetId: this.datasetId, // 添加数据集ID
        wellboreId: this.wellboreId, // 添加井眼ID
      };
      console.log("inputParams 格式数据", saveData);
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 构造统一模型参数
          const processMethodParams = this.buildProcessMethodParams(JSON.stringify(saveData));
          // 调用后端接口保存配置
          this.jsonRequest('/api/processModule/ProcessModule/SaveMethodParams', processMethodParams, 'post').then((data) => {
            if (data.success) {
              this.$message.success('配置保存成功！');
              console.log('配置保存结果:', data);
            } else {
              this.$message.error(data.message || '配置保存失败！');
            }
          }).catch((error) => {
            console.error('配置保存错误:', error);
            this.$message.error('配置保存失败，请重试！');
          });
          this.dialogVisible = false;
        } else {
          this.$message.error("表单验证失败");
        }
      });
    },

    // 初始化
    init() {
      // 获取地址参数
      this.appProjectId = this.$route.query.projectId || this.$route.query.id || "";
      this.appId = this.$route.query.appId;
    },

    // 初始化
    init2() {
      List(this.Id).then((res) => {
        if (res.data) {
          this.methodList = res.data;
        }

        this.inputList = JSON.parse(res.data[0].inputs);
        this.parameter_g_List = JSON.parse(res.data[0].parameters);
        this.outputList = JSON.parse(res.data[0].outputs);
      });
    },

    // 保存文件
    saveProject() {
      var data = this.CreateProjectModel();
      var sData = JSON.stringify(data);
      UpdateCreateImageProjectInfo(this.Id, this.appId, sData).then((res) => {
        this.$notify({
          title: "提示信息",
          message: res.message,
          type: "success",
          duration: 2000,
        });
      });
    },

    changeDepthRatio() {
      var vDepthRatio = document.getElementById("depthRatio").value;
      this.$refs["canvasRef"].changeDepthRatio(vDepthRatio);
    },

    changeDhTool() {
      // debugger;
      var vDhTool = document.getElementById('dhTools').value;
      GetDHToolData(this.Id, vDhTool).then(res => {
        var data = res.data;
        this.startIndex = data.startIndex;
        this.endIndex = data.endIndex;
        this.inputList = data.inputList;
        this.outputList = data.outputList;
        this.paramList = data.paramList;
        this.paramZoneList = data.paramZoneList;
      });
    },

    RunProject() {
      // spanRunWorkflowInfo
      // this.progressPercent = 0;
      // this.subProgressPercent = 0;
      // this.btnRunChangeEnable = true;
      // this.dialogProgressVisible = true;
      // this.progressTitle = 'Prepare Input';
      // this.progressPercent = 1;
      // this.$refs['flowRef'].processNode('state_start_-1');
      // const requestDynamicData = (connId) => {
      //   StartWorkflow(this.Id, connId)
      //     .then((res) => {
      //       this.progressPercent = 100;
      //       if (res.message != 'IsAbort') {
      //         // var vPackId=res.data;
      //         this.PackageId = res.data;
      //         this.$refs['canvasRef'].loadData(this.Id, this.PackageId);
      //         this.$message({
      //           message: res.message,
      //           type: 'success', // 消息类型，可以是 success、warning、info、error 中的一种
      //           duration: 4000, // 消息显示的持续时间，默认为 4000 毫秒
      //         });
      //       }
      //       this.progressPercent = 0;
      //       this.subProgressPercent = 0;
      //       this.btnRunChangeEnable = false;
      //       this.dialogProgressVisible = false;
      //     })
      //     .catch((err) => {
      //       console.log(err);
      //     });
      // };
      // const handleConnSuccess = (connId) => {
      //   requestDynamicData(connId);
      // };
      // const handleMessageReceived = (message) => {
      //   console.log('SignalR接收到数据：', message);
      //   var arrMsg = message.split(',');
      //   if (arrMsg.length >= 2 && arrMsg[1] != '') {
      //     this.$refs['flowRef'].processNode(arrMsg[1])
      //   }
      //   if (arrMsg.length >= 4) {
      //     if (arrMsg[3] == '1') {
      //       this.progressPercent = parseFloat(arrMsg[0]);
      //       this.subProgressPercent = 0;
      //       this.progressTitle = arrMsg[2];
      //     } else {
      //       this.subProgressPercent = parseFloat(arrMsg[0]);
      //       this.subProgressMsg = arrMsg[2];
      //     }
      //   }
      // };
      // signalrService.startConnection(handleConnSuccess);
      // signalrService.registerReceiveMessageHandler(handleMessageReceived);
    },

    //重置
    reset() {
      this.startIndex = this.defaultSettings.startIndex;
      this.endIndex = this.defaultSettings.endIndex;
      this.samplingInterval = this.defaultSettings.samplingInterval;
    },

    InitToolTip() {
      var tempDiv = document.createElement("div");
      document.body.insertBefore(tempDiv, document.body.childNodes[0]);
      tempDiv.id = "tipPanel";
      tempDiv.style.display = "none";
      tempDiv.style.position = "absolute";
      tempDiv.style.zIndex = "999";
      tempDiv.style.border = "solid 2px #09c";
      tempDiv.style.padding = "6px 8px";
      tempDiv.style.fontSize = "14px";
      tempDiv.style.color = "#555";
      tempDiv.style.minWidth = "300px";

      tempDiv.addEventListener("mouseover", this.hideTipPanel(), false);
    },

    HideTip: function() {
      if (document.getElementById("tipPanel") == null) {
        return;
      }
      document.getElementById("tipPanel").style.display = "none";
      document.getElementById("divToolTip").style.display = "none";
    },

    hideTipPanel() {
      document.getElementById("tipPanel").style.display = "none";
    },

    // 创建新工程
    createProject() {
      this.$confirm("未找到工程，是否创建新工程？", "提示", {
        type: "info",
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        closeOnHashChange: false,
        center: true,
        beforeClose: (action, instance, done) => {
          if (action == "cancel") {
            history.back();
          }
          if (action == "confirm") {
            this.$refs.createPro.show();
          }
          this.$nextTick((_) => {
            done();
          });
        },
      }).catch(() => {});
    },
    // 取消创建
    onCancelCreate() {
      this.$refs.createPro.close().resetForm();
    },
    // 创建完成
    // onCreateSuccess(data) {
    //   this.$message.success('工程创建成功')
    //   this.$refs.createPro.close().resetForm()

    //   this.$router.replace({
    //     path: 'createImage',
    //     query: {
    //       id: data.appProjectId,
    //       wellboreId: data.wellboreId,
    //       appId: this.appId
    //     }
    //   })
    //   this.$router.go(0);
    // },

    //侧边栏
    hideShowTargetTree(vType) {
      if (vType == 0) {
        this.leftSideWidth = "0px";
        document.getElementById("spanHideLeftTree").style.display = "none";
        document.getElementById("spanShowLeftTree").style.display = "block";
      } else {
        this.leftSideWidth = "300px";
        document.getElementById("spanHideLeftTree").style.display = "block";
        document.getElementById("spanShowLeftTree").style.display = "none";
      }

      var leftWidth = parseInt(this.leftSideWidth);
      var vWidth = window.screen.width - leftWidth - vMidWidth - 28;
      thi.$refs["canvasRef"].mainWidth = vWidth + "px";
      document.getElementById("well-log-app").style.width = this.mainWidth;
      this.setScale();
      this.reFresh();
    },

    deleteParameter(id) {
      this.temp.parameterList = this.temp.parameterList.filter(
        (da) => da.uuid !== id
      );
    },

    selNode(node) {
      this.selMethod = node;
      this.parameter_g_List = this.paramList.filter(
        (t) =>
          t.method == node.moduleId && t.zoned == "no" && t.isHidden == "no"
      );
      this.parameter_l_List = this.paramList.filter(
        (t) =>
          t.method == node.moduleId && t.zoned == "yes" && t.isHidden == "no"
      );
      this.paramZone_l_List = this.paramZoneList.filter(
        (t) => t.method == node.moduleId
      );

      if (this.parameter_g_List && this.parameter_g_List.length > 0) {
        this.showTabParameter_g = true;
        for (var i = 0; i < this.parameter_g_List.length; i++) {
          if (this.parameter_g_List[i].valueList != "") {
            this.parameter_g_List[i].defValue = parseInt(
              this.parameter_g_List[i].defValue
            );
          }
        }
      } else {
        this.showTabParameter_g = false;
      }

      if (this.parameter_l_List && this.parameter_l_List.length > 0) {
        this.showTabParameter_l = true;

        for (var i = 0; i < this.parameter_l_List.length; i++) {
          if (this.parameter_l_List[i].valueList != "") {
            this.parameter_l_List[i].defValue = parseInt(
              this.parameter_l_List[i].defValue
            );
          }
        }
      } else {
        this.showTabParameter_l = false;
      }
    },

    handleDeleteZone(row, index) {
      for (var i = 0; i < this.paramZoneList.length; i++) {
        if (this.paramZoneList[i].id == row.id) {
          this.paramZoneList.splice(i, 1);
          break;
        }
      }
      this.paramZone_l_List = this.paramZoneList.filter(
        (t) => t.method == this.selMethod.moduleId
      );
    },

    // 调接口获取井眼列表
    fetchChannelSelect() {
      this.loading = true;
      GetLogPlotChartTree(this.wellboreId)
        .then((res) => {
          res.data.forEach((log) => {
            const item1 = {
              label: log.label,
              value: log.id,
              children: [],
            };
            log.children.forEach((channel) => {
              const item2 = {
                label: channel.channelName,
                value: channel.id,
                // children: []
              };

              item1.children.push(item2);
            });
            if (item1.children.length) {
              this.channelSelect.push(item1);
            }
          });

          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    guid24() {
      return (
        this.S4() + this.S4() + this.S4() + this.S4() + this.S4() + this.S4()
      );
    },

    S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    },

    groupSpanMethod({ row, columnIndex }) {
      if (row.groupFlag) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },

    // 加载默认值
    applyDefValue(method) {
      console.log("applyDefValue 方法参数:", method);
      
      const arr = [
        JSON.parse(method.inputs),
        JSON.parse(method.outputs),
        JSON.parse(method.parameters),
      ];

      console.log("解析的默认配置:", arr);

      arr.forEach((list, index) => {
        if (list && list.length > 0) {
          list.forEach((item) => {
            item.value = item.defValue;
          });
        }
      });

      this.inputList = arr[0];
      this.outputList = arr[1];
      this.parameter_g_List = arr[2];
      
      console.log("设置默认值后:", {
        inputList: this.inputList,
        parameter_g_List: this.parameter_g_List,
        outputList: this.outputList
      });
    },

    // 将逗号分割的字符串转为字符串数组
    stringToArray(content) {
      if (content && content.length > 0) {
        return content.split(",");
      } else {
        return [];
      }
    },

    // 输入曲线是否全部配置完成校验
    inputCurvesValidate() {
      let valid = true;
      let err = "";
      if (this.selectableInputCurves.length === 0) {
        valid = false;
        err = "You need to select a dataset before running.";
        return { valid, err };
      }

      if (!this.inputList || this.inputList.length === 0) {
        valid = false;
        err =
          "Configuration with input channels is required to run calculations.";
        return { valid, err };
      }

      for (const input of this.inputList) {
        console.log("input curve", input);
        console.log("selectableInputCurves", this.selectableInputCurves);
        if (!this.selectableInputCurves.some((c) => c.value === input.value)) {
          valid = false;
          err = `The channel in the dataset needs to be specified for the input channel "${
            input.name
          }".`;
          break;
        }
      }

      return { valid, err };
    },

    outputChannelsValidate() {
      let valid = true;
      let err = "";

      if (!this.outputList || this.outputList.length === 0) {
        valid = false;
        err =
          "Configuration with output channels is required to run calculations.";
        return { valid, err };
      }

      const outputValue =
        this.outputList.length > 0 && this.outputList[0].value
          ? this.outputList[0].value
          : ""; // 默认获取用户输入的第一个output channel名称

      if (!outputValue) {
        valid = false;
        err = "Please input the name of the output channel!";
      }

      return { valid, err, outputValue };
    },
  }),
};
</script>

<style scoped lang="scss">
@import "/../../../static/Content/icon.css";

div {
  .cell {
    height: 30px;
  }
}

.gray {
  background: #fafafa;
  height: 32px;
  padding-top: 5px;
  border: 1px black solid;
}

.noPadding {
  padding: 0 0 0 10px;

  .el-table {
    padding: 0;
  }
}

.main {
  .method-title {
    display: flex;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 5px;
    }
  }

  .settings-container {
    margin-top: 20px;
  }
}

#divToolTip {
  position: absolute;
  z-index: 0;
  width: 400px;
  height: 1px;
  border-bottom: dashed 1px #0000ff;
  font-size: 14px;
  color: #2b65ec;
  margin: 0;
  padding: 0;
  top: 100px;
  left: 800px;
}

.clickRightMenu {
  width: 110px;
  margin-top: 0px;
  background-color: #fff;
  font-size: 12px;
  position: absolute;
  text-align: left;
  padding: 2px 0;
  border: 1px solid #ccc;
  display: none;
  z-index: 100;
}

.tree-title {
  padding-left: 4px;
  color: #444;
  line-height: 16px;
}

.clickRightMenu li {
  list-style: none;
  line-height: 20px;
  padding-left: 20px;
}

.clickRightMenu li:hover {
  background-color: #dcdcdc;
  cursor: pointer;
}

.moveBtn {
  height: 100%;
  width: 6px;
  /* opacity: 0; */
  position: absolute;
  left: 300px;
  top: 42px;
  cursor: col-resize;
  background-color: rgb(173, 173, 166);
  display: table-cell;
}

.icon-gs-m {
  border: none;
  padding: 0px;
  height: 24px !important;
  width: 24px !important;
}

.selectStyle {
  border: 1px solid gray;
  background-color: #fff;
  color: #666;
  height: 28px;
  line-height: 28px;
  padding: 2px 2px;
  font-size: 15px;
  /* font-weight:900; */
  /* font-family:MicrosoftYaHei; */
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}

.control-bar {
  display: flex;
  justify-content: center;
  /* 居中对齐 */
  align-items: center;
  background-color: #d9e8f5;
  /* 背景颜色 */
  border: 1px solid #b4c8dc;
  /* 边框颜色 */
  padding: 5px;
  /* 内间距调整 */
}

.control-button {
  margin: 0 5px;
  /* 按钮之间的间距 */
  color: #42a5f5;
  /* 按钮颜色 */
  border: none;
  /* 按钮无边框 */
  background-color: transparent;
  /* 按钮背景透明 */

  font-size: large;
}

/* .top {
    background-color: #d9e8f5;
  }
 */

.middle {
  .el-input-number .el-input__inner {
    text-align: left;
  }
}

.toolbarHead {
  background-color: #edf0f7;
  height: 32px;
  padding: 8px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.testButton {
  font-size: 15px;
}

.el-icon-caret-right {
  position: absolute;
  top: 3px;
  padding: 6px 6px;
}
</style>
